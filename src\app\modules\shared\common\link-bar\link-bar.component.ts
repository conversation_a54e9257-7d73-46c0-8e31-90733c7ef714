import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, HostBinding, inject, Input, Output } from '@angular/core';

import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { TextTransformPipe } from '../../pipe/text-transform.pipe';
import { IconComponent } from 'src/app/component/icon/icon.component';
import { MenuItem } from 'src/app/entities/form-field';
import { DocumentDataQuery } from 'src/app/modules/container/document/state';

@Component({
  selector: 'app-link-bar',
  templateUrl: './link-bar.component.html',
  styleUrls: ['./link-bar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [NgIf, NgFor, IconComponent, TextTransformPipe, AsyncPipe],
})
export class LinkBarComponent {
  @HostBinding('class') class = 'flex overflow-auto scrollbar-none';
  private readonly documentDataQuery = inject(DocumentDataQuery);

  @Input() model: MenuItem[] = [];
  @Input() moduleId: string;

  @Output() linkBarItem = new EventEmitter<MenuItem>();

  labelTransform(item: MenuItem) {
    return item.id === 'followDoc' || item.id === 'unfollowDoc' ? null : item.label;
  }

  formatTransform$(format: string): Observable<string> {
    return this.documentDataQuery
      .select()
      .pipe(map((data) => format.replace(/\{(\w+)\}/g, (_, key) => data[key] ?? '')));
  }
}
