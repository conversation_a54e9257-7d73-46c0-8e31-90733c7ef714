import { Dialog } from '@angular/cdk/dialog';
import { Injectable, inject } from '@angular/core';

import { untilDestroyed } from '@ngneat/until-destroy';
import { RowNode } from 'ag-grid-community';
import { Observable, of } from 'rxjs';
import { switchMap, take } from 'rxjs/operators';

import { DocumentDataQuery, DocumentDataService } from '../../../state';
import { AbstractGroupRowUIAction } from '../model/abstract-group-row-ui-action';
import { generalAlertDialogConfig } from 'src/app/config/dialog';
import { AlertDialogComponent } from 'src/app/modules/shared/common/warning-dialog/alert-dialog.component';
import { AuthService } from 'src/app/services/auth.service';

@Injectable()
export class GroupRowDeleteAction extends AbstractGroupRowUIAction {
  groupRowSelectedRows: any[] = [];

  readonly documentDataQuery = inject(DocumentDataQuery);
  readonly authService = inject(AuthService);

  constructor(readonly documentDataService: DocumentDataService, private readonly dialog: Dialog) {
    super();
  }

  isVendorDomain = this.authService.state.userType === 'vendor';

  validate(): Observable<boolean> {
    const { allLeafChildren } = this.node;
    let isSelectedAny = false;
    allLeafChildren.forEach((children) => {
      const data = children?.data;
      if (children.isSelected()) {
        this.groupRowSelectedRows.push(data);
        isSelectedAny = true;
      }
    });

    this.node?.childrenAfterSort.forEach((child) => {
      if (child?.childrenAfterSort && child?.isSelected()) {
        isSelectedAny = false;
      }
    });

    if (!isSelectedAny) {
      const { popupMeta } = this.actionParams;
      const openDialogMeta = {
        type: popupMeta.type,
        buttonType: popupMeta.buttonType,
        message: popupMeta.message,
      };
      return this.openWarningDialog(openDialogMeta.buttonType, openDialogMeta.message).closed.pipe(
        take(1),
        switchMap(({ payload }) => (payload ? of(true) : of(false))),
        untilDestroyed(this),
      );
    }

    if (this.sectionId.startsWith('vpoChainOfCustodyList')) {
      return this.handleVpoCocDetailsDel(this.groupRowSelectedRows);
    }

    return of(true);

    // return of(!!this.groupRowSelectedRows.length);
  }

  process() {
    const selectedRows = this.groupRowSelectedRows.reduce((acc, row) => ({ ...acc, [row.id]: true }), {});
    let newRowData = this.documentDataQuery.getValue()[this.sectionId];
    // const topParent = this.recusiveToTopParent(this.node);
    // const allLeafChildren = topParent.allLeafChildren;
    // allLeafChildren.forEach((rowNode) => {
    //   newRowData.push({ ...rowNode.data });
    // });

    if (this.sectionId.startsWith('factoryAuditRequirements') || this.sectionId.startsWith('factoryAuditChecklists')) {
      newRowData = this.handleFactoryAuditRequirementsDel(newRowData, selectedRows);
    }
    const rowData = newRowData.filter((row) => !selectedRows[row.id]);
    this.documentDataService.updateData({ [this.sectionId]: rowData });
    this.groupRowSelectedRows = [];
    return of(true);
  }

  postProcess() {
    super.postProcess();
    // setTimeout(() => this.gridSectionService.gridApi.refreshClientSideRowModel(), 100);
  }

  recusiveToTopParent(rowNode: RowNode) {
    if (rowNode.level === -1) {
      return rowNode;
    }
    return this.recusiveToTopParent(rowNode.parent);
  }

  handleFactoryAuditRequirementsDel(rowData: any[], selectRows: any) {
    let isSelectedAny = false;
    let newRowData = JSON.parse(JSON.stringify(rowData));
    const { groupRowData } = this;
    rowData.forEach((singleRowData) => {
      if (!isSelectedAny) {
        isSelectedAny = selectRows[singleRowData.id] === true;
      }
    });
    if (!isSelectedAny) {
      groupRowData.forEach((singleRow) => {
        newRowData = newRowData.filter((row) => singleRow.sectionSeqNo !== row.sectionSeqNo);
      });
    }

    return newRowData;
  }

  private openWarningDialog(messageButtons: string, message: string) {
    return this.dialog.open<any>(AlertDialogComponent, {
      ...generalAlertDialogConfig,
      data: {
        messageButtons,
        message,
        title: 'warning',
      },
    });
  }

  handleVpoCocDetailsDel(groupRowSelectedRows: any[]): Observable<boolean> {
    if (groupRowSelectedRows.findIndex((row) => row?.isFromTemplate === true) >= 0 && this.isVendorDomain) {
      const openDialogMeta = {
        type: 'warning',
        buttonType: 'Okay',
        message: 'Please do not delete predefined rows from template.',
      };
      this.openWarningDialog(openDialogMeta.buttonType, openDialogMeta.message).closed.pipe(
        take(1),
        switchMap(({ payload }) => (payload ? of(true) : of(false))),
        untilDestroyed(this),
      );
      return of(false);
    }
    return of(true);
  }
}
