import { Dialog } from '@angular/cdk/dialog';
import { <PERSON><PERSON><PERSON>, <PERSON>I<PERSON> } from '@angular/common';
import { HttpResponse } from '@angular/common/http';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Inject,
  Input,
  Output,
  ViewContainerRef,
} from '@angular/core';

import { <PERSON><PERSON><PERSON>roy } from '@ngneat/until-destroy';
import { filter } from 'rxjs';

import { IconComponent } from '../../../../component/icon/icon.component';
import { WINDOW } from '../../../../services/window.service';
import { BaseImageComponent } from '../../../shared/common/base-component/base-image/base-image.component';
import { UploadTaskComponent } from '../../../shared/common/upload-task/upload-task.component';
import { DateTimeFormatPipe } from '../../../shared/pipe/date-time-format.pipe';
import { SignatureInfoPopupComponent } from '../cbx-module/shareFile/signature-info-popup';
import { ImageLeafletGalleryViewDialogComponent } from '../dynamic-form-field/view-field';
import { DocumentDataQuery, DocumentDefineStore } from '../state';
import { defaultDialogConfig } from 'src/app/config/dialog';
import { defaultImageGalleryDialogConfig } from 'src/app/config/image-gallery';
import { UploadType } from 'src/app/entities/upload-type';
import {
  ImageGalleryViewDialogData,
  ItemImage,
  PdfViewerDialogData,
  SignatureInfoPopupData,
  SignatureInfoPopupResult,
} from 'src/app/interface/model';
import { FileInfo } from 'src/app/interface/model/file-info';
import { DocumentService } from 'src/app/modules/services/document.service';
import { PdfViewerDialogComponent } from 'src/app/modules/shared/common/pdf-viewer-dialog/pdf-viewer-dialog.component';
import { NotificationService } from 'src/app/services/notification.service';

@UntilDestroy()
@Component({
  selector: 'app-attachment-section-grid-mode',
  templateUrl: './attachment-section-grid-mode.component.html',
  styleUrls: ['./attachment-section-grid-mode.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [NgIf, NgFor, BaseImageComponent, IconComponent, DateTimeFormatPipe, UploadTaskComponent],
})
export class AttachmentSectionGridModeComponent {
  vcr = inject(ViewContainerRef, { skipSelf: true });

  @Input() attachmentList: FileInfo[] = [];
  @Input() edit: boolean;

  @Output() uploadSucceed = new EventEmitter<FileInfo>();
  @Output() uploadFailed = new EventEmitter<FileInfo>();
  @Output() removeFile = new EventEmitter<FileInfo>();

  doNotNeedFileIcon = {
    jpg: true,
    jpeg: true,
    png: true,
    gif: true,
    tif: true,
  };

  constructor(
    @Inject(WINDOW)
    private readonly window: Window,
    private readonly dialog: Dialog,
    private readonly documentDataQuery: DocumentDataQuery,
    private readonly documentService: DocumentService,
    private readonly notificationService: NotificationService,
    private readonly documentDefineStore: DocumentDefineStore,
  ) {}

  onClick(attachment: FileInfo) {
    if (
      attachment.fileExtension === 'jpg' ||
      attachment.fileExtension === 'jpeg' ||
      attachment.fileExtension === 'png'
    ) {
      const images = [];
      let activeIndex = 0;
      this.attachmentList
        .filter(
          (attach) =>
            attach.fileExtension === 'jpg' || attach.fileExtension === 'jpeg' || attach.fileExtension === 'png',
        )
        .forEach((attach: any, index: number) => {
          const file = attach.fileId
            ? attach.fileId
            : {
                id: attach.file.id,
                original: attach.file,
              };

          images.push({
            file,
          });
          if (attach.id === attachment.id) {
            activeIndex = index;
          }
        });

      this.openDialog(images, activeIndex);
    } else if (attachment.fileExtension === 'pdf') {
      this.openPdfViewerDialog(attachment);
    } else {
      this.window.open(attachment.url as string, '_blank');
    }
  }

  openDialog(images: ItemImage[], activeIndex: number) {
    this.dialog.open<any, ImageGalleryViewDialogData, ImageLeafletGalleryViewDialogComponent>(
      ImageLeafletGalleryViewDialogComponent,
      {
        ...defaultImageGalleryDialogConfig,
        data: {
          images,
          activeIndex,
          multipleMode: true,
        },
        viewContainerRef: this.vcr,
      },
    );
  }

  openPdfViewerDialog(attachment: FileInfo) {
    this.dialog.open<any, PdfViewerDialogData, PdfViewerDialogComponent>(PdfViewerDialogComponent, {
      ...defaultImageGalleryDialogConfig,
      data: {
        attachment,
      },
      viewContainerRef: this.vcr,
    });
  }

  handleUploadSucceed(attachment: FileInfo, response: HttpResponse<any>, index: number) {
    const { id, fileName, fileSize, url } = response.body;
    const succeedAttachment = {
      ...attachment,
      id,
      file: {
        fileName,
        id,
        url,
        fileSize,
      },
      needUpload: false,
      index,
    };
    this.uploadSucceed.emit(succeedAttachment);
  }

  handleUploadFailed(attachment: FileInfo, index: number) {
    const failedAttachment = { ...attachment, needUpload: false, uploadFailed: true, index };
    this.uploadFailed.emit(failedAttachment);
  }

  deleteImage(attachment: FileInfo, index: number, $event: any) {
    this.removeFile.emit({ ...attachment, index });
    $event.stopPropagation();
    $event.preventDefault();
  }

  checkIsFile(uploadType: UploadType): boolean {
    return uploadType === UploadType.FILE;
  }

  isShowPdfSigned(attachment: any) {
    return attachment?.signStatus === 'Signed';
  }

  openSignatureInfo(attachment: any) {
    const moduleId = 'shareFile';
    const formData = this.documentDataQuery.getValue();
    const { id } = formData;
    const requestBody = {
      agreementId: attachment?.agreementId,
      shareFileId: id,
    };
    this.documentDefineStore.setLoading(true);
    this.handleSignatureInfo(requestBody, moduleId);
  }

  handleSignatureInfo(requestBody: any, moduleId: string) {
    this.documentService.getSignatureInfo(requestBody, moduleId).subscribe({
      next: (data) => {
        this.documentDefineStore.setLoading(false);
        const responseResult$ = data;
        this.dialog
          .open<SignatureInfoPopupResult, SignatureInfoPopupData, SignatureInfoPopupComponent>(
            SignatureInfoPopupComponent,
            {
              ...defaultDialogConfig,
              width: '670px',
              data: {
                formData: responseResult$,
              },
            },
          )
          .closed.pipe(filter((result) => result?.type === 'done'));
      },
      error: (result) => {
        this.notificationService.errorNotify(result.message);
      },
    });
  }

  trackByAttachmentId(index: number, attachment: FileInfo): string {
    return attachment.fileToUpload?.lastModified?.toString();
  }
}
