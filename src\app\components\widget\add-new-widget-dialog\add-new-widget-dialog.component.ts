import { Dialog, DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { CdkMenuModule } from '@angular/cdk/menu';
import { AsyncPipe, Ng<PERSON>lass, NgFor, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, inject } from '@angular/core';

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject, filter, map, switchMap, take } from 'rxjs';

import { Widget_WidgetType } from '../../../entities/api';
import { EmbeddedBiConfigComponent } from '../../../modules/shared/common/choose-dada-source/embedded-bi-config/embedded-bi-config.component';
import { CheckboxComponent } from 'src/app/component/checkbox/checkbox.component';
import { IconComponent } from 'src/app/component/icon/icon.component';
import { defaultDialogConfig } from 'src/app/config/dialog';
import { DialogResult, TableSelectDialogResult } from 'src/app/interface/model';
import { WidgetComponent, WidgetType } from 'src/app/modules/container/document/cbx-module/widget/model/widget-dto';
import { WidgetService } from 'src/app/modules/services/widget.service';
import { CbxDialogCloseButtonComponent } from 'src/app/modules/shared/common/cbx-dialog-close-button/cbx-dialog-close-button.component';
import { ChooseDadaSourceComponent } from 'src/app/modules/shared/common/choose-dada-source/choose-dada-source.component';
import { ChooseDataFilterDialogComponent } from 'src/app/modules/shared/common/choose-dada-source/choose-data-filter-dialog/choose-data-filter-dialog.component';
import shareMode from 'src/assets/data/widget/shareMode/shareMode.json';

@UntilDestroy()
@Component({
  selector: 'app-add-new-widget-dialog',
  templateUrl: './add-new-widget-dialog.component.html',
  styleUrls: ['./add-new-widget-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    NgIf,
    NgFor,
    NgClass,
    IconComponent,
    CbxDialogCloseButtonComponent,
    CheckboxComponent,
    CdkMenuModule,
    AsyncPipe,
  ],
})
export class AddNewWidgetDialogComponent implements OnInit {
  private readonly dialogRef = inject(DialogRef<DialogResult<WidgetComponent[]>, AddNewWidgetDialogComponent>);

  title = 'Select widget';
  description = 'Select one or more widgets to add to your dashboard.';

  widgetTypeCategoryList: string[];
  selectCategory: string = 'All';
  selectShare$ = new BehaviorSubject<boolean>(false);
  isListingViewMode: boolean;
  defaultWidgetSource: string;
  displayWidgetTypeCategory: any;
  widgetTypeCategory = [
    {
      id: 'general',
      name: 'General',
    },
    {
      id: 'project',
      name: 'Projects',
    },
    {
      id: 'product',
      name: 'Products',
    },
    {
      id: 'sourcing',
      name: 'Sourcing',
    },
    {
      id: 'order',
      name: 'Orders & Logistics',
    },
    {
      id: 'quality',
      name: 'Quality & Compliance',
    },
    {
      id: 'master',
      name: 'Partner',
    },
    {
      id: 'infoCenter',
      name: 'Info Centre',
    },
    {
      id: 'share',
      name: 'Advanced',
    },
  ];
  currentWidgetTypeIndex: number;

  widgets: WidgetType[];
  sharedModeWidgets: WidgetType[];
  displayWidgets: WidgetType[];

  hasSelectedWidget: boolean;
  moduleIdList: any;
  naviModuleIdList: any;
  viewNameList: any;
  readonly selectedWidgetMap: Partial<{
    [K in Widget_WidgetType]: boolean;
  }> = {};

  readonly loading$ = new BehaviorSubject<boolean>(false);

  constructor(
    private readonly widgetService: WidgetService,
    private readonly cdr: ChangeDetectorRef,
    private readonly dialog: Dialog,
    @Inject(DIALOG_DATA) public data: any,
  ) {
    const { isListingViewMode, defaultWidgetSource, moduleIdList, naviModuleIdList, viewNameList } = data;
    this.isListingViewMode = isListingViewMode;
    this.defaultWidgetSource = defaultWidgetSource;
    this.moduleIdList = moduleIdList;
    this.naviModuleIdList = naviModuleIdList;
    this.viewNameList = viewNameList;
  }

  ngOnInit() {
    this.loading$.next(true);

    // this.widgetTypeCategoryList = ['All', ...this.widgetTypeCategory.map(({ name }) => name)];
    this.sharedModeWidgets = shareMode as WidgetType[];
    this.widgetService
      .fetchAllWidgetTypes()
      .pipe(untilDestroyed(this))
      .subscribe((data) => {
        this.widgets = data.filter((item) => item.widgetType !== 'shareMode');
        this.filterWidgetType(0);
        this.cdr.detectChanges();
        this.loading$.next(false);
      });
  }

  handleSelectWidgetAction(selectedWidget: WidgetType['id']) {
    this.selectedWidgetMap[selectedWidget] = !this.selectedWidgetMap[selectedWidget];
    this.hasSelectedWidget = this.getSelectedWidgetList().length > 0;
  }

  handleSelectSharedWidgetAction(widgetId: WidgetType['id']) {
    const currentSelection = this.selectedWidgetMap[widgetId];
    Object.keys(this.selectedWidgetMap).forEach((id) => {
      this.selectedWidgetMap[id] = false;
    });
    this.selectedWidgetMap[widgetId] = !currentSelection;
    this.hasSelectedWidget = this.getSelectedWidgetList().length > 0;
  }

  onCancel() {
    this.dialogRef.close({
      type: 'cancel',
    });
  }

  onDone() {
    this.dialogRef.close({
      type: 'done',
      payload: this.transformWidgetTypeToComponent(),
    });
  }

  filterWidgetType(index?: number) {
    this.currentWidgetTypeIndex = index;
    this.displayWidgets = [];
    this.getCustomWidget(index);
  }

  getCustomWidget(index?: number) {
    this.widgetService
      .fetchAllWidgetComponent()
      .pipe(untilDestroyed(this))
      .subscribe((data) => {
        this.widgets = [
          ...this.widgets,
          ...data.filter((item) => !this.widgets.some((widget) => widget.id === item.id)),
        ];
        this.displayWidgetTypeCategory = this.widgetTypeCategory.filter(
          ({ id }) => id === 'share' || this.widgets.some(({ category }) => category && category.includes(id)),
        );
        this.selectCategory = this.displayWidgetTypeCategory[index].id;
        this.widgetTypeCategoryList = this.displayWidgetTypeCategory.map(({ name }) => name);
        this.displayWidgets = this.widgets.filter(({ category }) =>
          category?.includes(this.displayWidgetTypeCategory[index].id),
        );
        this.cdr.detectChanges();
      });
  }

  trackBy(index: number, type: WidgetType): WidgetType['id'] {
    return type.id;
  }

  private getSelectedWidgetList(): Widget_WidgetType[] {
    return Object.entries(this.selectedWidgetMap)
      .filter(([, selection]) => selection)
      .map(([id]: [Widget_WidgetType, boolean]) => id);
  }

  private transformWidgetTypeToComponent(): WidgetComponent[] {
    return this.getSelectedWidgetList().map((id) => {
      const type = this.widgets.find((widget) => widget.id === id);

      let filters: WidgetComponent['filters'] = {};
      type?.filterDefine
        ?.filter((filterDefine) => filterDefine.default)
        .forEach((filterDefine) => {
          filters = { ...filters, [filterDefine.id]: filterDefine.default };
        });

      const result: WidgetComponent = {
        ...type,
        type: type.widgetType === 'shareMode' ? type.type : type.id,
        filters: type.widgetType === 'shareMode' ? type.filters : filters,
        rowSpan: type.defaultRowSpan ?? 2,
        columnSpan: type.defaultColumnSpan ?? 2,
      };

      return result;
    });
  }

  openChooseWidgetDialog() {
    const shareWidget = Object.entries(this.selectedWidgetMap)
      .filter(([, value]) => value)
      .map(([key]) => key);
    if (shareWidget[0] === 'FilterWidget') {
      this.dialog
        .open<any, any, ChooseDataFilterDialogComponent>(ChooseDataFilterDialogComponent, {
          ...defaultDialogConfig,
          maxWidth: '1120px',
          minWidth: '700px',
          width: '80vw',
          height: '780px',
          data: {
            moduleId: 'chooseDataFilter',
            moduleIdList: this.moduleIdList,
            naviModuleIdList: this.naviModuleIdList,
            viewNameList: this.viewNameList,
          },
        })
        .closed.pipe(
          filter((result) => result?.type === 'done'),
          map(({ payload }) => payload),
        )
        .subscribe((payload) => {
          this.dialogRef.close({
            type: 'done',
            payload,
          });
        });
    } else if (shareWidget[0] === 'EmbeddedBI') {
      this.dialog
        .open<any, any, EmbeddedBiConfigComponent>(EmbeddedBiConfigComponent, {
          ...defaultDialogConfig,
          maxWidth: '1120px',
          minWidth: '700px',
          width: '80vw',
          height: '780px',
          data: {
            moduleId: 'chooseDataFilter',
            moduleIdList: this.moduleIdList,
            naviModuleIdList: this.naviModuleIdList,
            viewNameList: this.viewNameList,
          },
        })
        .closed.pipe(
          filter((result) => result?.type === 'done'),
          map(({ payload }) => payload),
        )
        .subscribe((payload) => {
          this.dialogRef.close({
            type: 'done',
            payload,
          });
        });
    } else {
      this.dialog
        .open<TableSelectDialogResult, any, ChooseDadaSourceComponent>(ChooseDadaSourceComponent, {
          ...defaultDialogConfig,
          maxWidth: '1120px',
          minWidth: '700px',
          width: '80vw',
          height: '780px',
          data: {
            rowSelection: 'single',
            viewId: this.defaultWidgetSource ? this.defaultWidgetSource : null,
            widgetType: shareWidget[0],
            widgetChartType: shareWidget[0],
            isListingViewMode: this.isListingViewMode,
            // defaultWidgetSource: this.defaultWidgetSource,
            addMode: true,
          },
        })
        .closed.pipe(
          take(1),
          filter((result) => result?.type === 'done'),
          // tap(() => download$.next(true)),
          switchMap(({ payload }) => payload),
        )
        .subscribe((payload) => {
          this.dialogRef.close({
            type: 'done',
            payload,
          });
        });
    }
  }

  removeWidget(widget: WidgetType) {
    this.displayWidgets = this.displayWidgets.filter((displayWidget) => displayWidget?.id !== widget?.id);
    this.widgets = this.widgets.filter((displayWidget) => displayWidget?.id !== widget?.id);
    this.widgetService.deleteWidgetComponent(widget.id).subscribe();
  }
}
