import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Input, OnInit } from '@angular/core';

import { UntilD<PERSON>roy } from '@ngneat/until-destroy';
import { CKEditor4, CKEditorModule } from 'ckeditor4-angular';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { DocumentDataQuery, DocumentDataService, Section, SubSectionsQuery } from '../state';

@UntilDestroy()
@Component({
  selector: 'app-html-editor-section-container',
  templateUrl: './html-editor-section-container.component.html',
  styleUrls: ['./html-editor-section-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CKEditorModule, AsyncPipe],
})
export class HtmlEditorSectionContainerComponent implements OnInit {
  private readonly documentDataService = inject(DocumentDataService);
  private readonly subSectionsQuery = inject(SubSectionsQuery);
  private readonly documentDataQuery = inject(DocumentDataQuery);

  @Input() section: Section;
  @Input() edit = false;

  data$: Observable<string>;

  config = {
    removePlugins: 'easyimage,cloudservices,exportpdf', // Plugins that require API keys.
    entities: false,
    allowedContent: true,
    versionCheck: false,
    protectedSource: [
      /<#assign[^>]*>/g,
      /<#if[^>]*>/g,
      /<\/#if>/g,
      /<#list[^>]*>/g,
      /<\/#list>/g,
      /<a[^>]*>/g,
      /<\/a>/g,
    ],
    toolbar: [
      [
        'Source',
        '-',
        'NewPage',
        '-',
        'Bold',
        'Italic',
        'Underline',
        'Strike',
        'TextColor',
        'BGColor',
        '-',
        'Cut',
        'Copy',
        'Paste',
        'Link',
        'Unlink',
        '-',
        'Table',
        'SpecialChar',
        'Image',
        '-',
        'Maximize',
      ],
      [
        'Undo',
        'Redo',
        '-',
        'JustifyLeft',
        'JustifyCenter',
        'JustifyRight',
        'JustifyBlock',
        'Styles',
        'Format',
        'Font',
        'FontSize',
      ],
    ],
  };

  fieldId: string;

  ngOnInit() {
    this.subSectionsQuery
      .getSubSection$(this.section.subSections[0])
      .pipe(map((subSection) => (subSection && subSection.fields) || []))
      .subscribe((fields) => {
        this.fieldId = fields[0].id;
      });

    this.data$ = this.documentDataQuery.select(this.fieldId);
  }

  dataChange(value: CKEditor4.EventInfo) {
    this.documentDataService.updateData({ [this.fieldId]: value });
  }
}
