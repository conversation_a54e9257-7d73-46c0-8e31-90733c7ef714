import { Dialog } from '@angular/cdk/dialog';
import { Injectable } from '@angular/core';

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Observable, of } from 'rxjs';
import { switchMap } from 'rxjs/operators';

import { generalAlertDialogConfig } from '../../../../../../../config/dialog';
import { CodelistRef } from '../../../../../../../interface/model';
import { AlertDialogComponent } from '../../../../../../shared/common/warning-dialog/alert-dialog.component';
import { AbstractGroupRowUIAction } from '../../../../grid-section-content/group-row-action/model/abstract-group-row-ui-action';
import { DocumentDataQuery, DocumentDataService } from '../../../../state';
import { DocumentDataActionService } from '../../../../state/document-data-action.service';
import { deepClone, immutableMove, isNotNil } from 'src/app/utils';

@UntilDestroy()
@Injectable()
export class VpoCopyChainOfCustodyGroupRowAction extends AbstractGroupRowUIAction {
  // ... (existing code)
  groupRowSelectedRows: any[] = [];

  rowIndex: number;

  constructor(
    private readonly documentDataService: DocumentDataService,
    private readonly documentDataActionService: DocumentDataActionService,
    private readonly documentDataQuery: DocumentDataQuery,
    private readonly dialog: Dialog,
  ) {
    super();
  }

  validate(): Observable<boolean> {
    const { allLeafChildren } = this.node;

    this.groupRowSelectedRows = allLeafChildren.filter((children) => isNotNil(children?.data) && children.isSelected());

    const isSelectedAny = this.groupRowSelectedRows.length !== 0;

    if (!isSelectedAny) {
      const { popupMeta } = this.actionParams;
      const openDialogMeta = {
        type: popupMeta.type,
        buttonType: popupMeta.buttonType,
        message: popupMeta.message,
      };
      return this.openWarningDialog(openDialogMeta.buttonType, openDialogMeta.message).closed.pipe(
        switchMap(({ payload }) => (payload ? of(true) : of(false))),
        untilDestroyed(this),
      );
    }
    return of(true);
  }

  process() {
    const allRowData = this.documentDataActionService.dataTransferFromStoreToApi(
      this.documentDataQuery.getFilteredValue(),
    )[this.sectionId];
    let rowData = !allRowData ? [] : deepClone(allRowData);
    let latestCopyRow: any;
    this.groupRowSelectedRows.forEach((selectedRow) => {
      const rowDataLength = rowData?.length || 0;
      const { analysisId, ...restData } = selectedRow.data || {};

      rowData = this.gridSectionService.generateNewRow(this.fields, rowData, restData, this.seqId);
      rowData[rowDataLength] = { ...rowData[rowDataLength], isFromTemplate: null };

      // Update status field to "Requested"
      const latestRowIndex = rowDataLength;
      const requestedStatus: CodelistRef = {
        code: 'REQD',
        name: 'Requested',
        version: 1,
      };
      rowData[latestRowIndex].chainOfCustodyStatus = requestedStatus;

      latestCopyRow = rowData[latestRowIndex];

      const selectedRowIndex = rowData.findIndex((r) => r.id === selectedRow?.id);
      const moveRowData = immutableMove(rowData, latestRowIndex, selectedRowIndex + 1);
      rowData = moveRowData;
    });
    this.regenerateNewSeqNos(rowData, latestCopyRow);
    return of(true);
  }

  // ... (existing code)
  private openWarningDialog(messageButtons: string, message: string) {
    return this.dialog.open<any>(AlertDialogComponent, {
      ...generalAlertDialogConfig,
      data: {
        messageButtons,
        message,
        title: 'warning',
      },
    });
  }

  private regenerateNewSeqNos(rowData: any[], latestCopyRow: any) {
    const newRowsWrapper = this.gridSectionService.generateNewSeqNos(
      rowData,
      this.fields,
      this.moduleId,
      this.sectionId,
      this.rowGroupField,
      latestCopyRow,
    );
    const newRowData = newRowsWrapper.result;
    this.documentDataService.dynamicRowCellChanges$.next({ changes: newRowsWrapper.changes });
    this.gridSectionService.focusFirstCellSubject$.next({
      focus: true,
      autoFocusRowId: latestCopyRow?.id,
    });
    this.documentDataService.updateData({ [this.sectionId]: newRowData });
  }
}
