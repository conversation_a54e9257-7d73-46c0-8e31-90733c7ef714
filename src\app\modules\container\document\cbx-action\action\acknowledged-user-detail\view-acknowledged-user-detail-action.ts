import { Dialog } from '@angular/cdk/dialog';
import { inject, Injectable } from '@angular/core';

import { DocumentDefineStore } from '../../../state';
import { AbstractDocumentUIAction } from '../../model/abstract-document-ui-action';
import { AcknowledgedUserDetailDialogComponent } from './acknowledged-user-detail-dialog/acknowledged-user-detail-dialog.component';
import { defaultDialogConfig } from 'src/app/config/dialog';
import { AcknowledgedUserDetailDialogData } from 'src/app/interface/model';

@Injectable()
export class ViewAcknowledgedUserDetailAction extends AbstractDocumentUIAction {
  private readonly documentDefineStore = inject(DocumentDefineStore);
  private readonly dialog = inject(Dialog);

  process() {
    const { acknowledgedCount, assignedUserCount } = this.documentData;
    const acknowledgedRatio = `${acknowledgedCount}/${assignedUserCount}`;
    const unacknowledgedRatio = `${assignedUserCount - acknowledgedCount}/${assignedUserCount}`;

    return this.dialog.open<any, AcknowledgedUserDetailDialogData, AcknowledgedUserDetailDialogComponent>(
      AcknowledgedUserDetailDialogComponent,
      {
        ...defaultDialogConfig,
        height: '565px',
        width: '700px',
        data: {
          title: this.actionContext.popupMeta.title,
          acknowledgedRatio,
          unacknowledgedRatio,
          moduleId: this.moduleId,
          refNo: this.refNo,
        },
      },
    ).closed;
  }

  finalizeProcess() {
    this.documentDefineStore.setLoading(false);
  }
}
