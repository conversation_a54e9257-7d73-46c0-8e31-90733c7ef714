import { Injectable } from '@angular/core';

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { combineLatest } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, takeUntil } from 'rxjs/operators';

import { DocumentDropdownService } from '../../../service/document-dropdown.service';
import { DocumentDynamicDataService } from '../../../service/document-dynamic-data.service';
import { DocumentStatusService } from '../../../state';
import { deepEqual } from 'src/app/utils';

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class CompTemplDynamicDataService extends DocumentDynamicDataService {
  fetchInitialized$ = this.documentDataQuery
    .select('fetchInitialized')
    .pipe(filter((fetchInitialized) => !fetchInitialized));

  dataInitializing$ = this.documentDataQuery
    .selectDataInitialized$()
    .pipe(filter((dataInitialized) => !dataInitialized));

  constructor(
    protected readonly documentStatusService: DocumentStatusService,
    protected readonly dropdownService: DocumentDropdownService,
  ) {
    super();
  }

  registerBusinessLogic() {
    this.documentDataQuery
      .select('applyModule')
      .pipe(distinctUntilChanged(deepEqual), debounceTime(5), untilDestroyed(this), takeUntil(this.dataInitializing$))
      .subscribe(() => {
        const newList = [];
        const compTemplSectionList = this.documentDataQuery.getValue()?.compTemplSectionList;
        compTemplSectionList?.forEach((section) => {
          newList.push({ ...section, levelType: null, level: null });
        });
        this.documentDataService.updateData({ compTemplSectionList: newList });
      });

    combineLatest([
      this.documentDataQuery.select('compTemplSectionList'),
      this.dropdownService.getDropdownStoreByName('listLevels'),
      this.documentDataQuery.select('applyModule'),
    ])
      .pipe(
        distinctUntilChanged(deepEqual),
        debounceTime(5),
        filter(([, listLevels]) => listLevels?.length !== 0),
        untilDestroyed(this),
      )
      .subscribe(([compTemplSectionList, listLevels, applyModule]) => {
        const newList = [];
        const mainOptions = listLevels.filter((option) => {
          const otpionAny = option as any;
          return otpionAny?.module === applyModule?.module && otpionAny?.levelType === 'header';
        });

        if (mainOptions?.length > 0) {
          compTemplSectionList?.forEach((compTemplSection) => {
            if (compTemplSection?.levelType?.code === 'header') {
              newList.push({ ...compTemplSection, level: mainOptions[0] });
            } else {
              newList.push(compTemplSection);
            }
          });
        }

        this.documentDataService.updateData({ compTemplSectionList: newList });
      });
  }
}
