$topbarHeight: 60px;
$tabHeight: 40px;

:host {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 2px;
  box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.25);
  background-color: var(--color-background-contrast-lightest);
  contain: strict;
}

:host ::ng-deep {
  .cdk-virtual-scroll-content-wrapper {
    padding-top: 8px;
    width: 100%;
  }
}

.cbx-chat-list-topbar-wrapper {
  height: $topbarHeight;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.cbx-chat-list-title {
  font-size: 24px;
  line-height: normal;

  .select-wrapper {
    font-weight: bold;
    letter-spacing: 0.29px;
    color: #000000;
    font-size: inherit;
    line-height: inherit;
    &:focus {
      > .arrow-down-icon {
        color: var(--color-primary-blue-02);
      }
    }
    &:hover {
      > .arrow-down-icon {
        color: var(--color-primary-blue-01);
      }
    }
  }

  .arrow-down-icon {
    color: var(--color-primary-gray-75);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid;
    margin: 4px 4px 4px 0;
  }
}

.cbx-chat-list-overlay-menu {
  border-radius: 2px;
  box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.25);
  background-color: #ffffff;
  padding: 8px 0px;

  .cbx-chat-room-list-menu-item {
    display: block;
    padding: 8px 24px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    border: 0;
    width: 100%;
    text-align: left;
    background-color: transparent;
    cursor: pointer;

    &:focus {
      background-color: rgba($color: #4288f5, $alpha: 0.08);
    }
    &:hover {
      background-color: rgba($color: #4288f5, $alpha: 0.12);
    }

    &.cbx-chat-room-list-menu-item-active {
      color: #4288f5;
    }
  }
}

.cbx-chat-list-icon-button {
  padding: 0;
  outline: 0;
  border: 0;
  margin-left: 6px;
  background-color: transparent;
  border-radius: 4px;
  font-size: 0;
  cursor: pointer;

  &:focus {
    background-color: rgba($color: var(--legacy-primary-blue-01), $alpha: 0.08);
  }
  &:hover {
    background-color: rgba($color: var(--legacy-primary-blue-01), $alpha: 0.12);
  }
}

.cbx-chat-list-icon {
  display: block;
  width: 24px;
  height: 24px;
  color: var(--color-primary-blue-02);
}

.cbx-chat-room-more-btn {
  display: none;
  margin: 20px 4px;
  width: 20px;
  height: 20px;
  background-color: transparent;
  color: var(--color-icon-light);
  border-radius: 50%;
  transform: rotate(90deg);
  transition: transform 0.2s ease-out;

  &:hover,
  &:focus {
    color: var(--color-icon-subtlest);
  }
}

.cbx-chat-list-spacer {
  flex: 1 1 auto;
}

.cbx-chat-list-search-wrapper {
  margin-bottom: 10px;
}

.cbx-chat-list-search {
  margin: 0 16px;
  display: flex;
  align-items: center;
  height: 28px;
  border-radius: 2px;
  border: 1px solid transparent;
  cursor: pointer;
  flex-direction: row-reverse;

  .cbx-chat-list-search-input {
    opacity: 0.75;
    font-size: 12px;
    padding: 4px;
    outline: 0;
    border: 0;
    width: 0;
    background-color: transparent;
  }

  &.cbx-chat-list-display-search-input {
    border-color: rgba($color: #707070, $alpha: 0.05);
    background-color: rgba($color: #485962, $alpha: 0.05);
    .cbx-chat-list-search-input {
      width: 100%;
    }
  }
}

.cbx-chat-list-content-wrapper {
  position: relative;
  height: calc(100% - #{$topbarHeight + $tabHeight});
  border-top: 2px solid rgba(58, 70, 77, 0.14);
}

.cbx-chat-list-items-viewport {
  height: 100%;
  width: 100%;
}

.cbx-chat-list-item-wrapper {
  height: 80px;
  padding: 12px;
  padding-left: 0;
  cursor: pointer;
  contain: strict;
  border-bottom: 1px solid rgba(58, 70, 77, 0.14);

  &.cbx-chat-list-item-active {
    background-color: var(--color-background-contrast-default);
  }
  &:hover {
    background-color: var(--color-background-contrast-default);

    .cbx-chat-list-item-date,
    .cbx-chat-list-item-status-wrapper {
      display: none;
    }

    .cbx-chat-room-more-btn {
      display: block;
    }
  }
}

.cbx-chat-list-item-inner-wrapper {
  display: flex;
  height: 100%;
  box-sizing: border-box;
}

.cbx-chat-list-item-hint-wrapper {
  flex: 0 0 24px;
}

.cbx-chat-list-unread-hint {
  border-radius: 50%;
  width: 8px;
  height: 8px;
  margin: 4px auto;
  background-color: var(--color-main-secondary);
}

.cbx-chat-list-item-content-wrapper {
  flex: 1 1 auto;
  overflow: hidden;
  margin-left: 8px;
}

.cbx-chat-list-item-title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 20px;
}

.cbx-chat-list-item-title {
  font-size: 14px;
  font-weight: bold;
  color: var(--color-primary-gray);
  margin: 0;
  white-space: pre;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 190px;
}

.cbx-chat-list-item-date {
  font-size: 12px;
  letter-spacing: 0.14px;
  color: rgba(58, 70, 77, 0.72);
  margin-left: 8px;
  min-width: fit-content;
  text-align: end;
}

.cbx-chat-list-item-description-wrapper {
  display: flex;
  height: 36px;
  overflow: hidden;
}

.cbx-chat-list-item-description {
  flex: 1 1 auto;
  word-break: break-word;
  font-size: 14px;
  color: var(--color-primary-gray);
  line-height: 18.5px;
}

.cbx-chat-list-item-status-wrapper {
  color: var(--color-icon-light);
}

.cbx-chat-list-lock-icon {
  color: var(--color-icon-light);
  width: 20px;
  height: 20px;
}

.cbx-chat-list-new-room-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  > .cbx-chat-list-icon-button {
    margin-left: 0;
    padding: 4px;
    > .cbx-chat-list-icon {
      width: 64px;
      height: 64px;
      margin: auto;
    }
  }
}

.cbx-chat-list-new-room-label {
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.17px;
  color: var(--color-primary-gray);
}

.cbx-chat-list-loading-brick {
  width: 100%;
  height: 80%;
  background-color: var(--color-secondary-gray-03);
  border-radius: 3px;
}

.cbx-chat-list-opacity-animation {
  animation: twinkle-opacity 1s infinite alternate linear;
}

.cbx-chat-list-animation-delay {
  animation-delay: 400ms;
}

@keyframes twinkle-opacity {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.4;
  }
}

.cbx-chat-list-opacity-0 {
  opacity: 0;
}

.cbx-chat-list-unread-active {
  fill: var(--legacy-primary-blue-01);
}

.cbx-chat-list-unread-inactive {
  fill: currentColor;
}

.cbx-chat-list-pin-icon {
  height: 20px;
  color: var(--color-icon-light);
  border: none;
  outline: none;
  background-color: transparent;
  padding: 0;
  margin-left: 4px;

  .app-icon {
    --icon-size: 20px;
  }
}
