<form class="tag-form">
  <!--  -->
  <input
    #tagInput
    class="tag-input-text"
    [matAutocomplete]="auto"
    [placeholder]="placeholderText"
    matInput
    [formControl]="tagControl"
    (focus)="tagInputFocus()"
    (keydown)="handleKeyDown($event)"
  />

  <mat-autocomplete
    #auto="matAutocomplete"
    class="mat-autocomplete-cbx-base"
    [panelWidth]="500"
    (optionSelected)="selected($event)"
  >
    <mat-option
      *ngFor="let filteredTag of filteredTags$ | async; trackBy: trackByFn"
      [value]="filteredTag.content"
      class="tag-dropdown-option"
    >
      <span>{{ filteredTag.content }}</span>
    </mat-option>
    <mat-divider class="tag-divider" *ngIf="(isNewTag$ | async) && (filteredTags$ | async)?.length > 0"></mat-divider>
    <mat-option
      #newTagOpt
      *ngIf="isNewTag$ | async"
      class="tag-dropdown-option tag-dropdown-option-border-top"
      [value]="tagControl.value"
    >
      <span>{{ tagControl.value }} <b>(New Tag)</b></span>
    </mat-option>
  </mat-autocomplete>
</form>
