/* eslint-disable id-blacklist */
import { Dialog, DialogRef } from '@angular/cdk/dialog';
import { CdkScrollable } from '@angular/cdk/overlay';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule, Location } from '@angular/common';
import { HttpParams } from '@angular/common/http';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  HostBinding,
  Input,
  OnDestroy,
  OnInit,
  Output,
  Renderer2,
  ViewChild,
  inject,
  ChangeDetectorRef,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';

import { filterNilValue } from '@datorama/akita';
import { TranslocoModule, TranslocoService } from '@ngneat/transloco';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import * as R from 'ramda';
import { BehaviorSubject, combineLatest, merge, Observable, of, iif, Subject, Subscription, throwError } from 'rxjs';
import {
  auditTime,
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  finalize,
  map,
  pairwise,
  shareReplay,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs/operators';

import { IconComponent } from '../../../../component/icon/icon.component';
import { defaultDialogConfig, generalAlertDialogConfig } from '../../../../config/dialog';
import { ApprovalActions } from '../../../../interface/model/approval';
import { CodelistService } from '../../../../services/codelist.service';
import { PopoverRef } from '../../../../services/popover/popover-ref';
import {
  camelCaseToDisplay,
  capitalizeLetter,
  checkConditionsValidInObject,
  deepClone,
  deepEqual,
  genStatusLabelKey,
  getPrefixLabel,
  isEmptyOrNil,
  isNotEmptyOrNil,
  isNotNil,
} from '../../../../utils';
import { BaseMenuBarComponent } from '../../../shared/common/base-component/base-menu-bar/base-menu-bar.component';
import { LinkBarComponent } from '../../../shared/common/link-bar/link-bar.component';
import { ColorfulFontPipe } from '../../../shared/pipe/colorful-font.pipe';
import { CompareQuotationsDialogComponent } from '../../listing/compare-quotations-dialog/compare-quotations-dialog.component';
import { RefreshItemsAction } from '../cbx-action/action/refresh-items-action';
import { AcceptRejectVendorChangeAction } from '../cbx-action/action/vendor-change-proposed/accept-reject-vendor-change-action';
import { DocumentSiblingNavigatorComponent } from '../component/document-sibling-navigator/document-sibling-navigator.component';
import { DocumentSubjectComponent } from '../component/document-subject/document-subject.component';
import { CpmBarComponent } from '../cpm-bar/cpm-bar.component';
import { OverlayHostDirective } from '../directive/overlay-host.directive';
import { DocumentAlertSectionComponent } from '../document-alert-section/document-alert-section.component';
import { DocumentContainerEditFooterComponent } from '../document-container-edit-footer/document-container-edit-footer.component';
import { DocumentContentComponent } from '../document-content/document-content.component';
import { DocumentOverlayActionService } from '../document-preview/document-overlay-action.service';
import { InlineComparisonService } from '../inline-comparison/inline-comparison.service';
import { MoreMenuComponent } from '../more-menu/more-menu.component';
import { DocumentDataResolved } from '../resolver/document-data-resolved';
import { RightHandPanelStateService } from '../right-hand-panel/right-hand-panel-state.service';
import { RightHandPanelComponent } from '../right-hand-panel/right-hand-panel.component';
import { DocumentActionDispatcherService } from '../service/document-action-dispatcher.service';
import { DocumentChapterService } from '../service/document-chapter.service';
import { DocumentEventService } from '../service/document-event.service';
import { DocumentValidateService } from '../service/document-validate.service';
import { DocumentHookToken } from '../service/hook/document-hook';
import { OverlayContainerService } from '../service/overlay-container.service';
import { ValidationHandler } from '../service/validation-handler.service';
import { ShipmentTrackingPanelStateService } from '../shipment-tracking-panel/shipment-tracking-panel-state.service';
import {
  DocumentComparisonQuery,
  DocumentDataQuery,
  DocumentDataService,
  DocumentDefineQuery,
  DocumentDefineService,
  DocumentStatus,
  DocumentStatusQuery,
  DocumentStatusService,
  Section,
  TabGroup,
} from '../state';
import { DocumentCommentPanelService } from '../state/document-comment-panel.service';
import { DocumentCompareChangeQuery } from '../state/document-compare-change.query';
import { DocumentCompareChangeService } from '../state/document-compare-change.service';
import { InlineComparisonResultStore } from '../state/document-comparison-result.store';
import { DocumentComparisonService } from '../state/document-comparison.service';
import { DocumentCopyDataService } from '../state/document-copy-data.service';
import { DocumentDataActionService } from '../state/document-data-action.service';
import { TableOfContentsComponent } from '../table-of-contents/table-of-contents.component';
import { ToolPanelComponent } from '../tool-panel/tool-panel.component';
import { VersionComparisonVm } from '../version-comparison/model/version-comparison';
import { SlideToggleComponent } from 'src/app/component/slide-toggle/slide-toggle.component';
import { CBX_URL } from 'src/app/config/constant';
import { TooltipDirective } from 'src/app/directives/tooltip.directive';
import { Validation_ValidationFailedDto, Vpo_VpoItemDto } from 'src/app/entities/api';
import { AlertDialogTypes, DialogButtonTypes } from 'src/app/entities/dialog';
import { DataAbstract, DocHeaderField, FieldDefine, MenuItem } from 'src/app/entities/form-field';
import { ErrorWrapper, ValidationError } from 'src/app/interface/model';
import { NotificationEvent } from 'src/app/interface/model/notification';
import { ApprovalService } from 'src/app/modules/cbx-action/helper/approval.service';
import { ActivityDialogComponent } from 'src/app/modules/container/document/activity/activity-dialog/activity-dialog.component';
import { AlertService } from 'src/app/modules/services/alert.service';
import { DocumentService } from 'src/app/modules/services/document.service';
import { FieldBackgroundColorfulService } from 'src/app/modules/services/field-background-colorful.service';
import { MenuBarService } from 'src/app/modules/services/menu-bar.service';
import { AlertDialogComponent } from 'src/app/modules/shared/common/warning-dialog/alert-dialog.component';
import { AutoScrollNodeDirective } from 'src/app/modules/shared/directives/axis/auto-scroll-node.directive';
import { TocContentDirective } from 'src/app/modules/shared/directives/toc-content.directive';
import { ApiService } from 'src/app/services/api.service';
import { AuthService } from 'src/app/services/auth.service';
import { BehaviorPreferenceService } from 'src/app/services/behavior-preference.service';
import { CbxLabelTranslateService } from 'src/app/services/cbx-label-translate.service';
import { ChatSettingService } from 'src/app/services/chat/chat-setting.service';
import { ConfigurationService } from 'src/app/services/configuration.service';
import { CpmService } from 'src/app/services/cpm.service';
import { ConcurrentPendingChangesHook } from 'src/app/services/guard/pending-changes.guard';
import { MessageDialogService } from 'src/app/services/message-dialog.service';
import { NotificationService } from 'src/app/services/notification.service';
import { DocumentOverlayDataService } from 'src/app/services/overlay/document-overlay-data.service';
import { PendingStatusService } from 'src/app/services/pending-status.service';
import { RouterStateService } from 'src/app/services/router-state.service';
import { StateRecordService } from 'src/app/services/state-record.service';
import { ValidateTooltipService } from 'src/app/services/validate-tooltip.service';
import { InputObservable } from 'src/app/utils/input-subject-observer';
import { notMap } from 'src/app/utils/operators';
import { DomainAttributeService } from 'src/app/workspace/service/domain-attribute.service';

@UntilDestroy()
@Component({
  selector: 'app-document-container',
  templateUrl: './document-container.component.html',
  styleUrls: ['./document-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [RefreshItemsAction, AcceptRejectVendorChangeAction, OverlayContainerService],
  standalone: true,
  imports: [
    CommonModule,
    TranslocoModule,
    ScrollingModule,
    IconComponent,
    LinkBarComponent,
    BaseMenuBarComponent,
    TableOfContentsComponent,
    MoreMenuComponent,
    DocumentSiblingNavigatorComponent,
    DocumentSubjectComponent,
    DocumentAlertSectionComponent,
    AutoScrollNodeDirective,
    TocContentDirective,
    DocumentContentComponent,
    DocumentContainerEditFooterComponent,
    CpmBarComponent,
    ToolPanelComponent,
    RightHandPanelComponent,
    TooltipDirective,
    OverlayHostDirective,
    ColorfulFontPipe,
    SlideToggleComponent,
  ],
})
export class DocumentContainerComponent implements OnInit, AfterViewInit, OnDestroy {
  @HostBinding('class') class = 'contain-strict block width-100 height-100 position-relative';

  @ViewChild(RightHandPanelComponent) rightHandPanelCom: RightHandPanelComponent;
  @ViewChild(CdkScrollable) cdkScrollable: CdkScrollable;
  @ViewChild('sectionWrapper', { read: ElementRef }) sectionWrapper: ElementRef;
  @ViewChild('alertSection', { read: ElementRef }) alertSection: ElementRef;

  private readonly documentValidateService = inject(DocumentValidateService);
  private readonly documentHook = inject(DocumentHookToken);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly concurrentPendingChangesHook = inject(ConcurrentPendingChangesHook);

  headerTopSectionVisible$ = this.documentHook.headerTopSectionVisible$;
  headerMidSectionVisible$ = this.documentHook.headerMidSectionVisible$;
  headerBotSectionVisible$ = this.documentHook.headerBotSectionVisible$;
  headerActionMenuVisible$ = this.documentHook.headerActionMenuVisible$;
  headerMidSectionSiblingNavigatorVisible = this.documentHook.headerMidSectionSiblingNavigatorVisible;
  editingUserComponent$ = this.documentHook.editingUserComponent$;

  showProposedChangesToggle$ = this.documentHook.showProposedChangesToggle$;
  isProposedChangesCheck$ = this.documentHook.isProposedChangesCheck$;

  refNo$ = this.documentStatusQuery.select('documentRefNo').pipe(distinctUntilChanged());
  get refNo() {
    return this.documentStatusQuery.getValue().documentRefNo;
  }

  moduleId$ = this.documentStatusQuery.select('moduleId').pipe(distinctUntilChanged());
  get moduleId() {
    return this.documentStatusQuery.getValue().moduleId;
  }

  inSecondaryRoute = this.routerStateService.inSecondaryRoute;
  sectionIdPrefix = this.inSecondaryRoute ? 'secondary' : undefined;

  verticalSpacer: Element;
  overlays: PopoverRef[] = [];

  hasHistoryPage: boolean;

  showCpmBar$ = new BehaviorSubject<boolean>(false);
  @InputObservable()
  @Input()
  showCpmBar: boolean;

  showCpmButton$ = new BehaviorSubject(false);

  backgroundColor: string;
  moduleValue: string;
  fontColor: string;
  markChange: boolean;
  dataAbstract: string;
  showInitializeCpm$ = new BehaviorSubject(true);
  @InputObservable()
  @Input()
  showInitializeCpm: boolean;
  initializing$ = new BehaviorSubject(false);
  userId: string;
  initialized$ = this.initializing$.pipe(notMap);
  showReplaceBomButton = false;

  hasAlertSection$ = this.domainAttributeService.crossModuleAlertSetting$;

  theModuleHasSpecifySmartAlert$ = this.domainAttributeService.showSmartAlertModules$;

  private spacerHeight = 0;
  private spacerScrollSub: Subscription;
  private readonly spacerHeightZero$ = new Subject<boolean>();

  private get isInEditView(): boolean {
    return this.documentStatusService.getDocumentStatus() === DocumentStatus.Edit;
  }

  statusChanged$ = this.documentStatusService.getDocumentStatus$();
  isExpand$ = this.rightHandPanelStateService.isExpand$;
  edit$ = this.documentStatusService.isEditStatus$;
  view$ = this.documentStatusService.isViewStatus$;
  copyToNew$ = this.statusChanged$.pipe(map((status) => status === DocumentStatus.Copy));
  copyToExists$ = this.statusChanged$.pipe(map((status) => status === DocumentStatus.PartialCopy));
  copy$ = this.documentStatusService.isCopyStatus$;

  @Input() tabId: string;
  @Input() openApprovePopup: boolean;
  @Input() openRejectPopup: boolean;
  @Input() openCpmDialog: boolean;

  @Output() initCpmEvent = new EventEmitter<void>();

  newTocCollapse$ = new Subject<boolean>();
  initialTocCollapse$ = this.inSecondaryRoute
    ? of(true)
    : this.moduleId$.pipe(
        switchMap((moduleId) =>
          iif(
            () => moduleId === 'vqce',
            of(true),
            this.behaviorPreferenceService.getModuleScopeSetting(moduleId).pipe(
              map((moduleScopeSetting) => !!moduleScopeSetting?.tocCollapse),
              catchError(() => of(false)),
            ),
          ),
        ),
      );
  tocCollapse$ = merge(this.initialTocCollapse$, this.newTocCollapse$).pipe(untilDestroyed(this), shareReplay(1));
  tocCollapseLoaded$ = this.tocCollapse$.pipe(map(() => true));

  cpmInitialized$ = new Subject<void>();

  defineLoading$ = this.documentDefineQuery.selectLoading();
  data$ = this.documentDataQuery.documentViewData$;
  dataLoading$ = this.documentDataQuery.selectLoading();

  dataWithoutLoading$ = this.data$.pipe(filter(({ loading, ...data }) => isNotEmptyOrNil(data)));

  loadCount: number = 0;

  onlyDataLoading$ = combineLatest([this.defineLoading$, this.dataLoading$]).pipe(
    map(([defineLoading, dataLoading]) => {
      if (this.concurrentPendingChangesHook.isConcurrentMode) {
        if (!defineLoading && dataLoading) {
          this.loadCount += 1;
        }
        return !defineLoading && dataLoading && this.loadCount === 1;
      }

      return !defineLoading && dataLoading;
    }),
  );

  dataReady$ = this.documentDataQuery.selectDataInitialized$();
  defineReady$ = this.defineLoading$.pipe(notMap);

  isLatestVersion$ = this.documentDataQuery.select('isLatest').pipe(filterNilValue());
  notLatestVersion$ = this.isLatestVersion$.pipe(notMap);
  defineErrorMessage$ = this.documentDefineService.defineErrorMessage$;
  dataErrorMessage$ = this.documentDataService.dataErrorMessage$;
  errorMessage$ = merge(this.defineErrorMessage$, this.dataErrorMessage$);
  noErrorMessage$ = this.errorMessage$.pipe(notMap);

  showLoadingIndicator$ = combineLatest([this.defineLoading$, this.errorMessage$.pipe(startWith(''))]).pipe(
    map(([defineLoading, errorMessage]) => defineLoading && !errorMessage),
  );

  editable$ = combineLatest([this.documentStatusService.isEditStatus$, this.onlyDataLoading$]).pipe(
    map(([isEditStatus, dataLoading]) => isEditStatus && !dataLoading),
  );

  moduleLabel$ = this.documentDefineQuery.select('label');

  header$ = this.documentDefineQuery.select('header').pipe(filter((header) => !!header));
  headerFields$ = this.header$.pipe(map(({ fields }) => fields));
  linkbar$ = this.header$.pipe(
    map(({ linkbar }) => linkbar),
    filter((linkbar) => !!linkbar),
  );

  firstLineLinkBarItems$: Observable<MenuItem[]> = this.linkbar$.pipe(
    map(({ menuItems }) => menuItems),
    filter((linkBarItem) => !!linkBarItem),
    // TODO: Hard code only display some linkbar items
    map((linkBarItems) => linkBarItems.filter((linkBarItem) => ['followDoc', 'unfollowDoc'].includes(linkBarItem.id))),
  );

  secondLinkBarItems$: Observable<MenuItem[]> = this.linkbar$.pipe(
    map(({ menuItems }) => menuItems),
    filter((linkBarItem) => !!linkBarItem),
    // TODO: Hard code only display some linkbar items
    map((linkBarItems) =>
      linkBarItems.filter(
        (linkBarItem) =>
          ['popupDocDetail', 'relatedDoc', 'commentDoc', 'readCountButton', 'acknowledgedCountButton'].includes(
            linkBarItem.id,
          ) && checkConditionsValidInObject(linkBarItem.existConditions, this.documentDataQuery.getValue()),
      ),
    ),
  );

  editLinkBarItem$: Observable<MenuItem[]> = this.linkbar$.pipe(
    map(({ menuItems }) => menuItems),
    filter((linkBarItem) => !!linkBarItem),
    // TODO: Hard code only display some linkbar items
    map((linkBarItems) =>
      linkBarItems.filter(
        (linkBarItem) =>
          ['popupDocDetail', 'commentDoc'].includes(linkBarItem.id) && this.isDisplayCommentsInEditMode(linkBarItem.id),
      ),
    ),
  );

  private isDisplayCommentsInEditMode(linkBarItemId: string) {
    return (
      (this.documentStatusService.getDocumentStatus() === DocumentStatus.Edit &&
        !Number.isNaN(this.documentStatusService.getUrlVersion()) &&
        !this.domainAttributeService.enabledConcurrentModules.includes(this.moduleId)) ||
      (['popupDocDetail'].includes(linkBarItemId) && this.action === 'create')
    );
  }

  commentLinkBarItems$: Observable<MenuItem[]> = this.linkbar$.pipe(
    map(({ menuItems }) => menuItems),
    filter((linkBarItem) => !!linkBarItem),
    // TODO: Hard code only display some linkbar items
    map((linkBarItems) => linkBarItems.filter((linkBarItem) => ['addComment', 'readComment'].includes(linkBarItem.id))),
  );

  approvalButtons$ = this.headerFields$.pipe(
    map((fields) => fields.find((field) => field.id === 'approvalButtons')),
    untilDestroyed(this),
    shareReplay(1),
  );
  withdrawApprovalButton$ = this.headerFields$.pipe(
    map((fields) => fields.find((field) => field.id === 'withdrawApprovalButton')),
    untilDestroyed(this),
    shareReplay(1),
  );
  hasApprovalButton$ = combineLatest([this.approvalButtons$, this.withdrawApprovalButton$]).pipe(
    auditTime(0),
    map(([approvalButtons, withdrawApprovalButton]) => approvalButtons || withdrawApprovalButton),
    map((button) => !!button),
  );

  showApprovalButton$ = combineLatest([this.view$, this.hasApprovalButton$]).pipe(
    map(([view, hasApprovalButton]) => view && hasApprovalButton),
  );

  readonly acknowledgeButton$ = this.headerFields$.pipe(
    map((fields) => fields.find((field) => field.id === 'acknowledgeButton')),
    untilDestroyed(this),
    shareReplay(1),
  );

  readonly acknowledgedButtonLabel$ = this.acknowledgeButton$.pipe(map((field) => field?.label));

  expandToolbar$ = this.documentDefineQuery.select('expandToolbar').pipe(
    filter((expandToolbar) => !!expandToolbar),
    map(({ menuGroups }) => menuGroups),
    map((menuGroups) => this.documentDefineQuery.filterMenuGroups(menuGroups)),
  );

  expandToolbarAction$: Observable<MenuItem[]> = this.expandToolbar$.pipe(
    map((actions) => actions.find((action) => action.type === 'view')?.menuItems),
  );

  hasRejectVendorChangesButton$ = this.documentDataService.getFilterGroupByConditions(this.expandToolbarAction$).pipe(
    map((viewActions) => {
      let has = false;
      viewActions?.forEach((viewAction) => {
        if (viewAction.items !== undefined) {
          viewAction.items.forEach((item) => {
            if (item.id === 'rejectVendorChangesButton') {
              has = true;
            }
          });
        }
      });
      return has;
    }),
  );

  hasAcceptVendorChangesButton$ = this.documentDataService.getFilterGroupByConditions(this.expandToolbarAction$).pipe(
    map((viewActions) => {
      let has = false;
      viewActions.forEach((viewAction) => {
        if (viewAction.items !== undefined) {
          viewAction.items.forEach((item) => {
            if (item.id === 'acceptVendorChangesButton') {
              has = true;
            }
          });
        }
      });
      return has;
    }),
  );

  hasCompareChangesButton$ = new BehaviorSubject(false);

  hasInlinePendingComparison$ = this.documentComparisonService.hasInlinePendingComparison$;

  hasAcceptOrRejectVendorChangesButtons$ = combineLatest([
    this.hasRejectVendorChangesButton$,
    this.hasAcceptVendorChangesButton$,
    this.hasInlinePendingComparison$,
  ]).pipe(
    auditTime(0),
    map(
      ([rejectButton, acceptButton, hasInlinePendingComparison]) =>
        rejectButton || acceptButton || hasInlinePendingComparison,
    ),
    map((button) => !!button),
  );

  subjectId: string;

  subject$: Observable<string> = this.headerFields$.pipe(
    map((fields) => fields.find((field) => field.id === 'subject')),
    filter((fields) => !!fields),
    map((fields) => fields.mapping ?? ''),
    tap((id) => {
      this.subjectId = id;
    }),
    switchMap((id) => this.documentDataQuery.select(id)),
    map((value) => value ?? ''),
  );

  subjectRequiredFlag$ = this.headerFields$.pipe(
    map((fields) => fields?.find((field) => field.id === 'subject')),
    filter((fields) => !!fields),
  );

  actionLoading$ = new BehaviorSubject(false);

  isRefreshItems$ = new BehaviorSubject(false);

  showDataLoadingIndicator$ = combineLatest([
    this.onlyDataLoading$,
    this.noErrorMessage$,
    this.errorMessage$.pipe(startWith('')),
    this.actionLoading$,
  ]).pipe(map(([dataLoading, errorMessage, actionLoading]) => (dataLoading && !errorMessage) || actionLoading));

  actions$ = this.documentDefineQuery.selectFilteredMenuGroups();
  viewActions$: Observable<MenuItem[]> = this.actions$.pipe(
    map((actions) => actions.find((action) => action.type === 'view').menuItems),
  );
  filterViewActions$ = this.documentDataService.getFilterGroupByConditions(this.viewActions$).pipe(
    map((viewActions) =>
      viewActions
        .map((viewAction) => {
          if (viewAction.items) {
            const filteredItems = viewAction.items.filter((item) => {
              if (item.id === 'initializeCpm') {
                this.showCpmButton$.next(true);
                return false;
              }
              if (item.id === 'replaceBom') {
                this.showReplaceBomButton = true;
                return false;
              }
              return true;
            });
            return { ...viewAction, items: filteredItems };
          }
          return viewAction;
        })
        .filter((viewAction) => {
          if (viewAction.id === 'initializeCpm') {
            this.showCpmButton$.next(true);
            return false;
          }
          if (viewAction.type === 'menuGroup' && (!viewAction.items || viewAction.items.length === 0)) {
            return false;
          }
          return true;
        }),
    ),
  );

  amendActions$ = this.actions$.pipe(
    map((actions) => actions.find((action) => action.type === 'amend')?.menuItems),
    map((items) =>
      items?.map((menuItem) =>
        menuItem.action === 'DiscardDoc' ? { ...menuItem, style: '' } : { ...menuItem, style: 'primary' },
      ),
    ),
    switchMap((items) =>
      this.dataLoading$.pipe(
        map((dataLoading) => {
          if (dataLoading) {
            return items?.map((item) => ({ ...item, disabled: item.action !== 'Cancel' }));
          }
          return items;
        }),
      ),
    ),
  );
  filterAmendActions$ = this.documentDataService.getFilterGroupByConditions(this.amendActions$);

  copyAmendActions$ = this.actions$.pipe(
    map((actions) => actions.find((action) => action?.type === 'copyAmend')?.menuItems || []),
    map((items) =>
      items.map((menuItem) =>
        menuItem?.action === 'DeactivateCopyMode' ? { ...menuItem, style: '' } : { ...menuItem, style: 'primary' },
      ),
    ),
  );
  filterCopyAmendActions$ = this.documentDataService.getFilterGroupByConditions(this.copyAmendActions$);

  partialCopyActions$ = this.actions$.pipe(
    map((actions) => actions.find((action) => action?.type === 'partialCopy')?.menuItems || []),
    map((items) =>
      items.map((menuItem) =>
        menuItem?.action === 'DeactivateCopyMode' ? { ...menuItem, style: '' } : { ...menuItem, style: 'primary' },
      ),
    ),
  );
  filterPartialCopyActions$ = this.documentDataService.getFilterGroupByConditions(this.partialCopyActions$);

  footerActionsExist$ = this.documentStatusService.getDocumentStatus$().pipe(
    map((status) => status !== DocumentStatus.View),
    untilDestroyed(this),
    shareReplay(1),
  );

  footerActions$ = this.documentHook.genFooterActions$(
    this.filterCopyAmendActions$,
    this.filterPartialCopyActions$,
    this.filterAmendActions$,
  );

  businessReference$ = this.header$.pipe(
    map((header) => header.fields.find((field) => field.id === 'title')),
    filter<DocHeaderField>(isNotNil),
    distinctUntilChanged<DocHeaderField>((a: DocHeaderField, b: DocHeaderField) => a.mapping === b.mapping),
    switchMap((titleField) => this.documentDataQuery.select(titleField.mapping)),
    distinctUntilChanged(),
  );

  docVersion$ = this.documentDataQuery
    .select('version')
    .pipe(map((version) => (isNotNil(version) && this.documentHook.hasDocumentVersion ? version : null)));
  docEditingStatus$ = this.documentDataQuery.select('editingStatus').pipe(map((status) => status));
  docStatus$ = this.documentDataQuery.select('docStatus');
  docCreatedBy$ = this.documentDataQuery.select('createUserName');
  docCreatedOn$ = this.documentDataQuery.select('createdOn');
  docId$ = this.documentDataQuery.select('id').pipe(distinctUntilChanged());
  vpoItemIsLatest$ = this.documentDataQuery
    .select('vpoItemIsLatest')
    .pipe(filter((vpoItemIsLatest) => isNotEmptyOrNil(vpoItemIsLatest)));
  isHasRefreshItemAccess$ = this.documentDataQuery
    .select('isHasRefreshItemAccess')
    .pipe(filter((isHasRefreshItemAccess) => isNotEmptyOrNil(isHasRefreshItemAccess)));
  isDocLock$ = this.documentDataQuery.select('isDocLock').pipe(filter((isDocLock) => isNotEmptyOrNil(isDocLock)));

  displayDocVersion$ = combineLatest([this.edit$, this.docEditingStatus$, this.docVersion$]).pipe<number>(
    map(([isEditStatus, editingStatus, version]) => {
      if (isNotNil(version) && isEditStatus && editingStatus === 'confirmed') {
        return version + 1;
      }

      return version;
    }),
  );

  compareStatus$ = new BehaviorSubject<string>('');

  displayEditingStatus$ = combineLatest([this.edit$, this.docEditingStatus$]).pipe<'draft' | 'confirmed' | 'pending'>(
    map(([isEditStatus, editingStatus]) => {
      if (isEditStatus && editingStatus === 'confirmed') {
        return 'draft';
      }

      return editingStatus;
    }),
  );

  docVersionStatus$ = combineLatest([
    this.displayDocVersion$,
    this.displayEditingStatus$,
    this.translocoService.selectTranslate('document.ver'),
  ]).pipe(
    map(([displayDocVersion, displayEditingStatus, versionLabel]) => {
      const status = capitalizeLetter(this.translocoService.translate(`document.${displayEditingStatus}`));
      return isNotNil(displayDocVersion) ? `${versionLabel} ${displayDocVersion} - ${status}` : null;
    }),
  );

  refNoLabel$ = this.documentDefineQuery.select('label').pipe(
    switchMap((label) => {
      const refNoLabelFieldId$ = this.documentDefineQuery.select('refNoLabelFieldId');

      if (label === 'Shared File') {
        return refNoLabelFieldId$;
      }

      return refNoLabelFieldId$.pipe(
        filter((id) => !!id),
        switchMap((id) => this.documentDataQuery.select(id)),
      );
    }),
  );

  referenceDocument$ = combineLatest([
    this.subject$.pipe(startWith('')),
    this.moduleLabel$,
    this.docEditingStatus$,
    this.docVersion$,
    this.refNoLabel$,
  ]);

  tocChapters$ = this.documentChapterService.chapters$;
  chapters$ = this.documentChapterService.chapters$.pipe(
    map((chapters) => chapters.filter((chapter) => chapter.tabGroupType !== 'tocLabel')),
  );
  activeChapter$ = this.documentChapterService.activeChapter$;
  activeSubChapter$ = this.documentChapterService.activeSubChapter$;
  activeGridChapter$ = this.documentChapterService.activeGridChapter$;
  backendInvalidTabs$ = this.documentChapterService.backendInvalidTabs$;
  backendCompareTabs$ = this.documentComparisonService.backendCompareTabs$;
  backendInvalidSections$ = this.documentChapterService.backendInvalidSections$;
  backendCompareSections$ = this.documentComparisonService.backendCompareSections$;
  backendInvalidGridChapters$ = this.documentChapterService.backendInvalidGridChapters$;

  sectionByActionBefore = new BehaviorSubject<TabGroup | Section>(null);
  groupByActionBefore = new BehaviorSubject<TabGroup | Section>(null);
  // copy action and it from other document
  copyAction$ = this.route.queryParamMap.pipe(
    map((queryParamMap) => ({
      documentId: queryParamMap.get('documentId'),
      actionId: queryParamMap.get('actionId'),
    })),
    // only 'copy' action would need to update to state
    filter(({ documentId, actionId }) => !!documentId && actionId === 'CopyDoc'),
  );

  hasRHP$ = merge(
    // id from api document view-data
    this.documentDataQuery.select('id').pipe(filter((docId) => !!docId)),
    // id from copy from other document
    this.copyAction$,
  ).pipe(map(() => true));

  toolPanel$ = this.documentDefineQuery.select('toolPanels');

  rightHandPanels$ = this.documentDefineQuery.select('rightHandPanels');

  showRHP$ = combineLatest([this.hasRHP$, this.toolPanel$, this.dataWithoutLoading$]).pipe(
    map(([hasRHP, toolPanel, dataWithoutLoading]) => hasRHP && !toolPanel && dataWithoutLoading),
  );

  copyByDocumentId$ = this.copyAction$.pipe(
    map(({ documentId }) => documentId),
    tap((documentId) => this.documentDataService.updateData({ initializePartyByDocumentId: documentId })),
  );

  action$ = this.pendingStatusService.documentAction$;
  get action() {
    return this.pendingStatusService.documentAction$.getValue();
  }

  approvalStatusStyle: string;
  approvalStatus$ = combineLatest([
    this.documentDataQuery.select('id').pipe(distinctUntilChanged(), filter(isNotEmptyOrNil)),
    this.documentDataQuery.select('status').pipe(distinctUntilChanged()),
    this.documentDataQuery.select('editingStatus').pipe(distinctUntilChanged()),
    this.documentDataQuery.select('docStatus').pipe(distinctUntilChanged()),
  ]).pipe(
    auditTime(0),
    switchMap(([id]) => this.approvalService.getApprovalProfiles(id, true)),
    map((approvalProfiles) => {
      const latestProfile = approvalProfiles?.find((profile) => profile?.isProfileLatest);
      this.approvalStatusStyle = `approval-status-${latestProfile?.status}`;
      if (this.enableFormApprovalStatusLabel === 'Y') {
        return camelCaseToDisplay(latestProfile?.status);
      }
      return null;
    }),
  );

  statusLabelId$ = this.documentDataQuery.select('status').pipe(
    filter(isNotEmptyOrNil),
    map((status) => genStatusLabelKey(this.moduleId, status)),
  );

  integrationStatus$ = this.documentDataQuery.select('integrationStatus').pipe(
    filter(isNotEmptyOrNil),
    map((value) =>
      getPrefixLabel(
        'labelIdPrefix=lbl.integrationStatus.',
        value,
        this.translocoService.translate.bind(this.translocoService),
      ),
    ),
  );

  statusLabelBackgroundColor$ = this.configurationService.CONFIG.FLAG_APP_FIELD_BACKGROUND_COLORFUL
    ? combineLatest([
        this.fieldBackgroundColorfulService.backgroundColorMap$,
        this.documentDataQuery.select('status'),
      ]).pipe(
        map(
          ([backgroundColorMap, statusValue]) =>
            backgroundColorMap[this.moduleId]?.status?.[statusValue] ?? 'rgba(58, 70, 77, 0.72)',
        ),
      )
    : of('rgba(58, 70, 77, 0.72)');

  readonly notCreateAction$ = this.action$.pipe(
    map((action) => action !== 'create'),
    untilDestroyed(this),
    shareReplay(1),
  );
  readonly activeDocStatus$ = this.docStatus$.pipe(map((docStatus) => docStatus === 'active'));
  readonly currentRecordIndex$ = this.stateRecordService.state$.pipe(map((state) => state.rowIndex));
  readonly showNavigateSibling$ = combineLatest([this.notCreateAction$, this.currentRecordIndex$]).pipe(
    map(([notCreateAction, index]) => !this.inSecondaryRoute && notCreateAction && index !== -1),
  );
  readonly showNavigateLatestDocument$ = combineLatest([this.notCreateAction$, this.notLatestVersion$]).pipe(
    map(([notCreateAction, notLatestVersion]) => notCreateAction && notLatestVersion),
  );

  readonly showNavigateVpoItemItemNotIsLatestDocument$ = combineLatest([
    this.notLatestVersion$,
    this.activeDocStatus$,
    this.vpoItemIsLatest$,
    this.isDocLock$,
    this.edit$,
    this.isHasRefreshItemAccess$,
  ]).pipe(
    map(
      ([notLatestVersion, activeDocStatus, vpoItemIsLatest, isDocLock, edit, isHasRefreshItemAccess]) =>
        this.moduleId === 'vpo' &&
        activeDocStatus &&
        !notLatestVersion &&
        !vpoItemIsLatest &&
        (edit || !isDocLock) &&
        !this.isVendorDomain &&
        isHasRefreshItemAccess,
    ),
  );

  get docId() {
    return this.documentDataQuery.getValue().id;
  }

  get docModuleName() {
    return this.documentDefineQuery.getValue().label;
  }

  get docVersion() {
    return this.documentDataQuery.getValue().version;
  }

  get docSubject() {
    return this.documentDataQuery.getValue()[this.subjectId];
  }

  get editingStatus() {
    return this.documentDataQuery.getValue().editingStatus;
  }

  protected readonly dataAbstractLoading$ = new BehaviorSubject<boolean>(true);

  compareData$: Observable<VersionComparisonVm>;
  differentData$ = this.documentComparisonService.differentData$;
  isVendorDomain = this.authService.state.userType === 'vendor';

  enableFormApprovalStatusLabel: string;
  enableFormApprovalStatusLabel$ = this.domainAttributeService.enableFormApprovalStatusLabel$;
  documentData$ = this.documentDataQuery.select();

  alertSection$ = this.alertService.alertSection$;

  readonly validating$ = this.documentValidateService.validating$;

  private readonly destroyed$ = new Subject<void>();

  constructor(
    private readonly pendingStatusService: PendingStatusService,
    private readonly documentService: DocumentService,
    private readonly renderer: Renderer2,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly documentDataActionService: DocumentDataActionService,
    private readonly documentDataService: DocumentDataService,
    private readonly documentDataQuery: DocumentDataQuery,
    private readonly documentDefineService: DocumentDefineService,
    private readonly documentDefineQuery: DocumentDefineQuery,
    private readonly dialog: Dialog,
    private readonly location: Location,
    private readonly elementRef: ElementRef,
    private readonly documentDataCopyService: DocumentCopyDataService,
    private readonly stateRecordService: StateRecordService,
    private readonly codelistService: CodelistService,
    private readonly actionDispatcherService: DocumentActionDispatcherService,
    private readonly documentChapterService: DocumentChapterService,
    private readonly shipmentTrackingPanelStateService: ShipmentTrackingPanelStateService,
    private readonly documentStatusService: DocumentStatusService,
    private readonly documentStatusQuery: DocumentStatusQuery,
    private readonly chatSettingService: ChatSettingService,
    private readonly cpmService: CpmService,
    private readonly notificationService: NotificationService,
    private readonly inlineComparisonResultStore: InlineComparisonResultStore,
    private readonly validateTooltipService: ValidateTooltipService,
    private readonly behaviorPreferenceService: BehaviorPreferenceService,
    private readonly cbxLabelTranslateService: CbxLabelTranslateService,
    private readonly validationHandler: ValidationHandler,
    private readonly approvalService: ApprovalService,
    private readonly configurationService: ConfigurationService,
    private readonly fieldBackgroundColorfulService: FieldBackgroundColorfulService,
    private readonly apiService: ApiService,
    private readonly translocoService: TranslocoService,
    private readonly routerStateService: RouterStateService,
    private readonly documentOverlayActionService: DocumentOverlayActionService,
    private readonly documentOverlayDataService: DocumentOverlayDataService,
    private readonly domainAttributeService: DomainAttributeService,
    private readonly titleService: Title,
    private readonly refreshItemsAction: RefreshItemsAction,
    protected readonly messageDialogService: MessageDialogService,
    private readonly menuBarService: MenuBarService,
    private readonly documentComparisonService: DocumentComparisonService,
    private readonly authService: AuthService,
    private readonly documentCompareChangeQuery: DocumentCompareChangeQuery,
    private readonly documentCompareChangeService: DocumentCompareChangeService,
    private readonly inlineComparisonService: InlineComparisonService,
    private readonly documentCommentPanelService: DocumentCommentPanelService,
    private readonly documentComparisonQuery: DocumentComparisonQuery,
    private readonly alertService: AlertService,
    private readonly rightHandPanelStateService: RightHandPanelStateService,
    private readonly documentEventService: DocumentEventService,
  ) {
    this.enableFormApprovalStatusLabel$.pipe(untilDestroyed(this)).subscribe((enableFormApprovalStatusLabel) => {
      this.enableFormApprovalStatusLabel = enableFormApprovalStatusLabel;
    });
  }

  shipmentStateSubscription: Subscription;

  ngOnInit() {
    this.documentHook.handleUrlVersion(this.destroyed$, this.tabId, this.moduleId, this.refNo$);

    this.handleAbstract();

    this.registerActionNotification();

    this.loadCodelistBooksOnEdit();

    this.handleOpenApprovalPopup();

    this.handleCreateValidation();

    this.hasHistoryPage = window.history.length > 1;

    if (this.inSecondaryRoute) {
      this.documentChapterService.updateSectionIdPrefix(this.sectionIdPrefix);
    }
    if (this.moduleId === 'vpo' || this.moduleId === 'shipmentAdvice') {
      this.shipmentStateSubscription = this.shipmentTrackingPanelStateService.setDocumentData(this.documentData$);
    }

    this.handleFieldCompareChange();

    this.initProposedChanges();
  }

  ngAfterViewInit() {
    this.handleSectionWrapperHeight();

    this.handleFetchDefineAndDataError();

    this.footerActionsExist$
      .pipe(untilDestroyed(this))
      .subscribe((exist) => this.chatSettingService.setBubbleOffsetY(exist ? -52 : 0));

    this.referenceDocument$.pipe(auditTime(0), untilDestroyed(this)).subscribe({
      next: ([name, moduleLabel, editingStatus, version, refNoLabel]) =>
        this.chatSettingService.documentReference$.next({
          name,
          moduleLabel,
          editingStatus,
          version,
          module: this.moduleId,
          refNo: this.refNo,
          refNoLabel,
        }),
    });

    this.documentStatusService.registerDeactivateLogic();

    this.route.queryParamMap
      .pipe(
        take(1),
        filter((queryParamMap) => !!queryParamMap.get('enterEditMode')),
        switchMap(() => this.isLatestVersion$),
        switchMap((isLatestVersion) => {
          if (isLatestVersion) {
            return of('enterEditMode');
          }
          return this.documentHook.leaveEditMode(this.refNo, this.moduleId);
        }),
        take(1),
        filter((enterEditMode) => enterEditMode === 'enterEditMode'),
        untilDestroyed(this),
      )
      .subscribe({
        next: () => {
          this.onMenuClick({
            action: 'AmendDoc',
            actionId: '',
            label: '',
            id: '',
            type: 'menuItem',
            actionParams: {},
          } as MenuItem);
        },
      });
  }

  ngOnDestroy() {
    if (this.inSecondaryRoute) {
      this.documentOverlayDataService.documentDataBus$.next(this.documentDataQuery.getValue());
    }
    this.documentDataCopyService.clearData();
    this.validateTooltipService.closeTooltip();
    this.unlock(this.moduleId, this.refNo);

    this.destroyed$.next();
    this.destroyed$.complete();
    this.moduleLabel$.pipe(take(1)).subscribe(() => {
      this.titleService.setTitle('CBX Cloud');
    });

    this.shipmentStateSubscription?.unsubscribe();
  }

  private startEditDocument(moduleId: string, refNo: string) {
    if (!refNo) {
      return;
    }

    this.documentHook.startEditDocument(refNo, moduleId);
  }

  private unlock(moduleId: string, refNo: string, isPersistAction?: boolean) {
    if (
      (!this.isInEditView && moduleId !== 'lineSheet') ||
      (!refNo && this.moduleId !== 'vqce') ||
      (!this.isInEditView && !this.concurrentPendingChangesHook.isConcurrentMode)
    ) {
      return;
    }

    this.documentHook.stopEditDocument(refNo, moduleId, isPersistAction);
  }

  hideSidebar() {
    this.newTocCollapse$.next(true);
    return this.updateSideBarSetting(true).pipe(untilDestroyed(this)).subscribe();
  }

  openSidebar() {
    this.newTocCollapse$.next(false);
    return this.updateSideBarSetting(false).pipe(untilDestroyed(this)).subscribe();
  }

  private updateSideBarSetting(value: boolean) {
    return this.behaviorPreferenceService.updateModuleScopeSetting(this.moduleId, { tocCollapse: value });
  }

  scrollToChapter(chapter: TabGroup | Section) {
    this.documentChapterService
      .scrollToChapter(chapter)
      ?.pipe(filter((addedHeight) => addedHeight))
      .subscribe((addedHeight) => this.handleSpacer(addedHeight));
  }

  scrollToGridChapter(gridChapter: any) {
    this.documentChapterService
      .directlyScrollToChapter(gridChapter)
      ?.pipe(filter((addedHeight) => addedHeight))
      .subscribe((addedHeight) => this.handleSpacer(addedHeight));
  }

  onChapterEnter(data: { chapter: TabGroup; subChapter: Section }) {
    this.documentChapterService.onChapterEnter(data);
  }

  goBack() {
    this.documentHook.pageGoBack(this.destroyed$, this.tabId, this.moduleId);
  }

  onMenuClick(menu: MenuItem) {
    if (menu.id === 'compareQuotations') {
      this.compareQuotations();
      return;
    }
    if (this.actionLoading$.getValue()) {
      return;
    }
    this.actionLoading$.next(true);
    const params = {
      documentData$: this.documentDataQuery.documentViewData$,
      docModuleName: this.docModuleName,
      docSubject: this.docSubject,
      docId: this.docId,
      docVersion: this.docVersion,
      editingStatus: this.editingStatus,
      docAction: this.action,
      displayDocVersion$: this.displayDocVersion$,
      tabId: this.tabId,
      moduleId: this.moduleId,
      refNo: this.refNo,
      elementRef: this.elementRef,
      dataTransferFromStoreToApiFn: this.documentDataActionService.dataTransferFromStoreToApi.bind(this),
      hasDocumentVersion: this.documentHook.hasDocumentVersion,
      showReplaceBomButton: this.showReplaceBomButton,
      startEditDocument: () => this.startEditDocument(this.moduleId, this.refNo),
      stopEditDocument: () => this.unlock(this.moduleId, this.refNo, menu?.isPersistAction),
    };

    this.actionDispatcherService
      .execute(menu, params)
      .pipe(
        take(1),
        untilDestroyed(this),
        finalize(() => {
          this.actionLoading$.next(false);
        }),
      )
      .subscribe({
        next: () => {
          this.groupByActionBefore.next(this.activeChapter$.getValue());
          this.sectionByActionBefore.next(this.activeSubChapter$.getValue());
        },
        error: (error) => {
          if (error && this.authService.notOpen2MFAPopup$.getValue()) {
            this.notificationService.open({
              message: error?.errorMessage || error?.message || error?.error?.errorMessage,
              type: 'warn',
              position: 'right',
            });
          }
        },
      });
  }

  private compareQuotations() {
    const { refNo } = this;
    this.dialog.open(CompareQuotationsDialogComponent, {
      ...defaultDialogConfig,
      maxWidth: '98vw',
      height: '100%',
      width: '100%',
      data: {
        moduleId: this.moduleId,
        refNo,
      },
    });
  }

  openApprovalActionDialog(action: ApprovalActions) {
    const params = {
      documentData$: this.documentDataQuery.documentViewData$,
      docModuleName: this.docModuleName,
      docSubject: this.docSubject,
      docId: this.docId,
      docVersion: this.docVersion,
      editingStatus: this.editingStatus,
      docAction: this.action,
      displayDocVersion$: this.displayDocVersion$,
      tabId: this.tabId,
      moduleId: this.moduleId,
      refNo: this.refNo,
      dataTransferFromStoreToApiFn: this.documentDataActionService.dataTransferFromStoreToApi.bind(this),
      differentData: this.documentComparisonQuery.getValue().documentComparison,
    };

    const menu = {
      actionId: action,
      action,
      id: undefined,
      type: undefined,
      label: undefined,
      actionParams: undefined,
    } as MenuItem;

    this.actionDispatcherService
      .execute(menu, params)
      .pipe(untilDestroyed(this))
      .subscribe({
        error: (error) => {
          console.error(error);
          this.notificationService.open({ message: error?.error?.message || error, type: 'warn', position: 'right' });
        },
      });
  }

  openConfirmAcknowledgeDialog() {
    this.messageDialogService
      .openDialog('ConfirmCancel', 'By clicking "Acknowledge" I confirm that I have reviewed this document.', 'warning')
      .closed.pipe(
        filter((result?: any) => result?.payload),
        switchMap(() => this.apiService.patch(CBX_URL.markDocAsAcknowledged(this.moduleId, this.refNo), {})),
        untilDestroyed(this),
      )
      .subscribe(() => {
        this.documentDefineService.refetchDefine(this.moduleId, this.refNo, of(this.docVersion.toString())).subscribe();

        const refetchDataResolved: Partial<DocumentDataResolved> = {
          action: 'view',
          data$: this.documentService.getViewData(this.moduleId, this.refNo),
        };
        this.documentEventService.refetchData$.next(refetchDataResolved);
      });
  }

  redirectToLatestVersion() {
    this.router.navigate(['/document', this.tabId, this.moduleId, this.refNo]).then();
    this.vpoItemIsLatest$ = this.documentDataQuery.select('vpoItemIsLatest');
  }

  vpoItemItemNotIsLatestVersion() {
    const vpoItemList = deepClone(this.documentDataQuery.getValue().vpoItemList) as Vpo_VpoItemDto[];
    const isRefreshToLatestMessage = true;
    if (vpoItemList.length === 0) {
      this.messageDialogService.openDialog('Okay', 'There is no any item record defined under the VPO.', 'warning');
    } else if (isRefreshToLatestMessage) {
      this.messageDialogService
        .openDialog(
          'OkayCancel',
          'You are about to refresh all the linked item(s) to the latest version. Press "OK" to continue, or "Cancel" to discard the refresh action.',
          'warning',
        )
        .closed.pipe(
          filter((result?: any) => result?.payload),
          map(() => true),
        )
        .subscribe((result: boolean) => {
          if (result) {
            this.isRefreshItems$.next(true);
            let refreshMessage;
            this.refreshItemsAction.menuBarService
              .refreshItems(this.documentDataQuery.getValue(), null)
              .pipe(
                tap((params) => {
                  refreshMessage = params;
                }),
                map((params) => this.documentDataService.doSideEffect(params.data)),
                tap((refreshData) => {
                  this.documentDataService.updateData({
                    vpoItemCsDto: refreshData.vpoItemCsList ? R.unnest(refreshData.vpoItemCsList) : undefined,
                    vpoItemCsList: refreshData.vpoItemCsList,
                    vpoItemList: refreshData.vpoItemList,
                    vpoShipDtlCsDto: refreshData.vpoShipDtlCsDto,
                    vpoShipDtlDtoGroupList: refreshData.vpoShipDtlDtoGroupList,
                  });
                }),
                switchMap((refreshData) => this.menuBarService.saveAfterRefresh(refreshData, null)),
                catchError((errorWrapper: ErrorWrapper) => {
                  this.documentDefineService.resetUiState();

                  const validationErrors = errorWrapper.error.validationErrors as Validation_ValidationFailedDto[];
                  if (validationErrors) {
                    this.validationHandler.handleError(errorWrapper.error.validationErrors);
                  }

                  this.documentChapterService.scrollToFirstBackEndErrorSection();

                  this.isRefreshItems$.next(false);

                  return throwError(() => errorWrapper);
                }),
              )
              .toPromise()
              .then(() => {
                this.refreshItemsAction.openRefreshedDialogMessage(refreshMessage);
                this.router.navigate(['/document', this.tabId, this.moduleId, this.refNo]).then();
                this.ngOnInit();
              })
              .catch((error) => {
                this.refreshItemsAction.openRefreshedDialogMessage(refreshMessage);
                if (refreshMessage.alertDialogType !== 'Error' && error?.error?.validationErrors) {
                  this.documentStatusService.setDocumentStatus(DocumentStatus.Edit);
                  this.documentDataService.updateData({ isHasRefreshItem: true, vpoItemIsLatest: true });
                }
              })
              .finally(() => this.isRefreshItems$.next(false));
          }
        });
    } else {
      this.isRefreshItems$.next(true);
      this.refreshItemsAction.menuBarService
        .refreshItems(this.documentDataQuery.getValue(), null)
        .pipe(
          catchError((errorWrapper: ErrorWrapper) => {
            this.documentDefineService.resetUiState();

            const validationErrors = errorWrapper.error.validationErrors as Validation_ValidationFailedDto[];
            if (validationErrors) {
              this.validationHandler.handleError(errorWrapper.error.validationErrors);
            }

            this.documentChapterService.scrollToFirstBackEndErrorSection();

            this.isRefreshItems$.next(false);

            return throwError(() => errorWrapper);
          }),
        )
        .toPromise()
        .then((params) => {
          if (params.messageStatus === 'UnRelease') {
            this.refreshItemsAction.openRefreshedDialogMessage(params);
            if (params.editingStatus === 'confirmed') {
              this.router.navigate(['/document', this.tabId, this.moduleId, this.refNo]).then();
            }
          } else {
            this.refreshItemsAction.openRefreshedDialogMessage(params);
          }
          this.ngOnInit();
        })
        .catch(() => {
          this.documentStatusService.setDocumentStatus(DocumentStatus.Edit);
          this.documentDataService.updateData({ isHasRefreshItem: true });
        })
        .finally(() => this.isRefreshItems$.next(false));
    }
    return of(true);
  }

  private registerActionNotification() {
    this.documentService.actionNotification
      .pipe(untilDestroyed(this))
      .subscribe((notificationEvent: NotificationEvent) => {
        if (notificationEvent?.action === 'closeAllOverlays') {
          this.overlays.forEach((overlay) => overlay.close());
          this.overlays = [];
        }
      });
  }

  initCpm() {
    if (this.initializing$.getValue()) {
      return;
    }
    this.initializing$.next(true);
    this.cpmService
      .initCpmDoc(this.moduleId, this.refNo)
      .pipe(
        finalize(() => this.initializing$.next(false)),
        untilDestroyed(this),
      )
      .subscribe({
        next: () => {
          this.notificationService.open({
            message: this.translocoService.translate('document.CPMalreadyinitiated'),
            type: 'hint',
            duration: 6000,
          });
          this.documentDataService.updateData({ isCpmInitialized: true });
          this.openCpmBar();
          this.showInitializeCpm$.next(false);
          this.cpmInitialized$.next();
          this.initCpmEvent.emit();
        },
        error: (error) => {
          const message = error.error.messageLabelId
            ? this.cbxLabelTranslateService.getHardCodeLabel(
                error.error.messageLabelId,
                error?.error?.interpolateParams,
                true,
              )
            : error.error.message;
          this.openMessageDialog('Okay', '230px', message, 'information');
        },
      });
  }

  private openCpmBar() {
    this.showCpmBar$.next(true);
  }

  switchCpmBar() {
    this.showCpmBar$.next(!this.showCpmBar$.getValue());
  }

  private loadCodelistBooksOnEdit() {
    this.edit$
      .pipe(
        filter((isEditStatus) => isEditStatus),
        switchMap(() => this.documentDefineQuery.selectAllFields()),
        filter((allFields) => !isEmptyOrNil(allFields)),
        switchMap((allFields) => this.loadAllCodelistBooksOfFieldDefine(allFields)),
        take(1),
        untilDestroyed(this),
      )
      .subscribe();
  }

  private loadAllCodelistBooksOfFieldDefine(allFields: FieldDefine[]) {
    const codelistBookNames: string[] = R.pipe(
      R.map((field: FieldDefine) => field),
      R.filter((field: FieldDefine) => !field.isDynamicCodelist && field.popup !== 'true'),
      R.map((field: FieldDefine) => field.codelistName),
      R.filter((codelistName) => !!codelistName),
      R.uniq,
    )(allFields);
    return this.codelistService.fetchCodelistsByNameWithCache(codelistBookNames);
  }

  private handleAbstract() {
    combineLatest([this.docId$, this.refNo$])
      .pipe(
        auditTime(0),
        tap(() => {
          this.dataAbstractLoading$.next(true);
        }),
        switchMap(([docId, refNo]) =>
          this.getDataAbstract$(docId, refNo).pipe(
            catchError(() => of({ dataAbstract: '' } as DataAbstract)),
            finalize(() => this.dataAbstractLoading$.next(false)),
          ),
        ),
        untilDestroyed(this),
      )
      .subscribe((abstract) => {
        this.backgroundColor = abstract?.backgroundColor;
        this.fontColor = abstract?.fontColor;
        this.moduleValue = abstract?.moduleLabel;
        this.markChange = !(isEmptyOrNil(this.backgroundColor) && isEmptyOrNil(this.fontColor));
        this.dataAbstract = abstract?.dataAbstract;

        this.cdr.detectChanges();

        this.updateTitleByAbstract(abstract);
      });
  }

  private updateTitleByAbstract(abstract: DataAbstract) {
    let title: string = abstract?.title;

    if (isEmptyOrNil(title)) {
      const abstractText = abstract?.dataAbstract || this.refNo;
      const prefix = isNotEmptyOrNil(abstractText) ? `${abstractText} - ` : '';

      title = `${prefix}CBX Cloud`;
    }

    this.titleService.setTitle(title);
  }

  private handleFetchDefineAndDataError() {
    const translocoMessage$ = this.translocoService.selectTranslate<string>('document.doyouwanttogobacktopreviouspage');

    let dialogRef: DialogRef<any>;

    merge(this.defineErrorMessage$, this.dataErrorMessage$)
      .pipe(
        switchMap((errorMessage: string) =>
          translocoMessage$.pipe(map((message: string) => `${errorMessage ?? ''}\n${message}`)),
        ),
        switchMap((message) => {
          if (dialogRef) {
            dialogRef.close();
          }
          dialogRef = this.openMessageDialog('YesCancel', '210px', message, 'warning');
          return dialogRef.closed;
        }),
        filter((result) => result?.type === 'done'),
        untilDestroyed(this),
      )
      .subscribe(() => this.location.back());
  }

  private handleOpenApprovalPopup() {
    combineLatest([this.approvalButtons$, this.defineReady$, this.dataReady$])
      .pipe(
        filter(([headerField]) => headerField?.id === 'approvalButtons'),
        filter(([, defineReady, dataReady]) => defineReady && dataReady),
        take(1),
        untilDestroyed(this),
      )
      .subscribe(() => {
        if (this.openApprovePopup) {
          this.openApprovalActionDialog('approve');
        }

        if (this.openRejectPopup) {
          this.openApprovalActionDialog('reject');
        }
      });
  }

  scrollElementReady(scrollElement: Element) {
    this.documentChapterService.setScrollElement(scrollElement);
  }

  verticalSpacerReady(verticalSpacer: Element) {
    this.verticalSpacer = verticalSpacer;
  }

  chapterReady() {
    const tabGroup: TabGroup | Section = this.groupByActionBefore.getValue();
    if (tabGroup?.id !== 'tabHeader') {
      this.scrollToChapter(tabGroup);
      this.scrollToChapter(this.sectionByActionBefore.getValue());
    }
  }

  private handleSpacer(addedHeight: number) {
    this.spacerHeight = addedHeight + this.spacerHeight;
    this.renderer.setStyle(this.verticalSpacer, 'transform', `translateY(${this.spacerHeight}px)`);
    this.cdkScrollable.scrollTo({ bottom: 0 });
    this.documentChapterService.handleDelayScrollToChapter();

    if (this.spacerScrollSub) {
      this.spacerScrollSub.unsubscribe();
    }

    this.spacerScrollSub = this.cdkScrollable
      .elementScrolled()
      .pipe(
        map(() => this.cdkScrollable.measureScrollOffset('top')),
        pairwise(),
        map(([oldTop, newTop]) => oldTop - newTop),
        filter((scrollTop) => scrollTop > 0),
        takeUntil(this.spacerHeightZero$.asObservable()),
        untilDestroyed(this),
      )
      .subscribe((scrollTop) => {
        this.spacerHeight = Math.max(0, this.spacerHeight - scrollTop);

        this.renderer.setStyle(this.verticalSpacer, 'transform', `translateY(${this.spacerHeight}px)`);

        if (!this.spacerHeight) {
          this.spacerHeightZero$.next(true);
        }
      });
  }

  private openMessageDialog(
    messageButtons: DialogButtonTypes,
    _height: string,
    message: string,
    title: AlertDialogTypes,
  ) {
    return this.dialog.open<any>(AlertDialogComponent, {
      ...generalAlertDialogConfig,
      data: {
        messageButtons,
        message,
        title,
      },
    });
  }

  private getDataAbstract$(docId: string, refNo: string): Observable<DataAbstract> {
    if (isEmptyOrNil(this.moduleId) || isEmptyOrNil(docId) || isEmptyOrNil(this.docVersion)) {
      return of({ dataAbstract: '', backgroundColor: '', fontColor: '', moduleLabel: '', title: '' });
    }

    const url =
      isNotEmptyOrNil(this.moduleId) && isEmptyOrNil(refNo)
        ? CBX_URL.getDataAbstractByModule(this.moduleId)
        : CBX_URL.getDataAbstract(this.moduleId, this.refNo, this.docVersion);

    return this.apiService.get<DataAbstract>(url);
  }

  private handleSectionWrapperHeight() {
    combineLatest([this.showCpmBar$, this.footerActionsExist$, this.copy$, this.alertSection$])
      .pipe(
        distinctUntilChanged(deepEqual),
        tap(([hasCpmBar, hasFooterActionsExist, copy, alertSection]) => {
          const heightMap = { hasCpmBar: 30, hasFooterActionsExist: 52, copy: 24, alertSection: 0 };
          if (this.alertSection && this.alertSection.nativeElement) {
            const { clientHeight } = this.alertSection.nativeElement;
            if (clientHeight && clientHeight > 0) {
              heightMap.alertSection = clientHeight + 4;
            }
          }
          let totalHeight = 70;
          Object.entries({ hasCpmBar, hasFooterActionsExist, copy, alertSection }).forEach(([key, value]) => {
            if (value) {
              totalHeight += heightMap[key];
            }
          });
          if (this.sectionWrapper && this.sectionWrapper.nativeElement) {
            this.renderer.setStyle(this.sectionWrapper.nativeElement, 'height', `calc(100% - ${totalHeight}px)`);
          }
          // this.sectionWrapper.nativeElement.style.height = `calc(100% - ${totalHeight}px)`;
        }),
        untilDestroyed(this),
      )
      .subscribe();
  }

  cancelAll() {
    this.documentDataCopyService.clearData();
    this.documentStatusService.deselectAllCopy();
  }

  selectAll() {
    this.documentDataCopyService.clearData();
    this.documentStatusService.selectAllCopy();
  }

  cancelAllPartialCopy() {
    this.documentDataCopyService.clearData();
    this.documentStatusService.deselectAllPartialCopy();
  }

  selectAllPartialCopy() {
    this.documentDataCopyService.clearData();
    this.documentStatusService.selectAllPartialCopy();
  }

  showCpmButton(hasCpmMilestones: boolean) {
    if (!this.showInitializeCpm$.getValue()) {
      this.showCpmButton$.next(hasCpmMilestones);
    }
  }

  closeOverlay() {
    this.documentOverlayActionService.action$.next('closeOverlay');
  }

  openIntegration() {
    this.dialog.open(ActivityDialogComponent, {
      ...defaultDialogConfig,
      width: '75%',
      height: '75%',
      data: {
        moduleId: this.moduleId,
        refNo: this.refNo,
        firstLoadIntegration: true,
      },
    });
  }

  openApproval() {
    const approvalMenu = { id: 'approval', action: 'OpenApprovalByDoc', type: 'menuItem' } as MenuItem;
    this.onMenuClick(approvalMenu);
  }

  openVendorChangeProposedActionDialog(accept: boolean) {
    const params = {
      documentData$: this.documentDataQuery.documentViewData$,
      docModuleName: this.docModuleName,
      docSubject: this.docSubject,
      docId: this.docId,
      docVersion: this.docVersion,
      editingStatus: this.editingStatus,
      docAction: this.action,
      displayDocVersion$: this.displayDocVersion$,
      tabId: this.tabId,
      moduleId: this.moduleId,
      refNo: this.refNo,
      accept,
      differentData: this.differentData$.getValue(),
      dataTransferFromStoreToApiFn: this.documentDataActionService.dataTransferFromStoreToApi.bind(this),
    };

    const action = 'AcceptAndRejectVendorChanges';
    const menu = {
      actionId: action,
      action,
      id: undefined,
      type: undefined,
      label: undefined,
      actionParams: undefined,
    } as MenuItem;

    this.actionDispatcherService
      .execute(menu, params)
      .pipe(untilDestroyed(this))
      .subscribe({
        next: () => {},
        error: (error) => {
          console.error(error);
          this.notificationService.open({ message: error?.error?.message || error, type: 'warn', position: 'right' });
        },
      });
  }

  openInlineActionDialog(accept: boolean) {
    const httpParams = new HttpParams().set('docId', this.inlineComparisonService.currentDocId);
    this.apiService.get<any>(CBX_URL.checkCurrentDocLockedByOthers, { params: httpParams }).subscribe((result) => {
      const lockedUserName = result[this.inlineComparisonService.currentDocId];
      if (lockedUserName) {
        this.messageDialogService.openDialog(
          'Okay',
          `Document is currently being amended by ${lockedUserName}. Please try again later`,
          'warning',
        );
      } else {
        const params = {
          documentData$: this.documentDataQuery.documentViewData$,
          docModuleName: this.docModuleName,
          docSubject: this.docSubject,
          docId: this.docId,
          docVersion: this.docVersion,
          editingStatus: this.editingStatus,
          docAction: this.action,
          displayDocVersion$: this.displayDocVersion$,
          tabId: this.tabId,
          moduleId: this.moduleId,
          refNo: this.refNo,
          accept,
          differentData: this.documentComparisonQuery.getValue().documentComparison,
          dataTransferFromStoreToApiFn: this.documentDataActionService.dataTransferFromStoreToApi.bind(this),
        };

        const action = 'AcceptAndRejectComparison';
        const menu = {
          actionId: action,
          action,
          id: undefined,
          type: undefined,
          label: undefined,
          actionParams: undefined,
        } as MenuItem;

        this.actionDispatcherService
          .execute(menu, params)
          .pipe(untilDestroyed(this))
          .subscribe({
            next: () => {},
            error: (error) => {
              console.error(error);
              this.notificationService.open({
                message: error?.error?.message || error,
                type: 'warn',
                position: 'right',
              });
            },
          });
      }
    });
  }

  handleCreateValidation() {
    this.amendActions$.pipe(distinctUntilChanged()).subscribe((menuItems) => {
      const amendActions = [];
      if (this.action === 'create') {
        menuItems?.forEach((menuItem) => {
          if (isNotEmptyOrNil(menuItem?.actionParams?.action)) {
            amendActions.push(menuItem?.actionParams?.action);
          }
        });
        const params = {
          refNo: '',
          actions: amendActions,
          module: this.moduleId,
        };
        this.apiService
          .post<void>('api/validations/amendvalidate', null, { params })
          .pipe(
            catchError((errorWrapper: ErrorWrapper) => {
              this.documentDefineService.resetUiState();
              const validationErrors = errorWrapper.error.validationErrors as ValidationError[];
              if (validationErrors) {
                this.validationHandler.validationErrors = validationErrors;
                this.validationHandler.handleError(errorWrapper.error.validationErrors);
              }
              return throwError(() => errorWrapper);
            }),
          )
          .subscribe();
      }
    });
  }

  initProposedChanges() {
    combineLatest([this.documentDataQuery.select('refNo'), this.approvalStatus$])
      .pipe(distinctUntilChanged(deepEqual), debounceTime(100), untilDestroyed(this))
      .subscribe(([refNo, approvalStatus]) => {
        this.handleProposedChangesToggle(refNo, null, approvalStatus);
        this.documentComparisonService.doVendorAndFactChangeCompare(this.documentDataQuery.getValue(), approvalStatus);
        this.documentComparisonService.doVpoVendorChangeCompare(this.documentDataQuery.getValue());
        this.handleProposedChangesToggle(refNo, null);
      });
  }

  changeProposedChanges(slideToggleChange: boolean, approvalStatus?: string) {
    this.documentDataService.updateData({ showProposedChanges: slideToggleChange });
    this.handleProposedChangesToggle(this.refNo, slideToggleChange, approvalStatus);
  }

  handleProposedChangesToggle(refNo: string, showProposedChanges?: boolean, approvalStatus?: string) {
    if (isEmptyOrNil(refNo)) {
      return;
    }

    if (
      (isEmptyOrNil(approvalStatus) || approvalStatus !== 'Pending') &&
      !this.inlineComparisonService.hasInlinePending$.getValue()
    ) {
      this.documentDataService.updateData({ showProposedChanges: false });
      this.documentDataService.updateData({ showProposedChangesToggle: false });
      this.documentComparisonService.resetData();
      return;
    }

    this.apiService
      .post<any>(CBX_URL.currentUserShowProposedChanges, { refNo, showProposedChanges } as any)
      .pipe(untilDestroyed(this))
      .subscribe((userProposedChange) => {
        if (isEmptyOrNil(showProposedChanges)) {
          this.documentDataService.updateData({ showProposedChanges: userProposedChange.showProposedChanges });
        }
      });
  }

  private handleFieldCompareChange() {
    this.documentCompareChangeQuery
      .select()
      .pipe(untilDestroyed(this))
      .subscribe((fieldCompareChange) => {
        if (fieldCompareChange?.documentCompareChange) {
          this.hasCompareChangesButton$.next(fieldCompareChange?.documentCompareChange.isChange);
        }
      });
  }

  unDoAllChanges() {
    this.documentCompareChangeService.resetData();
    this.hasCompareChangesButton$.next(false);
    this.inlineComparisonResultStore.reset();
  }

  submitAcceptOrRejectChanges() {
    const httpParams = new HttpParams().set('docId', this.inlineComparisonService.currentDocId);
    this.apiService.get<any>(CBX_URL.checkCurrentDocLockedByOthers, { params: httpParams }).subscribe((result) => {
      const lockedUserName = result[this.inlineComparisonService.currentDocId];
      if (lockedUserName) {
        this.messageDialogService.openDialog(
          'Okay',
          `Document is currently being amended by ${lockedUserName}. Please try again later`,
          'warning',
        );
      } else {
        const params = {
          documentData$: this.documentDataQuery.documentViewData$,
          docModuleName: this.docModuleName,
          docSubject: this.docSubject,
          docId: this.docId,
          docVersion: this.docVersion,
          editingStatus: this.editingStatus,
          docAction: this.action,
          displayDocVersion$: this.displayDocVersion$,
          tabId: this.tabId,
          moduleId: this.moduleId,
          refNo: this.refNo,
          dataTransferFromStoreToApiFn: this.documentDataActionService.dataTransferFromStoreToApi.bind(this),
        };

        const action = 'SubmitAcceptOrRejectChanges';
        const menu = {
          actionId: action,
          action,
          id: undefined,
          type: undefined,
          label: undefined,
          actionParams: undefined,
        } as MenuItem;

        this.actionDispatcherService
          .execute(menu, params)
          .pipe(untilDestroyed(this))
          .subscribe({
            next: () => {},
            error: (error) => {
              console.error(error);
              this.notificationService.open({
                message: error?.error?.message || error,
                type: 'warn',
                position: 'right',
              });
            },
          });
      }
    });
  }
}
