import { Dialog } from '@angular/cdk/dialog';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostBinding, Input, OnInit } from '@angular/core';

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject, defer, Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';

import { UploadType } from '../../../../entities/upload-type';
import { DropZoneComponent } from '../../../shared/common/drop-zone/drop-zone.component';
import { AttachmentSectionContentComponent } from '../attachment-section-content/attachment-section-content.component';
import { DocumentDataQuery, DocumentDataService, DocumentDefineQuery, Section } from '../state';
import { AddUrlDialogComponent } from './add-url-dialog/add-url-dialog.component';
import { IconComponent } from 'src/app/component/icon/icon.component';
import { defaultDialogConfig } from 'src/app/config/dialog';
import { ModeType } from 'src/app/interface/model';
import { FileInfo, FileMetaData } from 'src/app/interface/model/file-info';
import { FileSelectDirective } from 'src/app/modules/shared/directives/file-upload/file-select.directive';
import { ConfigurationService } from 'src/app/services/configuration.service';
import { NotificationService } from 'src/app/services/notification.service';
import { isEmptyOrNil } from 'src/app/utils';
import { getFileExtension } from 'src/app/utils/file-util';

@UntilDestroy()
@Component({
  selector: 'app-attachment-section-container',
  templateUrl: './attachment-section-container.component.html',
  styleUrls: ['./attachment-section-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    NgIf,
    NgFor,
    AsyncPipe,
    IconComponent,
    DropZoneComponent,
    AttachmentSectionContentComponent,
    FileSelectDirective,
  ],
})
export class AttachmentSectionContainerComponent implements OnInit {
  @HostBinding('class') class = 'block';

  @Input() moduleId: string;
  @Input() section: Section;
  @Input() edit = false;

  modes: ModeType[] = ['grid', 'list'];
  currentMode = 'grid';

  attachmentList$: Observable<FileInfo[]>;

  isUploading$ = new BehaviorSubject<boolean>(false);
  fileToUpload: FileInfo[];

  modeTemplate = 'grid';

  gridErrorMessages$ = defer(() =>
    this.documentDefineQuery.select((state) => state.ui.asyncError.grids[this.section.id]),
  ).pipe(
    map((gridError) => [...(gridError?.grid ?? []), ...(gridError?.row ?? [])]),
    map((errors) => errors?.map((error) => error.errorMessage)),
  );

  limitationSize = this.configurationService.CONFIG.FILE_UPLOAD_LIMITATION ?? 10;
  sizeLimit = 2 ** 20 * this.limitationSize;
  constructor(
    private readonly dialog: Dialog,
    private readonly documentDataQuery: DocumentDataQuery,
    private readonly documentDataService: DocumentDataService,
    private readonly cdr: ChangeDetectorRef,
    private readonly documentDefineQuery: DocumentDefineQuery,
    private readonly notificationService: NotificationService,
    private readonly configurationService: ConfigurationService,
  ) {}

  ngOnInit(): void {
    this.attachmentList$ = this.documentDataQuery.select(this.section.id).pipe(
      filter((attachments) => attachments),
      map((attachments) =>
        attachments.map((attachment) => {
          const fileName = this.getFileName(attachment);
          const fileExtension = getFileExtension(fileName);
          const url = this.getUrl(attachment);
          return {
            ...attachment,
            fileName,
            fileExtension,
            url,
            size: attachment.file?.fileSize,
            updatedOn: attachment.file?.updatedOn,
          };
        }),
      ),
    );
  }

  getFileName(attachment: any) {
    let fileName = attachment.file?.fileName || attachment.fileAddress || attachment.fileName;
    if (fileName) {
      return fileName;
    }
    // old ui data
    fileName = attachment.fileId?.original?.fileName;
    return fileName;
  }

  getUrl(attachment: any) {
    let url = attachment.file?.url || attachment.fileAddress;
    if (url) {
      return url;
    }
    // old ui data
    url = attachment.fileId?.original?.url;
    return url;
  }

  changeMode(mode: ModeType) {
    this.modeTemplate = mode;
  }

  valueChanged(files: FileInfo[]) {
    this.documentDataService.updateData({ [this.section.id]: files });
    this.isUploading$.next(false);
  }

  handleAddUrl() {
    const dialogRef = this.dialog.open<any, any, AddUrlDialogComponent>(AddUrlDialogComponent, {
      ...defaultDialogConfig,
      width: '400px',
    });

    dialogRef.closed
      .pipe(
        filter((result) => result?.type === 'done'),
        untilDestroyed(this),
      )
      .subscribe(({ payload }) => {
        const file = {
          fileExtension: getFileExtension(payload.url),
          fileName: payload.url,
          fileToUpload: {
            name: payload.url,
          },
          uploadType: UploadType.URL,
          fileAddress: payload.url,
          url: payload.url,
        } as FileInfo;
        const sectionData = this.documentDataQuery.getValue()[this.section.id] || [];
        if (isEmptyOrNil(sectionData)) {
          this.documentDataService.updateData({
            [this.section.id]: [],
          });
        }
        this.documentDataService.appendRowValue(this.section.id, file);
        this.fileToUpload = [file];
        this.cdr.markForCheck();
        this.isUploading$.next(false);
      });
  }

  uploadFile(fileList: ArrayLike<FileMetaData>) {
    const files = Array.from(fileList);
    const validFiles = this.validateFileSize(files);
    this.fileToUpload = validFiles?.map((file) => ({
      id: 'id',
      url: URL.createObjectURL(file),
      fileName: file.name,
      needUpload: true,
      fileToUpload: file,
      fileExtension: getFileExtension(file.name),
      updatedOn: file.lastModifiedDate,
      uploadType: UploadType.FILE,
    }));

    this.isUploading$.next(true);
  }

  validateFileSize(files: FileMetaData[]) {
    const invalidFiles: FileMetaData[] = [];
    const validFiles = files.filter((file) => {
      if (file.size < this.sizeLimit) {
        return true;
      }
      invalidFiles.push(file);
      return false;
    });
    if (invalidFiles.length !== 0) {
      const errors = invalidFiles.map((file) => this.attachErrorMessage(file));
      this.showError(errors);
    }
    if (validFiles.length === 0) {
      this.isUploading$.next(false);
    }
    return validFiles;
  }

  attachErrorMessage(file: File) {
    return {
      error: `${file.name} is greater than maximum upload size(${this.limitationSize}MB).`,
      file,
    };
  }

  showError(errors: { file: File; error: string }[]) {
    const message = errors.reduce((acc, error) => `${acc}${error.error} \n\n`, '');
    this.notificationService.open({ message, type: 'warn' });
  }
}
