import { inject, Injectable } from '@angular/core';

import { TranslocoService } from '@ngneat/transloco';
import { isAfter, isBefore, isEqual } from 'date-fns';
import equal from 'fast-deep-equal';
import * as R from 'ramda';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';

import { FieldDefine } from '../../../../entities/form-field';
import { FormFieldFilterTypes, FormFieldTypes } from '../../../../entities/form-field-types';
import { AnyObject } from '../../../../interface/model';
import { deepEqual, dotPath, isArray, isEmptyOrNil, isNotEmptyOrNil, stringAsc } from '../../../../utils';
import { CellMapperService } from './cell-mapper.service';
import { RegExpSet } from 'src/app/config/regexp';
import { FormDefine_FormFieldTypes } from 'src/app/entities/api';

enum CONDITION_SYMBOL {
  and = ';,;',
  globalOr = ';||;',
  or = '||',
}

enum CONDITION_OPERATOR {
  contain = '~',
  notContain = '!~',
  startWith = '=startwith',
  notStartWith = '!=startwith',
  endWith = '=endwith',
  notEndWith = '!=endwith',
  inRange = '=range',
  notInRange = '!=range',
  seperateByComma = '=seperateByComma',
  is = '=',
  isNot = '!=',
  blank = 'IS BLANK',
  notBlank = 'IS NOT BLANK',
  daysGreaterThan = '>days',
  daysLessThan = '<days',
  greaterThan = '>',
  lessThan = '<',
}

interface SearchFilter {
  condition: string;
  joinOperator: CONDITION_SYMBOL;
}

export interface FilterBus {
  filters: SearchFilter[];
  fieldDefine: FieldDefine;
}

@Injectable({
  providedIn: 'root',
})
export class DocumentGridFilterService {
  cellMapperService = inject(CellMapperService);

  translocoService = inject(TranslocoService);

  operatorRegExp = new RegExp(`^(?<operator>${Object.values(CONDITION_OPERATOR).join('|')})(?<value>.*)`);

  readonly enableFormatTypeMap = new Set<FormDefine_FormFieldTypes>([
    FormFieldTypes.selection,
    FormFieldTypes.tableSelect,
    FormFieldTypes.hyperlink,
  ]);

  readonly enableFilterTypeMap = new Set<FormDefine_FormFieldTypes>([
    FormFieldTypes.text,
    FormFieldTypes.email,
    FormFieldTypes.label,
    FormFieldTypes.tel,
    FormFieldTypes.url,
    FormFieldTypes.number,
    FormFieldTypes.decimal,
    FormFieldTypes.textarea,
    FormFieldTypes.dropdown,
    FormFieldTypes.hclGroup,
    FormFieldTypes.selection,
    FormFieldTypes.date,
    FormFieldTypes.datetime,
    FormFieldTypes.year,
    FormFieldTypes.hyperlink,
    FormFieldTypes.hyperlinkText,
    FormFieldTypes.seq,
    FormFieldTypes.numero,
    FormFieldTypes.tableSelect,
    FormFieldTypes.checkbox,
    FormFieldTypes.costSheetNo,
    FormFieldTypes.percentage,
    FormFieldTypes.radio,
    FormFieldTypes.treePathHyperlink,
  ]);

  readonly dateFormat = (value: string) => String(value).replace(/\//g, '-');

  readonly resolveFormatWithLabelPrefix = (value: string, format: string) => {
    const translateResult = this.translocoService.translate(`${R.last(format.split('='))}${value}`, {
      fallback: null,
    });

    if (isEmptyOrNil(translateResult)) {
      return null;
    }

    return translateResult;
  };

  canFilter(fieldDefine: FieldDefine): boolean {
    return fieldDefine?.filter?.canFilter ?? this.enableFilterTypeMap.has(fieldDefine?.type);
  }

  getFilterType(fieldDefine: FieldDefine): FormFieldFilterTypes {
    if (fieldDefine?.filter?.type) {
      return FormFieldFilterTypes[fieldDefine.filter.type];
    }

    if (this.isValueFilter(fieldDefine)) {
      return FormFieldFilterTypes.value;
    }

    if (this.isNumberFilter(fieldDefine)) {
      return FormFieldFilterTypes.number;
    }

    if (this.isDateFilter(fieldDefine)) {
      return FormFieldFilterTypes.date;
    }

    if (fieldDefine.type === 'Percentage') {
      return FormFieldFilterTypes.percentage;
    }

    if (fieldDefine.type === 'Radio') {
      return FormFieldFilterTypes.radio;
    }

    return FormFieldFilterTypes.text;
  }

  getComparator(fieldDefine: FieldDefine) {
    return (valueA: any, valueB: any): number => {
      if (deepEqual(valueA, valueB)) {
        return 0;
      }

      if (isEmptyOrNil(valueA)) {
        return -1;
      }

      if (isEmptyOrNil(valueB)) {
        return 1;
      }

      const { type } = fieldDefine;

      if (this.isNumberFilter(fieldDefine)) {
        return valueA - valueB;
      }

      if (
        (type === FormFieldTypes.hyperlink || type === FormFieldTypes.tableSelect) &&
        fieldDefine != null &&
        isNotEmptyOrNil(fieldDefine.format)
      ) {
        const sortedFormat = fieldDefine.format;
        const sortedFormatStr = String(sortedFormat).replace('{', '').replace('}', '');
        let formatValueA = '';
        let formatValueB = '';
        let keys = [];
        const key = R.slice(1, -1, fieldDefine.format);
        // handle case--{a}{b}{c}
        keys = key.replaceAll('{', '')?.split('}') || [];
        if (type === FormFieldTypes.tableSelect) {
          if (isNotEmptyOrNil(valueA)) {
            valueA.forEach((v: any) => {
              let baseAvaliableKeysFmt = '';
              const avaliableKeys = keys.filter((k: string) => v[k]);
              avaliableKeys.forEach((aKey: string) => {
                baseAvaliableKeysFmt += v[aKey];
              });
              formatValueA += baseAvaliableKeysFmt;
            });
          }
          if (isNotEmptyOrNil(valueB)) {
            valueB.forEach((v: any) => {
              let baseAvaliableKeysFmt = '';
              const avaliableKeys = keys.filter((k: string) => v[k]);
              avaliableKeys.forEach((aKey: string) => {
                baseAvaliableKeysFmt += v[aKey];
              });
              formatValueB += baseAvaliableKeysFmt;
            });
          }
        } else {
          formatValueA = valueA ? valueA[sortedFormatStr] : valueA;
          formatValueB = valueB ? valueB[sortedFormatStr] : valueB;
        }
        return String(formatValueA).localeCompare(String(formatValueB));
      }

      if (type === FormFieldTypes.selection || (Array.isArray(valueA) && Array.isArray(valueB))) {
        const sortedFormat = fieldDefine.format;
        const sortedFormatStr = sortedFormat.replace('{', '').replace('}', '');
        let formatValueA = valueA ? valueA[sortedFormatStr] : valueA;
        formatValueA = formatValueA || '';
        valueA.forEach((a) => {
          formatValueA = formatValueA === '' ? a[sortedFormatStr] : `${formatValueA},${a[sortedFormatStr]}`;
        });
        let formatValueB = valueB ? valueB[sortedFormatStr] : valueB;
        formatValueB = formatValueB || '';
        valueB.forEach((b) => {
          formatValueB = formatValueB === '' ? b[sortedFormatStr] : `${formatValueB},${b[sortedFormatStr]}`;
        });
        return formatValueA.localeCompare(formatValueB);
      }

      if (type === FormFieldTypes.dropdown) {
        return String(valueA.name).localeCompare(String(valueB.name));
      }

      if (this.isDateFilter(fieldDefine)) {
        return new Date(valueA).getTime().toString() > new Date(valueB).getTime().toString() ? 1 : -1;
      }

      if (this.useBooleanOptions(fieldDefine)) {
        return Boolean(valueA) > Boolean(valueB) ? 1 : -1;
      }

      return String(valueA).localeCompare(String(valueB));
    };
  }

  isValueFilter(fieldDefine: FieldDefine, newMapping?: any, isCheckbox?: boolean): boolean {
    // TODO: should only control in form-define
    const status = ['status', 'docStatus', 'editingStatus', 'processStatus'];
    const id = fieldDefine.mapping ? R.last(R.split('.', fieldDefine.mapping)) : fieldDefine.id;
    return (
      fieldDefine?.filter?.type === FormFieldFilterTypes.value ||
      fieldDefine?.type === FormFieldTypes.checkbox ||
      fieldDefine?.type === FormFieldTypes.dropdown ||
      fieldDefine?.type === FormFieldTypes.selection ||
      isNotEmptyOrNil(fieldDefine?.codelistName) ||
      R.includes(id, status) ||
      (newMapping && R.has(newMapping, status)) ||
      isCheckbox ||
      fieldDefine?.filter?.useYesNoOptions
    );
  }

  isNumberFilter(fieldDefine: FieldDefine): boolean {
    return (
      fieldDefine?.filter?.type === FormFieldFilterTypes.number ||
      fieldDefine?.type === FormFieldTypes.number ||
      fieldDefine?.type === FormFieldTypes.decimal ||
      fieldDefine?.type === FormFieldTypes.seq ||
      fieldDefine?.id === 'seq' ||
      fieldDefine?.id === 'seqNo'
    );
  }

  isDateFilter(fieldDefine: FieldDefine): boolean {
    return fieldDefine?.type === FormFieldTypes.date || fieldDefine?.type === FormFieldTypes.datetime;
  }

  useBooleanOptions(fieldDefine: FieldDefine): boolean {
    return fieldDefine?.filter?.useBooleanOptions || fieldDefine?.type === FormFieldTypes.checkbox;
  }

  getCurrentCodelistOptions(
    fieldDefine: FieldDefine,
    data$: Observable<AnyObject<any>[]>,
    isRequirementField?: boolean,
  ): Observable<any> {
    if (this.useBooleanOptions(fieldDefine)) {
      return of(['Filled', 'Empty']);
    }

    if (isRequirementField) {
      return of(['Yes', 'No']);
    }

    if (this.isValueFilter(fieldDefine)) {
      const status = ['status', 'docStatus', 'editingStatus'];
      const id = fieldDefine.mapping ? R.last(R.split('.', fieldDefine.mapping)) : fieldDefine.id;
      const noNeedEmptyValue = R.includes(id, status);
      return data$.pipe(
        map((data: AnyObject<any>[]) =>
          data.map((value: AnyObject<any>) => this.getFieldValue(value, fieldDefine)).filter(isNotEmptyOrNil),
        ),
        map((data: AnyObject<any>[]) => R.flatten(data)),
        map((data: AnyObject<any>[]) => R.sort(stringAsc, R.uniq(data).map(String))),
        map((data: string[]) => (noNeedEmptyValue ? data : [...data, 'Empty'])),
      );
    }

    return of([]);
  }

  getGroupGridCodelistOptions(fieldDefine: FieldDefine, data$: Observable<AnyObject<any>[]>): Observable<any> {
    const isCheckbox = fieldDefine.type === FormFieldTypes.checkbox;
    const status = ['status', 'docStatus', 'editingStatus'];
    const mapping = fieldDefine.mapping ? R.pipe(R.split('.'), R.last)(fieldDefine.mapping) : '';
    const isCodelist =
      isNotEmptyOrNil(fieldDefine?.codelistName) ||
      R.includes(fieldDefine.id, status) ||
      R.includes(mapping, status) ||
      FormFieldTypes.selection === fieldDefine.type;
    if (isCheckbox) {
      return of(['Filled', 'Empty']);
    }

    if (isCodelist) {
      return data$.pipe(
        map((datas: AnyObject<any>[][]) => {
          const dataArr = [];
          datas.forEach((data) => {
            dataArr.push(...data);
          });
          return dataArr;
        }),
        map((data: AnyObject<any>[]) =>
          data.map((value: AnyObject<any>) => this.getFieldValue(value, fieldDefine)).filter(isNotEmptyOrNil),
        ),
        map((data: AnyObject<any>[]) => R.flatten(data)),
        map((data: AnyObject<any>[]) => R.sort(stringAsc, R.uniq(data).map(String))),
      );
    }

    return of([]);
  }

  getCurrentValue(value: any, fieldDefine: FieldDefine): string {
    if (this.useBooleanOptions(fieldDefine)) {
      return value ? 'Filled' : 'Empty';
    }

    if (R.isNil(value)) {
      return value;
    }

    if (this.isDateFilter(fieldDefine)) {
      return String(value).replace(/-/g, '/');
    }

    const { type, format } = fieldDefine;
    if (type === FormFieldTypes.dropdown) {
      if (format?.startsWith('{') && this.isFieldTypeEnableFormat(type)) {
        if (isArray(value)) {
          value = value.map((v) => dotPath(R.slice(1, -1, format), v)).filter(isNotEmptyOrNil);
        } else {
          value = dotPath(R.slice(1, -1)(format), value);
        }
        return value;
      }
      return value.name;
    }

    if (fieldDefine.type === FormFieldTypes.hyperlink) {
      if (fieldDefine.format === 'Ver. {$self}') {
        return value;
      }
      if (fieldDefine.format) {
        return dotPath(R.slice(1, -1, fieldDefine.format), value);
      }
      return value;
    }

    if (fieldDefine.type === FormFieldTypes.tableSelect) {
      return fieldDefine.format ? dotPath(R.slice(1, -1, fieldDefine.format), value) : value;
    }

    if (fieldDefine?.format?.match(RegExpSet.LABEL_PREFIX)) {
      return this.resolveFormatWithLabelPrefix(value, fieldDefine?.format);
    }

    return String(value);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  doesFilterPass(
    rowData: AnyObject<any>,
    filterBus: FilterBus[],
    gridData: AnyObject<any>[],
    sectionId?: string,
  ): boolean {
    const result = this.getFilterResult(filterBus, rowData, sectionId);

    return result.every((pass) => pass);
  }

  getFilterResult(filterBus: FilterBus[], rowData: AnyObject<any>, sectionId?: string): boolean[] {
    return filterBus.map(({ fieldDefine, filters }) => {
      const { id, mapping, actionParams } = fieldDefine;
      const newMapping = mapping ? R.pipe(R.split('.'), R.last)(mapping) : '';
      const data = isNotEmptyOrNil(mapping) ? dotPath(mapping, rowData) : rowData[id];

      const isCheckbox = fieldDefine.type === FormFieldTypes.checkbox;

      const formatFilter = (val: SearchFilter[]) =>
        val[0].condition.split('||').map((condition, index) => {
          if (index === 0) {
            return {
              condition,
              joinOperator: null,
            };
          }
          return {
            condition: `=${condition}`,
            joinOperator: CONDITION_SYMBOL.or,
          } as SearchFilter;
        });

      const isRequirementField =
        fieldDefine.type === FormFieldTypes.radio &&
        fieldDefine.id?.startsWith('result') &&
        sectionId.startsWith('factoryAuditRequirements');

      const searchFilter =
        this.isValueFilter(fieldDefine, newMapping, isCheckbox) || isRequirementField ? formatFilter(filters) : filters;

      if (isEmptyOrNil(data)) {
        return this.filterPass('Empty', searchFilter, true, this.isDateFilter(fieldDefine));
      }

      if (fieldDefine.type === FormFieldTypes.selection || Array.isArray(data)) {
        if (fieldDefine.format) {
          const key = fieldDefine.format ? R.slice(1, -1, fieldDefine.format) : 'name';
          if (key.indexOf('{') !== -1) {
            // handle case--{a}{b}{c}
            const keys = key.replaceAll('{', '')?.split('}') || [];
            const selectionResult: boolean[] = data.map((row: AnyObject<any>) =>
              this.filterPass(
                row[keys.filter((v: string) => row[v])?.[0]],
                searchFilter,
                isEmptyOrNil(key),
                this.isDateFilter(fieldDefine),
              ),
            );
            return selectionResult.some((pass) => pass);
          }
          const selectionResult: boolean[] = data.map((row: AnyObject<any>) =>
            this.filterPass(row[key], searchFilter, isEmptyOrNil(key), this.isDateFilter(fieldDefine)),
          );

          return selectionResult.some((pass) => pass);
        }
      }

      if (fieldDefine.type === FormFieldTypes.treePathHyperlink) {
        const fieldId = actionParams?.fieldId || mapping;
        if (fieldDefine.format) {
          const key = R.slice(1, -1, fieldDefine.format);
          const treeData =
            isNotEmptyOrNil(fieldId) && isNotEmptyOrNil(fieldDefine.format) && rowData[fieldId]
              ? fieldDefine?.uiId === 'ui.tabShipDtl.vpoShipDtl.treePath'
                ? rowData?.[fieldId]?.itemId?.[key] || rowData[fieldId][key]
                : rowData[fieldId][key]
              : data;
          return this.filterPass(treeData, searchFilter, false, this.isDateFilter(fieldDefine));
        }
      }

      const value = this.getCurrentValue(data, fieldDefine);

      return this.filterPass(value, searchFilter, false, this.isDateFilter(fieldDefine));
    });
  }

  filterPass(data: any, filters: SearchFilter[], valueIsEmpty: boolean, isDate: boolean): boolean {
    let passResult = [];

    filters.forEach((searchFilter: SearchFilter) => {
      const { operator } = searchFilter.condition.match(this.operatorRegExp).groups;
      let { value } = searchFilter.condition.match(this.operatorRegExp).groups;
      value = decodeURIComponent(value.replace(/%/g, '%25'));

      let value1 = '';
      let value2 = '';

      if (operator === CONDITION_OPERATOR.inRange || operator === CONDITION_OPERATOR.notInRange) {
        const dates = R.slice(1, -1, value).split('-');
        [value1, value2] = dates;
      } else {
        value1 = value;
      }

      const calculateResult = this.calculationResult(operator, data, value1, value2, valueIsEmpty, isDate);
      passResult.push(calculateResult);

      if (isNotEmptyOrNil(searchFilter.joinOperator)) {
        const result =
          searchFilter.joinOperator === CONDITION_SYMBOL.and
            ? passResult.every((pass) => pass)
            : passResult.some((pass) => pass);
        passResult = [result];
      }
    });

    return passResult[0];
  }

  private calculationResult(
    operator: string,
    originalData: any,
    value1: string,
    value2: string,
    valueIsEmpty: boolean,
    isDate: boolean,
  ): boolean {
    if (!R.is(String, originalData) && !R.is(Number, originalData)) {
      return false;
    }

    const data = R.is(Number, originalData) ? String(originalData) : originalData;

    if (CONDITION_OPERATOR.contain === operator) {
      return R.includes(value1.toUpperCase(), data.toUpperCase());
    }

    if (CONDITION_OPERATOR.notContain === operator) {
      return !R.includes(value1.toUpperCase(), data.toUpperCase());
    }

    if (CONDITION_OPERATOR.startWith === operator) {
      value1 = value1.substring(1, value1.length - 1);
      return R.startsWith(value1.toUpperCase(), data.toUpperCase());
    }

    if (CONDITION_OPERATOR.notStartWith === operator) {
      value1 = value1.substring(1, value1.length - 1);
      return !R.startsWith(value1.toUpperCase(), data.toUpperCase());
    }

    if (CONDITION_OPERATOR.endWith === operator) {
      value1 = value1.substring(1, value1.length - 1);
      return R.endsWith(value1.toUpperCase(), data.toUpperCase());
    }

    if (CONDITION_OPERATOR.notEndWith === operator) {
      value1 = value1.substring(1, value1.length - 1);
      return !R.endsWith(value1.toUpperCase(), data.toUpperCase());
    }

    if (CONDITION_OPERATOR.is === operator) {
      if (isDate) {
        const date1 = new Date(this.dateFormat(data));
        const date2 = new Date(this.dateFormat(value1));
        return isEqual(new Date(date1.toDateString()), new Date(date2.toDateString()));
      }

      return equal(value1, data);
    }

    if (CONDITION_OPERATOR.isNot === operator) {
      if (isDate) {
        const date1 = new Date(this.dateFormat(data));
        const date2 = new Date(this.dateFormat(value1));
        return !isEqual(new Date(date1.toDateString()), new Date(date2.toDateString()));
      }
      return !equal(value1, data);
    }

    if (CONDITION_OPERATOR.blank === operator) {
      return this.isBlank(data) || valueIsEmpty;
    }

    if (CONDITION_OPERATOR.notBlank === operator) {
      return !this.isBlank(data) && !valueIsEmpty;
    }

    if (CONDITION_OPERATOR.daysGreaterThan === operator) {
      if (isDate) {
        const date1 = new Date(this.dateFormat(data));
        const date2 = new Date();
        const offset = Number(value1.replace('(', '').replace(')', ''));
        date2.setDate(date2.getDate() + (offset > 0 ? offset : offset - 1));
        return isAfter(new Date(date1.toDateString()), new Date(date2.toDateString()));
      }
      return R.gt(Number(data), Number(value1));
    }

    if (CONDITION_OPERATOR.daysLessThan === operator) {
      if (isDate) {
        const date1 = new Date(this.dateFormat(data));
        const date2 = new Date();
        const offset = Number(value1.replace('(', '').replace(')', ''));
        date2.setDate(date2.getDate() + (offset > 0 ? offset + 1 : offset));
        return isBefore(new Date(date1.toDateString()), new Date(date2.toDateString()));
      }
      return R.lt(Number(data), Number(value1));
    }

    if (CONDITION_OPERATOR.greaterThan === operator) {
      if (isDate) {
        const date1 = new Date(this.dateFormat(data));
        const date2 = new Date(this.dateFormat(value1));
        return isAfter(new Date(date1.toDateString()), new Date(date2.toDateString()));
      }
      return R.gt(Number(data), Number(value1));
    }

    if (CONDITION_OPERATOR.lessThan === operator) {
      if (isDate) {
        const date1 = new Date(this.dateFormat(data));
        const date2 = new Date(this.dateFormat(value1));
        return isBefore(new Date(date1.toDateString()), new Date(date2.toDateString()));
      }
      return R.lt(Number(data), Number(value1));
    }

    if (CONDITION_OPERATOR.inRange === operator) {
      if (isDate) {
        const date1 = new Date(this.dateFormat(data));
        const date2 = new Date(this.dateFormat(value1));
        const date3 = new Date(this.dateFormat(value2));
        return (
          ((isBefore(new Date(date2.toDateString()), new Date(date1.toDateString())) ||
            isEqual(new Date(date2.toDateString()), new Date(date1.toDateString()))) &&
            isAfter(new Date(date3.toDateString()), new Date(date1.toDateString()))) ||
          isEqual(new Date(date3.toDateString()), new Date(date1.toDateString()))
        );
      }
      return R.lte(Number(value1), Number(data)) && R.gte(Number(value2), Number(data));
    }

    if (CONDITION_OPERATOR.notInRange === operator) {
      if (isDate) {
        const date1 = new Date(this.dateFormat(data));
        const date2 = new Date(this.dateFormat(value1));
        const date3 = new Date(this.dateFormat(value2));
        return (
          isBefore(new Date(date1.toDateString()), new Date(date2.toDateString())) ||
          isAfter(new Date(date1.toDateString()), new Date(date3.toDateString()))
        );
      }
      return R.gte(Number(value1), Number(data)) || R.lte(Number(value2), Number(data));
    }

    if (CONDITION_OPERATOR.seperateByComma === operator) {
      const valueArr = value1.split(',');
      return valueArr.includes(data);
    }

    return false;
  }

  private isBlank(str: any): boolean {
    return !str || /^\s*$/.test(str) || str === 'Empty';
  }

  getFieldValue(row: any, fieldDefine: FieldDefine) {
    const { id: fieldId, mapping, type, format } = fieldDefine;

    let value = mapping ? dotPath(mapping, row) : row[fieldId];

    if (fieldId.startsWith('customField')) {
      value = R.path(['customFields', fieldId], row);
    }

    if (format?.startsWith('{') && (this.isFieldTypeEnableFormat(type) || fieldDefine?.filter?.canFilter)) {
      if (isArray(value)) {
        value = value.map((v) => dotPath(R.slice(1, -1, format), v)).filter(isNotEmptyOrNil);
      } else {
        value = dotPath(R.slice(1, -1)(format), value);
      }
    }

    if (
      this.isFieldTypeEnableFormat(type) &&
      isArray(value) &&
      type === FormFieldTypes.selection &&
      fieldId.startsWith('custSelection')
    ) {
      value = value.map((v) => dotPath(R.slice(1, -1, '{name}'), v));
    }

    if (R.has('name')(value)) {
      // for codelist field and its configuration is not completely
      value = value.name;
    }

    if (format?.match(RegExpSet.LABEL_PREFIX)) {
      return this.resolveFormatWithLabelPrefix(value, format);
    }

    return value;
  }

  private isFieldTypeEnableFormat(type: FormDefine_FormFieldTypes): boolean {
    const fun = this.cellMapperService.editFormatValues[type] || this.cellMapperService.viewFormatValues[type];

    return !!fun || this.enableFormatTypeMap.has(type);
  }
}
