import { Injectable } from '@angular/core';

import { <PERSON><PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';
import { of } from 'rxjs';

import { DocumentValueChangeService } from '../../../service/document-value-change.service';
import { DocumentDataQuery, DocumentDataService } from '../../../state';
import { FieldDefine } from 'src/app/entities/form-field';

@UntilDestroy()
@Injectable()
export class CompTemplValueChangeService extends DocumentValueChangeService {
  confirmBeforeValueChange = {
    compTemplSectionList: {
      levelType: this.clearLevel.bind(this),
      level: this.clearDetailSection.bind(this),
    },
  };
  constructor(
    protected readonly documentDataQuery: DocumentDataQuery,
    protected readonly documentDataService: DocumentDataService,
  ) {
    super();
  }

  clearLevel(value: any, field: FieldDefine, rowIndex?: number) {
    this.documentDataService.updateCellValueByFieldId(null, 'compTemplSectionList', rowIndex, 'level');
    this.clearDetailSection(null, null, rowIndex);
    return of(true);
  }

  clearDetailSection(value: any, field: FieldDefine, rowIndex?: number) {
    const section = this.documentDataQuery.getValue()?.compTemplSectionList[rowIndex];
    const newSection = {
      ...section,
      level: value,
    };
    this.documentDataQuery.getValue()?.compTemplSectionDetailList?.forEach((obj, index) => {
      if (
        obj?.compTemplSection?.sectionName === newSection?.sectionName &&
        obj?.compTemplSection?.sequence === newSection?.sequence
      ) {
        this.documentDataService.updateCellValueByFieldId(
          newSection,
          'compTemplSectionDetailList',
          index,
          'compTemplSection',
        );
        this.documentDataService.updateCellValueByFieldId(null, 'compTemplSectionDetailList', index, 'field');
        this.documentDataService.updateCellValueByFieldId(null, 'compTemplSectionDetailList', index, 'extraField');
      }
    });
    return of(true);
  }
}
