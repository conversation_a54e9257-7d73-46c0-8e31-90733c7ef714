import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, HostBinding, Output } from '@angular/core';

import { GridApi, ICellRendererParams, RowClickedEvent } from 'ag-grid-community';
import { Subject } from 'rxjs';

import { CellActionEvent } from '../cell-action-event.model';
import { AngularCellComponent, CellRendererAngularComp } from '../cell-renderer-params';
import { IconComponent } from 'src/app/component/icon/icon.component';
import { isNotNil } from 'src/app/utils';

@Component({
  selector: 'app-link-cell',
  templateUrl: './link-cell.component.html',
  styleUrls: ['./link-cell.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule, IconComponent],
})
export class LinkCellComponent extends AngularCellComponent implements CellRendererAngularComp {
  @HostBinding('class') class = 'flex position-relative width-100';

  @Output() rowDataChanged = new EventEmitter<RowClickedEvent>();

  gridApi: GridApi;

  private params: ICellRendererParams;
  private actionBus$: Subject<CellActionEvent>;
  text: string;
  link: string;
  edit: boolean;

  agInit(params: any) {
    this.params = params;
    const { api, context, value, valueFormatted, edit, col, data } = params;
    this.gridApi = api;
    this.actionBus$ = context?.actionBus$;
    const formatedValue = this.replaceValueWithFormat(col, data);
    this.text = formatedValue ?? value;
    this.link = valueFormatted;
    this.edit = edit;
  }

  refresh(): boolean {
    return false;
  }

  click(event: MouseEvent) {
    if (!event.ctrlKey) {
      event.preventDefault();
      event.stopPropagation();

      const encodedValueFormatted = this.encodeUrlSpecialChars(this.params.valueFormatted);

      this.actionBus$?.next({
        type: 'link',
        params: {
          ...this.params,
          valueFormatted: encodedValueFormatted,
        },
      });

      const { valueFormatted, colDef, value } = this.params;
      const callbackMethodObj = this.params.data?.callbackMethodObj;
      if (callbackMethodObj) {
        if (valueFormatted) {
          callbackMethodObj.arg.href = encodedValueFormatted;
          callbackMethodObj.arg.colId = colDef?.field;
          callbackMethodObj.arg.value = value;
        }
        callbackMethodObj.callbackMethod(callbackMethodObj.arg);
      }
    }
  }

  removeRow() {
    const removedRow = [this.params.node];
    this.gridApi.applyTransaction({ remove: removedRow });
  }

  replaceValueWithFormat(col, data): string {
    const format = col?.actionParams?.format;
    if (!format) {
      return null;
    }
    const properties = format
      .split(',')
      .map((prop) => prop.trim())
      .filter((prop) => prop.length > 0);
    const formatValue = properties
      .map((property) => data[property])
      .filter((property) => isNotNil(property))
      .join(',');
    return formatValue;
  }

  encodeUrlSpecialChars(url: string): string {
    const replacements: { [key: string]: string } = {
      '(': '%28',
      ')': '%29',
      '[': '%5B',
      ']': '%5D',
    };
    return url.replace(/[()$$]/g, (match) => replacements[match] || match);
  }
}
