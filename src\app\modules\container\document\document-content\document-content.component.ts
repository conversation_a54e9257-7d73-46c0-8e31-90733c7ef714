import {
  CdkDrag,
  CdkDragDrop,
  CdkDragHandle,
  CdkDropList,
  moveItemInArray,
  transferArrayItem,
} from '@angular/cdk/drag-drop';
import { CommonModule, formatDate } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  HostBinding,
  Inject,
  Input,
  LOCALE_ID,
  OnDestroy,
  Output,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';

import { TranslocoService } from '@ngneat/transloco';
import { isNil } from 'ramda';
import { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';
import { auditTime, distinctUntilChanged, filter, map, switchMap, tap } from 'rxjs/operators';

import { IconComponent } from '../../../../component/icon/icon.component';
import { DateTimeFormatStateService } from '../../../../services/date-time-format-state.service';
import { NavigationService } from '../../../../services/navigation.service';
import {
  camelCaseToDisplay,
  checkFieldChangeConditionsValidInObject,
  deepEqual,
  isEmptyOrNil,
  isNotEmptyOrNil,
  isNotNil,
} from '../../../../utils';
import { HierarchyChartComponent } from '../../dashboard/powerbi/balkan-chart/document/hierarchy-chart/hierarchy-chart.component';
import { AttachmentSectionContainerComponent } from '../attachment-section-container/attachment-section-container.component';
import { OverlayHostDirective } from '../directive/overlay-host.directive';
import { PositionContainerDirective } from '../directive/position-container.directive';
import { PositionSectionHeaderDirective } from '../directive/position-section-header.directive';
import { PositionTabHeaderDirective } from '../directive/position-tab-header.directive';
import { SectionStateDirective } from '../directive/section-state.directive';
import { DocumentSectionHeaderContentComponent } from '../document-section-header-content/document-section-header-content.component';
import { DynamicSectionComponent } from '../dynamic-section/dynamic-section.component';
import { FormSectionContainerComponent } from '../form-section-container/form-section-container.component';
import { GridSectionContainerComponent } from '../grid-section-container/grid-section-container.component';
import { GroupGridContainerComponent } from '../group-grid-container/group-grid-container.component';
import { HtmlEditorSectionContainerComponent } from '../html-editor-section-container/html-editor-section-container.component';
import { GenAnchorIdPipe } from '../pipe/gen-anchor-id.pipe';
import { SectionListContainerComponent } from '../section-list-container/section-list-container.component';
import { DocumentDynamicFieldService } from '../service/document-dynamic-field.service';
import { OverlayContainerService } from '../service/overlay-container.service';
import { DocumentDataQuery, DocumentStatus, DocumentStatusService, Section, TabGroup } from '../state';
import { FormTemplateService } from '../state/form-template.service';
import { TagChipsComponent } from '../tag-chips/tag-chips.component';
import { ViewGridSectionContainerComponent } from '../viewgrid-section-container/viewgrid-section-container.component';
import { TooltipDirective } from 'src/app/directives/tooltip.directive';
import { FormConfigPayload } from 'src/app/entities/form-field';
import { FileMetaData } from 'src/app/interface/model/file-info';
import { ApprovalService } from 'src/app/modules/cbx-action/helper/approval.service';
import { DropZoneComponent } from 'src/app/modules/shared/common/drop-zone/drop-zone.component';
import { AutoScrollEdgeDirective } from 'src/app/modules/shared/directives/axis/auto-scroll-edge.directive';
import { HintDirective } from 'src/app/modules/shared/directives/hint/hint.directive';
import { SyncHorizontalScrollContainerDirective } from 'src/app/modules/shared/directives/sync-horizontal-scroll-container.directive';
import { TocChapterDirective } from 'src/app/modules/shared/directives/toc-chapter.directive';
import { ViewportIndicatorDirective } from 'src/app/modules/shared/directives/viewport-indicator.directive';
import { VisibleIndicatorDirective } from 'src/app/modules/shared/directives/visible-indicator.directive';

@Component({
  selector: 'app-document-content',
  templateUrl: './document-content.component.html',
  styleUrls: ['./document-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CommonModule,
    TooltipDirective,
    PositionContainerDirective,
    PositionTabHeaderDirective,
    HintDirective,
    GenAnchorIdPipe,
    IconComponent,
    DocumentSectionHeaderContentComponent,
    TocChapterDirective,
    SectionStateDirective,
    AutoScrollEdgeDirective,
    PositionSectionHeaderDirective,
    ViewportIndicatorDirective,
    FormSectionContainerComponent,
    GridSectionContainerComponent,
    SyncHorizontalScrollContainerDirective,
    HierarchyChartComponent,
    VisibleIndicatorDirective,
    GroupGridContainerComponent,
    AttachmentSectionContainerComponent,
    SectionListContainerComponent,
    HtmlEditorSectionContainerComponent,
    OverlayHostDirective,
    ViewGridSectionContainerComponent,
    DynamicSectionComponent,
    MatIconModule,
    DropZoneComponent,
    CdkDropList,
    CdkDrag,
    CdkDragHandle,
    TagChipsComponent,
  ],
  providers: [OverlayContainerService],
})
export class DocumentContentComponent implements AfterViewInit, OnDestroy {
  @HostBinding('class') class = 'app-document-content block height-100 position-relative contain-strict will-scroll';

  @Input() edit: boolean;
  @Input() read: boolean;
  @Input() useTag: boolean = false;
  @Input() canAdd: boolean = false;
  @Input() multiple = true;
  @Input() isCopyDocument: boolean;
  @Input() moduleId: string;
  @Input() action: 'create' | 'view' | 'snapshot' | 'formTemplate';
  @Output() fileUploaded = new EventEmitter<ArrayLike<FileMetaData>>();

  warningMsgList$ = this.documentDataQuery
    .select('warningMsgList')
    .pipe(map((warningMsgList) => (isEmptyOrNil(warningMsgList) ? null : warningMsgList)));
  statusChanged$ = this.documentStatusService.getDocumentStatus$();
  view$ = this.statusChanged$.pipe(map((status) => status === DocumentStatus.View));

  chapters$ = new BehaviorSubject<TabGroup[]>([]);
  sequentialChapters$ = new BehaviorSubject<TabGroup[]>([]);

  filteredSequentialChapters$: Observable<TabGroup[]> = this.sequentialChapters$.pipe(
    switchMap((chapters) => {
      const chapterVisibilityObservables = chapters.map((chapter) => {
        const chapterVisibility$ = this.getDynamicChapterVisibleLogical(chapter);
        return chapterVisibility$.pipe(map((visible) => ({ chapter, visible })));
      });

      return combineLatest(chapterVisibilityObservables).pipe(
        map((results) => results.filter((result) => result.visible).map((result) => result.chapter)),
      );
    }),
  );

  sequencedChapters: { [tabId: string]: TabGroup } = {};
  private requestId: number;
  private timeoutId: number;

  @Input() get chapters(): TabGroup[] {
    return this._chapters;
  }
  set chapters(value: TabGroup[]) {
    this._chapters = value;
    this.chapters$.next(value);
    if (isNotNil(this.requestId)) {
      cancelAnimationFrame(this.requestId);
    }
    if (isNotNil(this.timeoutId)) {
      clearTimeout(this.timeoutId);
    }

    if (isNil(value)) {
      return;
    }

    this.requestId = requestAnimationFrame(() => this.pullChapter(value));
  }
  private _chapters: TabGroup[];

  dropConnectedList$ = this.sequentialChapters$.pipe(map((tabs) => tabs.map((tab) => tab.id)));
  disableDrag = true;
  disableDrag$ = this.documentStatusService.getDocumentAction$().pipe(
    map((action) => action !== 'formTemplate'),
    tap((disableDrag) => {
      this.disableDrag = disableDrag;
    }),
  );

  @Input() subject: string;

  @Input() sectionIdPrefix: string;

  dataLoading$ = this.documentDataQuery.selectLoading();

  chapterLoading$ = combineLatest([this.chapters$, this.sequentialChapters$]).pipe(
    map(([chapters, sequentialChapters]) => chapters?.length !== sequentialChapters.length),
  );

  onlyChapterLoading$ = combineLatest([this.chapterLoading$, this.dataLoading$]).pipe(
    map(([chapterLoading, dataLoading]) => chapterLoading && !dataLoading),
  );

  docCreatedBy$ = this.documentDataQuery.select('createUserName');
  docUpdatedBy$ = this.documentDataQuery.select('updateUserName');
  docCreateUser$ = this.documentDataQuery.select('createUser');
  docUpdateUser$ = this.documentDataQuery.select('updateUser');
  docUpdatedOn$ = this.documentDataQuery.select('updatedOn');
  refNo$ = this.documentDataQuery.select('refNo');
  version$ = this.documentDataQuery.select('version');

  approvalStatus$ = combineLatest([
    this.documentDataQuery.select('id').pipe(filter(isNotEmptyOrNil)),
    this.documentDataQuery.select('status'),
    this.documentDataQuery.select('editingStatus'),
    this.documentDataQuery.select('docStatus'),
  ]).pipe(
    auditTime(0),
    switchMap(([id]) => this.approvalService.getApprovalProfiles(id, true)),
    map((approvalProfiles) => {
      const latestProfile = approvalProfiles?.find((profile) => profile?.isProfileLatest);
      return camelCaseToDisplay(latestProfile?.status);
    }),
  );

  statusDescription$ = combineLatest([
    this.docCreatedBy$,
    this.docUpdatedBy$,
    this.docCreateUser$,
    this.docUpdateUser$,
    this.docUpdatedOn$,
    this.translocoService.selectTranslation('document'),
  ]).pipe(
    auditTime(0),
    filter(([createdBy, updateBy]) => !!(createdBy && updateBy)),
    map(([createdBy, updateBy, createUser, updateUser, updatedOn, translation]) => {
      const {
        createdBy: createdByLabel,
        lastModifiedBy: lastModifiedByLabel,
        lastModifiedon: lastModifiedonLabel,
        justAMomentAgo: justAMomentAgoLabel,
        today: todayLabel,
        on: onLabel,
      } = translation;
      const statement = this.getLastModifiedStatus(
        updatedOn,
        justAMomentAgoLabel,
        todayLabel,
        onLabel,
        createUser !== updateUser,
      );
      if (createUser === updateUser) {
        return `${createdByLabel} ${createdBy}, ${lastModifiedonLabel} ${statement}`;
      }
      return `${createdByLabel} ${createdBy}, ${lastModifiedByLabel} ${updateBy} ${statement}`;
    }),
  );

  @Output() chapterChanged = new EventEmitter<{ chapter: TabGroup; subChapter: Section }>();

  @Output() verticalSpacerReady = new EventEmitter<Element>();
  @Output() scrollElementReady = new EventEmitter<Element>();
  @Output() chapterReady = new EventEmitter<Element>();

  constructor(
    @Inject(LOCALE_ID) private readonly locale: string,
    private readonly host: ElementRef,
    private readonly documentDataQuery: DocumentDataQuery,
    readonly navigationService: NavigationService,
    private readonly translocoService: TranslocoService,
    private readonly dateTimeFormatStateService: DateTimeFormatStateService,
    private readonly documentDynamicFieldService: DocumentDynamicFieldService,
    private readonly documentStatusService: DocumentStatusService,
    private readonly approvalService: ApprovalService,
    private readonly formTemplateService: FormTemplateService,
  ) {}

  ngAfterViewInit() {
    this.scrollElementReady.emit(this.host.nativeElement);
    this.chapterLoading$.pipe(filter((value) => !value)).subscribe(() => {
      this.chapterReady.emit();
    });
  }

  ngOnDestroy(): void {
    if (isNotNil(this.requestId)) {
      cancelAnimationFrame(this.requestId);
    }
    if (isNotNil(this.timeoutId)) {
      clearTimeout(this.timeoutId);
    }
  }

  checksumTrackBy(index: number, obj: { checksum: number }) {
    return obj.checksum;
  }

  private isChapterCheckSumEqual(chapter: TabGroup, compareChapter: TabGroup) {
    if (!(chapter && compareChapter)) {
      return false;
    }

    return (
      chapter.checksum === compareChapter.checksum &&
      this.isSectionsCheckSumEqual(chapter.subChapters, compareChapter.subChapters)
    );
  }

  private isSectionsCheckSumEqual(sections: Section[], compareSections: Section[]) {
    if (sections?.length !== compareSections?.length) {
      return false;
    }
    if (!(sections && compareSections)) {
      return false;
    }
    return sections.every((section, index) => section.checksum === compareSections[index].checksum);
  }

  private pullChapter(queueChapters: TabGroup[], count: number = 1) {
    const queuedChapters = queueChapters.slice(0, count);
    const restChapter = queueChapters
      .slice(count)
      .filter((chapter) => this.isChapterCheckSumEqual(chapter, this.sequencedChapters[chapter.id]));
    const resultChapter = [...queuedChapters, ...restChapter];
    this.sequentialChapters$.next(resultChapter);
    if (resultChapter.length >= queueChapters.length) {
      queueChapters.forEach((chapter) => {
        this.sequencedChapters[chapter.id] = chapter;
      });
      return;
    }
    this.timeoutId = setTimeout(() => {
      this.requestId = requestAnimationFrame(() => this.pullChapter(queueChapters, count + 1));
    }, 40) as unknown as number;
  }

  onChapterEnter(chapter: TabGroup, subChapter: Section) {
    this.chapterChanged.emit({ chapter, subChapter });
  }

  getLastModifiedStatus(
    updatedOn: string,
    justAMomentAgoLabel: string,
    todayLabel: string,
    onLabel: string,
    needOnWd: boolean,
  ): string {
    const now = new Date();
    const updateTime = new Date(updatedOn);
    const minutes = (now.getTime() - updateTime.getTime()) / 60000;
    if (minutes <= 60) {
      return justAMomentAgoLabel;
    }
    if (minutes - 1440 <= 0 && minutes - 60 > 0) {
      return todayLabel;
    }
    const { dateFormat, timezone } = this.dateTimeFormatStateService;
    const datetime = formatDate(updatedOn, dateFormat, this.locale, timezone);
    return needOnWd ? `${onLabel} ${datetime}` : `${datetime}`;
  }

  getDynamicSectionVisibleLogical(section: Section, isSectionLabel: boolean): Observable<boolean> {
    const visibleFunc = (item: Section) =>
      isSectionLabel ? !item.hideLabel && item.label && !item.hidden : !item.hidden;

    if (this.documentDynamicFieldService.checkDynamicSectionVisibleLogical(section.id)) {
      return this.documentDynamicFieldService
        .getDynamicSectionVisibleLogical(section)
        .pipe(map((section1) => visibleFunc(section1)));
    }
    return visibleFunc(section) ? this.checkFieldChangeConditions$(section) : of(visibleFunc(section));
  }

  onChapterEnterForSectionList(data: { chapter: TabGroup; subChapter: Section }) {
    this.chapterChanged.emit(data);
  }

  checkFieldChangeConditions$(section: Section): Observable<boolean> {
    if (isEmptyOrNil(section.fieldChangeConditions)) {
      return of(true);
    }
    const conditionFieldIds = section.fieldChangeConditions
      .flatMap((conditionGroup) => conditionGroup)
      .map((condition) => condition.id);
    return combineLatest(
      [...new Set(conditionFieldIds)].map((conditionFieldId) => {
        const select$ = this.isCustomField(conditionFieldId)
          ? this.documentDataQuery.select('customFields').pipe(
              switchMap((customFields) => {
                const customField = customFields[conditionFieldId];
                return of(customField);
              }),
            )
          : this.documentDataQuery.select(conditionFieldId);

        return select$.pipe(
          distinctUntilChanged(deepEqual),
          map((value) => ({ [conditionFieldId]: value })),
        );
      }),
    ).pipe(
      map((conditionFieldValues) =>
        conditionFieldValues.reduce((result, item) => {
          const key = Object.keys(item)[0];
          result[key] = item[key];
          return result;
        }),
      ),
      distinctUntilChanged(deepEqual),
      map(
        (documentConditionData) =>
          // match condition => hidden field; not match condition => show field;
          !checkFieldChangeConditionsValidInObject(section.fieldChangeConditions, documentConditionData, 'SOME'),
      ),
    );
  }

  isCustomField(conditionFieldId: string) {
    const prefixes = [
      'customFieldText',
      'customFieldDate',
      'customFieldNumber',
      'customFieldDecimal',
      'customFieldMemoText',
      'customFieldBoolean',
      'customFieldCodeList',
      'customFieldSelection',
    ];

    return prefixes.some((prefix) => conditionFieldId.startsWith(prefix));
  }

  uploadFile(fileList: ArrayLike<FileMetaData>) {
    this.fileUploaded.emit(fileList);
  }

  onSectionDrop(event: CdkDragDrop<any[]>) {
    if (this.disableDrag) {
      return;
    }
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);
    }
    const tabId = event.previousContainer.id;
    const toTabId = event.container.id;
    const sectionId = event.container.data[event.currentIndex].id;
    let toSectionId: string;
    if (event.currentIndex !== 0) {
      toSectionId = event.container.data[event.currentIndex - 1].id;
    }
    this.formTemplateService.updateSectionPosition(sectionId, tabId, toSectionId, toTabId);
  }

  onTabDrop(event: CdkDragDrop<any[]>) {
    if (this.disableDrag) {
      return;
    }
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);
    }
    const tabId = event.container.data[event.currentIndex].id;
    let toTabId: string;
    if (event.currentIndex !== 0) {
      toTabId = event.container.data[event.currentIndex - 1].id;
    }
    this.formTemplateService.updateTabPosition(tabId, toTabId);
  }

  onLabelClick(type: string, tabId: string, label: string, sectionId?: string) {
    const formConfigPayload = {
      tabId,
      sectionId,
      type,
      label,
    } as FormConfigPayload;
    this.formTemplateService.componentClick(formConfigPayload);
  }

  getDynamicChapterVisibleLogical(chapter: TabGroup): Observable<boolean> {
    return this.checkFieldChangeConditions$(chapter as unknown as Section);
  }
}
