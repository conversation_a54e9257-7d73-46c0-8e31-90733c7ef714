<ng-container *ngIf="tocCollapseLoaded$ | async">
  <div *transloco="let t; read: 'document'" class="container grid contain-strict height-100">
    <div class="sidebar-wrapper overflow-hidden height-100" [class.collapse-sidebar]="tocCollapse$ | async">
      <div class="sidebar grid height-100 contain-strict">
        <div class="sidebar-description grid gap-16 padding-y-12">
          <div style="display: flex">
            <button
              *ngIf="!inSecondaryRoute && hasHistoryPage"
              type="button"
              class="back-button justify-self-start flex items-center pointer outline-0 padding-0 border-0 bg-transparent color-white margin-x-8"
              title="Go to previous page"
              (click)="goBack()"
            >
              <app-icon class="icon-size-20 back-icon">keyboard_backspace</app-icon>
              <span>{{ 'back' | transloco }}</span>
            </button>

            <button type="button" class="hide-sidebar-btn" title="Hide TOC" (click)="hideSidebar()">
              <app-icon>first_page</app-icon>
            </button>
          </div>
          <h3 class="sidebar-title text-align-left margin-0 padding-x-12">{{ moduleLabel$ | async }}</h3>
        </div>

        <div class="overflow-hidden">
          <app-table-of-contents
            [moduleId]="moduleId"
            [chapters]="tocChapters$ | async"
            [activeChapter]="activeChapter$ | async"
            [activeSubChapter]="activeSubChapter$ | async"
            [activeGridChapter]="activeGridChapter$ | async"
            [invalidTabs]="backendInvalidTabs$ | async"
            [compareTabs]="backendCompareTabs$ | async"
            [invalidSections]="backendInvalidSections$ | async"
            [compareSections]="backendCompareSections$ | async"
            [invalidGridChapters]="backendInvalidGridChapters$ | async"
            [edit]="edit$ | async"
            (chapterSelected)="scrollToChapter($event)"
            (subChapterSelected)="scrollToChapter($event)"
            (gridChapterSelected)="scrollToGridChapter($event)"
          ></app-table-of-contents>
        </div>

        <ng-container *ngIf="showCpmButton$ | async">
          <button
            *ngIf="showInitializeCpm$ | async; else initializedCpm"
            class="cpm-initialized-button cpm-initialize-button flex justify-center items-center pointer width-100 border-0"
            [disabled]="initializing$ | async"
            (click)="initCpm()"
          >
            <span>
              <ng-container *ngIf="initialized$ | async">{{ t('initializeCPM') }}</ng-container>
              <ng-container *ngIf="initializing$ | async">{{ t('initializing') }}</ng-container>
            </span>
          </button>

          <ng-template #initializedCpm>
            <button
              class="cpm-initialized-button flex justify-end items-center gap-4 pointer width-100 border-0"
              (click)="switchCpmBar()"
            >
              <span>{{ t('criticalPath') }}</span>
              <app-icon class="show-cpm-bar-icon" [class.rotate]="showCpmBar$ | async"
                >keyboard_double_arrow_right</app-icon
              >
            </button>
          </ng-template>
        </ng-container>
      </div>
    </div>

    <div class="content-wrapper grid overflow-scroll height-100">
      <div class="content-header position-relative grid overflow-auto">
        <header class="document-header grid position-relative">
          <div
            *ngIf="headerTopSectionVisible$ | async"
            class="position-relative flex justify-between gap-8 flex-noshrink"
          >
            <div class="flex items-center flex-spacer gap-8">
              <div *ngIf="backgroundColor">
                <span
                  [style.background-color]="backgroundColor"
                  [style.color]="backgroundColor | colorfulFont"
                  class="moduleStyle"
                >
                  {{ moduleValue }}
                </span>
              </div>
              <div *ngIf="dataAbstractLoading$ | async" class="action-loading">
                <span class="bouncing-loading"><span class="bouncing-point"></span></span>
              </div>
              <span class="doc-info-title2 max-height-100 overflow-visible" [style.color]="fontColor">
                {{ dataAbstract }}
              </span>
            </div>

            <div
              *ngIf="notCreateAction$ | async"
              class="inline-flex items-center justify-center flex-wrap overflow-auto scrollbar-none"
            >
              <span class="doc-info-version white-space-nowrap">
                {{ docVersionStatus$ | async }}
              </span>

              <span *ngIf="docStatus$ | async as docStatus" class="doc-info-status">({{ docStatus | titlecase }})</span>
            </div>

            <div class="flex">
              <div class="flex flex-column status" *ngIf="statusLabelId$ | async as statusLabelId">
                <span class="status-type">Workflow</span>
                <span
                  class="status-label white-space-nowrap"
                  [style.background-color]="statusLabelBackgroundColor$ | async"
                  [style.color]="statusLabelBackgroundColor$ | async | colorfulFont"
                >
                  {{ statusLabelId | transloco }}
                </span>
              </div>
              <div
                class="flex flex-column pointer status"
                *ngIf="integrationStatus$ | async as integrationStatus"
                (click)="openIntegration()"
              >
                <span class="status-type">Integration</span>
                <span
                  class="status-label"
                  [style.background-color]="statusLabelBackgroundColor$ | async"
                  [style.color]="statusLabelBackgroundColor$ | async | colorfulFont"
                >
                  {{ integrationStatus | transloco }}
                </span>
              </div>
              <div
                class="flex flex-column pointer status"
                *ngIf="approvalStatus$ | async as approvalStatus"
                (click)="openApproval()"
              >
                <span class="status-type">Approval</span>
                <span class="approval-label" [class]="approvalStatusStyle">
                  {{ approvalStatus }}
                </span>
              </div>
            </div>

            <ng-container *ngIf="headerActionMenuVisible$ | async">
              <app-link-bar
                *ngIf="moduleId !== 'vqce'"
                class="flex-noshrink"
                [moduleId]="moduleId"
                [model]="firstLineLinkBarItems$ | async"
                (linkBarItem)="onMenuClick($event)"
              ></app-link-bar>

              <app-more-menu
                class="flex-noshrink"
                [moduleId]="moduleId"
                [refNo]="refNo$ | async"
                [tabId]="tabId"
              ></app-more-menu>
            </ng-container>
          </div>

          <div *ngIf="headerMidSectionVisible$ | async" class="flex gap-8 headerMidSection">
            <ng-container *ngIf="headerMidSectionSiblingNavigatorVisible">
              <app-document-sibling-navigator
                *ngIf="showNavigateSibling$ | async"
                [tabId]="tabId"
                [moduleId]="moduleId"
              ></app-document-sibling-navigator>
            </ng-container>
            <ng-container
              *ngIf="moduleId !== 'vqce'"
              [ngComponentOutlet]="editingUserComponent$ | async"
            ></ng-container>

            <div class="margin-left-auto" *ngIf="validating$ | async">
              <div
                class="opacity-0 flex items-center pointer-events-none"
                [class.validation-loading]="validating$ | async"
                appTooltip="Document validating"
              >
                <span class="bouncing-loading"><span class="bouncing-point"></span></span>
              </div>
            </div>
            <app-link-bar
              [model]="editLinkBarItem$ | async"
              [moduleId]="moduleId"
              (linkBarItem)="onMenuClick($event)"
              style="padding: 5px 10px 0 0"
            ></app-link-bar>
            <ng-container *ngIf="(subjectRequiredFlag$ | async) && moduleId !== 'vqce'">
              <app-document-subject
                *ngIf="edit$ | async; else subjectReadModeTemplate"
                class="flex-auto"
                [headerFields]="headerFields$ | async"
                [moduleId]="moduleId"
              ></app-document-subject>
              <ng-template #subjectReadModeTemplate>
                <div class="flex subject-view" [appTooltip]="subject$ | async">
                  {{ subject$ | async }}
                </div>
              </ng-template>
            </ng-container>
          </div>

          <div *ngIf="headerBotSectionVisible$ | async" class="header-action-wrapper flex">
            <app-base-menu-bar
              class="menu-bar-action-wrapper"
              [moduleId]="moduleId"
              [model]="filterViewActions$ | async"
              [disabled]="actionLoading$ | async"
              (actionItem)="onMenuClick($event)"
            ></app-base-menu-bar>

            <div *ngIf="actionLoading$ | async" class="flex items-center action-loading">
              <span class="bouncing-loading"><span class="bouncing-point"></span></span>
            </div>

            <div class="flex-spacer"></div>

            <ng-container *ngIf="showProposedChangesToggle$ | async as showProposedChange">
              <ng-container *ngIf="approvalStatus$ | async as approvalStatus">
                <app-slide-toggle
                  class="action-slide-toggle margin-right-24"
                  color="primary"
                  disableRipple
                  [checked]="isProposedChangesCheck$ | async"
                  (checkedChange)="changeProposedChanges($event, approvalStatus)"
                >
                  Proposed Changes
                </app-slide-toggle>
              </ng-container>
            </ng-container>

            <app-link-bar
              class="margin-right-16 items-center"
              [model]="secondLinkBarItems$ | async"
              [moduleId]="moduleId"
              (linkBarItem)="onMenuClick($event)"
            ></app-link-bar>

            <app-document-sibling-navigator
              *ngIf="showNavigateSibling$ | async"
              [tabId]="tabId"
              [moduleId]="moduleId"
            ></app-document-sibling-navigator>

            <ng-container
              *ngIf="moduleId === 'vqce'"
              [ngComponentOutlet]="editingUserComponent$ | async"
            ></ng-container>
          </div>

          <div *ngIf="showNavigateLatestDocument$ | async" class="version-warning flex-center gap-4 position-absolute">
            <app-icon class="info-icon icon-size-16" svgIcon="icon_info"></app-icon>
            <span> This is an old version. </span>
            <a class="pointer" (click)="redirectToLatestVersion()">
              {{ t('clickheretolatestversion.') }}
            </a>
          </div>

          <div
            *ngIf="showNavigateVpoItemItemNotIsLatestDocument$ | async"
            class="version-warning flex-center gap-4 position-absolute"
          >
            <app-icon svgIcon="icon_info"></app-icon>
            <span>
              Please
              <a href="javascript:" (click)="vpoItemItemNotIsLatestVersion()">refresh </a>
              to get the updated information of the linked items
            </span>
          </div>

          <button
            *ngIf="inSecondaryRoute"
            type="button"
            class="close position-absolute icon-button primary-text"
            (click)="closeOverlay()"
          >
            <app-icon>close</app-icon>
          </button>
        </header>

        <div #alertSection>
          <ng-container
            *ngIf="(hasAlertSection$ | async) || (theModuleHasSpecifySmartAlert$ | async).includes(moduleId)"
          >
            <app-document-alert-section
              [moduleId]="moduleId"
              [hasAlertSection]="hasAlertSection$ | async"
              [theModuleHasSpecifySmartAlert]="theModuleHasSpecifySmartAlert$ | async"
            ></app-document-alert-section>
          </ng-container>
        </div>

        <div
          appOverlayHost
          class="overlay-host position-absolute full-size-offset pointer-events-none display-none"
        ></div>
      </div>

      <div *ngIf="copyToNew$ | async" class="content-copy-actions margin-left-16">
        <button class="basic-button primary-color margin-right-16" (click)="selectAll()">Select All</button>
        <button class="basic-button primary-color" (click)="cancelAll()">Cancel All</button>
      </div>

      <div *ngIf="copyToExists$ | async" class="content-copy-actions margin-left-16">
        <button class="basic-button primary-color margin-right-16" (click)="selectAllPartialCopy()">Select All</button>
        <button class="basic-button primary-color" (click)="cancelAllPartialCopy()">Cancel All</button>
      </div>

      <div class="content-main grid contain-strict">
        <div
          class="open-sidebar-wrapper overflow-hidden contain-strict"
          [class.sidebar-collapse]="tocCollapse$ | async"
        >
          <button
            *ngIf="tocCollapse$ | async"
            type="button"
            class="open-sidebar-btn"
            appTooltip="Click to Expand"
            tooltipPosition="right"
            (click)="openSidebar()"
          >
            <app-icon>more_vert</app-icon>
          </button>
        </div>

        <app-document-content
          appAutoScrollNode
          appTocContent
          cdkScrollable
          class="document-content"
          [class.sidebar-collapse]="tocCollapse$ | async"
          [moduleId]="moduleId"
          [action]="action$ | async"
          [isCopyDocument]="copy$ | async"
          [edit]="edit$ | async"
          [read]="view$ | async"
          [useTag]="true"
          [chapters]="chapters$ | async"
          [sectionIdPrefix]="sectionIdPrefix"
          (chapterChanged)="onChapterEnter($event)"
          (scrollElementReady)="scrollElementReady($event)"
          (verticalSpacerReady)="verticalSpacerReady($event)"
          (chapterReady)="chapterReady()"
        >
          <div
            *ngIf="(hasApprovalButton$ | async) || (hasAcceptOrRejectVendorChangesButtons$ | async)"
            approvalButtons
            class="approval-button-wrapper"
          >
            <button
              *ngIf="withdrawApprovalButton$ | async"
              class="approval-button withdraw-button flat-button"
              (click)="openApprovalActionDialog('withdraw')"
            >
              Withdraw Approval
            </button>

            <ng-container *ngIf="approvalButtons$ | async">
              <button class="approval-button reject-button flat-button" (click)="openApprovalActionDialog('reject')">
                Reject
              </button>

              <button class="approval-button approve-button flat-button" (click)="openApprovalActionDialog('approve')">
                Approve
              </button>
            </ng-container>

            <ng-container
              *ngIf="
                (view$ | async) &&
                (hasAcceptOrRejectVendorChangesButtons$ | async) &&
                (hasInlinePendingComparison$ | async) === false &&
                (hasCompareChangesButton$ | async) === false
              "
            >
              <button
                class="approval-button reject-button flat-button"
                (click)="openVendorChangeProposedActionDialog(false)"
              >
                Reject Changes
              </button>
              <button
                class="approval-button approve-button flat-button"
                (click)="openVendorChangeProposedActionDialog(true)"
              >
                Accept Changes
              </button>
            </ng-container>
            <ng-container
              *ngIf="
                (view$ | async) && (hasInlinePendingComparison$ | async) && (hasCompareChangesButton$ | async) === false
              "
            >
              <button class="approval-button reject-button flat-button" (click)="openInlineActionDialog(false)">
                Reject All
              </button>
              <button class="approval-button approve-button flat-button" (click)="openInlineActionDialog(true)">
                Accept All
              </button>
            </ng-container>
          </div>

          <div
            *ngIf="(acknowledgeButton$ | async) && (edit$ | async) === false"
            acknowledgeButton
            class="acknowledge-button-wrapper"
          >
            <button class="acknowledge-button flat-button" (click)="openConfirmAcknowledgeDialog()">
              {{ acknowledgedButtonLabel$ | async }}
            </button>
          </div>
        </app-document-content>

        <div *ngIf="onlyDataLoading$ | async" class="loading-wrapper">
          <div class="transform-center">
            <span class="bouncing-loading"><span class="bouncing-point"></span></span>
          </div>
        </div>

        <div *ngIf="showDataLoadingIndicator$ | async" class="loading-wrapper">
          <div class="transform-center">
            <span class="bouncing-loading"><span class="bouncing-point"></span></span>
          </div>
        </div>
      </div>

      <div
        *ngIf="
          (hasCompareChangesButton$ | async) &&
          (edit$ | async) === false &&
          ((hasApprovalButton$ | async) || (hasAcceptOrRejectVendorChangesButtons$ | async))
        "
        approvalButtons
        class="compare-btn flex gap-8 scrollbar-none"
      >
        <ng-container>
          <button class="confirm-btn flex-noshrink flat-button margin-x-4" (click)="unDoAllChanges()">
            Discard Changes
          </button>
        </ng-container>
        <ng-container>
          <button
            class="confirm-btn flex-noshrink flat-button margin-x-4 primary-color"
            (click)="submitAcceptOrRejectChanges()"
          >
            Save Changes
          </button>
        </ng-container>
      </div>

      <app-document-container-edit-footer
        *ngIf="footerActionsExist$ | async"
        class="content-edit-footer contain-strict overflow-hidden"
        [moduleId]="moduleId"
        [editable]="editable$ | async"
        [actionLoading]="actionLoading$ | async"
        [footerActions]="footerActions$ | async"
        (clickMenu)="onMenuClick($event)"
      ></app-document-container-edit-footer>

      <app-cpm-bar
        *ngIf="showCpmBar$ | async"
        class="content-cpm-bar cpm-bar-wrapper block overflow-hidden"
        [module]="moduleId"
        [refNo]="refNo$ | async"
        [docVersionStatus]="docVersionStatus$ | async"
        [businessReference]="businessReference$ | async"
        [openCpmDialog]="openCpmDialog"
        (hasCpmMilestones)="showCpmButton($event)"
      >
      </app-cpm-bar>
    </div>

    <!-- <app-tool-panel *ngIf="toolPanel$ | async" class="tool-panel" [panels]="toolPanel$ | async"></app-tool-panel> -->
    <div
      class="right-panel-wrapper overflow-hidden height-100"
      [class.expand]="(showRHP$ | async) && (isExpand$ | async)"
      *ngIf="(showRHP$ | async) && moduleId !== 'user' && moduleId !== 'lineSheet'"
    >
      <app-right-hand-panel
        *ngIf="(showRHP$ | async) && moduleId !== 'user' && moduleId !== 'lineSheet'"
        [class.sideBarCollapse]="tocCollapse$ | async"
        [module]="moduleId"
        [refNo]="refNo$ | async"
        [version]="docVersion$ | async"
        [docCreatedBy]="docCreatedBy$ | async"
        [docCreatedOn]="docCreatedOn$ | async"
        [copyByDocumentId]="copyByDocumentId$ | async"
        [rightHandPanels]="rightHandPanels$ | async"
        [documentData]="documentData$ | async"
      >
      </app-right-hand-panel>
    </div>
    <app-tool-panel
      *ngIf="toolPanel$ | async"
      class="tool-panel"
      [panels]="toolPanel$ | async"
      [classSideBarCollapse]="tocCollapse$"
      [module]="moduleId"
      [refNo]="refNo$"
      [docCreatedBy]="docCreatedBy$"
      [docCreatedOn]="docCreatedOn$"
      [copyByDocumentId]="copyByDocumentId$"
      [rightHandPanels]="rightHandPanels$"
      [documentData]="documentData$"
    ></app-tool-panel>
  </div>
</ng-container>

<div *ngIf="isRefreshItems$ | async" class="loading-wrapper">
  <div class="transform-center">
    <span class="bouncing-loading"><span class="bouncing-point"></span></span>
  </div>
</div>

<div *ngIf="showLoadingIndicator$ | async" class="loading-wrapper">
  <div class="transform-center">
    <span class="bouncing-loading"><span class="bouncing-point"></span></span>
  </div>
</div>
