import { DIALOG_DATA } from '@angular/cdk/dialog';
import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, HostBinding, Inject } from '@angular/core';

import { LazyLoadImageModule } from 'ng-lazyload-image';
import { BehaviorSubject } from 'rxjs';

import { HintDirective } from '../../directives/hint/hint.directive';
import { BasePdfComponent } from '../base-component/base-pdf/base-pdf.component';
import { IconComponent } from 'src/app/component/icon/icon.component';
import { PdfViewerDialogData } from 'src/app/interface/model';
import { FileInfo } from 'src/app/interface/model/file-info';
import { CbxDialogHeaderComponent } from 'src/app/modules/shared/common/cbx-dialog-header/cbx-dialog-header.component';

@Component({
  selector: 'app-pdf-viewer-dialog',
  templateUrl: './pdf-viewer-dialog.component.html',
  styleUrls: ['./pdf-viewer-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [LazyLoadImageModule, CbxDialogHeaderComponent, IconComponent, AsyncPipe, BasePdfComponent, HintDirective],
})
export class PdfViewerDialogComponent {
  @HostBinding('class') class = 'height-100';

  readonly totalPage$ = new BehaviorSubject<number>(1);
  readonly currentPage$ = new BehaviorSubject<number>(1);
  readonly currentZoom$ = new BehaviorSubject<number>(1);

  title: string;
  attachment: FileInfo;

  constructor(@Inject(DIALOG_DATA) public data: PdfViewerDialogData) {
    this.title = data.attachment.fileName || 'PDF Viewer';
    this.attachment = data.attachment;
  }

  incrementPage(amount: number) {
    const currentPage = this.currentPage$.value;
    this.currentPage$.next(currentPage + amount);
  }

  incrementZoom(amount: number) {
    const currentZoom = this.currentZoom$.value;
    this.currentZoom$.next(currentZoom + amount);
  }
}
