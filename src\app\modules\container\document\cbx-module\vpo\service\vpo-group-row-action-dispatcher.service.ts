import { Injectable, Type } from '@angular/core';

import { defer, Observable } from 'rxjs';

import { GroupRowActionDispatcherService } from '../../../grid-section-content/service/group-row-action-dispacher.service';
import { AbstractUIAction } from 'src/app/modules/cbx-action/model/abstract-ui-action';

@Injectable()
export class VpoGroupRowActionDispatcherService extends GroupRowActionDispatcherService {
  actionMap: { [action: string]: Observable<Type<AbstractUIAction>> } = {
    ...this.actionMap,
    VpoEditChainOfCustodyRequirementAction: defer(() =>
      import('../action/group-grid-action').then((a) => a.VpoEditChainOfCustodyRequirementAction),
    ),
    VpoCopyChainOfCustodyGroupRowAction: defer(() =>
      import('../action/group-grid-action').then((a) => a.VpoCopyChainOfCustodyGroupRowAction),
    ),
    VpoRefreshChainOfCustodyRequirementAction: defer(() =>
      import('../action/group-grid-action').then((a) => a.VpoRefreshChainOfCustodyRequirementAction),
    ),
    VpoSelectChainOfCustodyRequirementAction: defer(() =>
      import('../action/group-grid-action').then((a) => a.VpoSelectChainOfCustodyRequirementAction),
    ),
  };
}
