import { As<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>I<PERSON> } from '@angular/common';
import { Component, ChangeDetectionStrategy, HostBinding, inject } from '@angular/core';

import { getTime } from 'date-fns';
import { DateLabelFormatPipe } from 'projects/chat/src/lib/pipe/date-label-format.pipe';
import { HtmlToPlainTextPipe } from 'projects/chat/src/lib/pipe/html-to-plain-text.pipe';
import * as R from 'ramda';
import { combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';

import { WidgetDirective } from '../../dynamic-widget/widget-message.directive';
import { IconComponent } from 'src/app/component/icon/icon.component';
import { Widget_CommunicationDetailDto } from 'src/app/entities/api';
import { DATE_TYPE } from 'src/app/entities/date-type';
import { AuthService } from 'src/app/services/auth.service';

@Component({
  selector: 'app-communication-detail',
  templateUrl: './communication-detail.component.html',
  styleUrls: ['./communication-detail.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [AsyncPipe, NgFor, DateLabelFormatPipe, NgIf, IconComponent, HtmlToPlainTextPipe],
})
export class CommunicationDetailComponent extends WidgetDirective<Widget_CommunicationDetailDto> {
  private readonly authService = inject(AuthService);

  @HostBinding('class') class = 'items-center justify-center';

  dateFormat$ = this.authService.dateFormat$;
  dateType = DATE_TYPE.datetime;

  sortedData$ = this.filteredData$.pipe(
    map((data) => R.sort((a, b) => getTime(new Date(b.publishedOn)) - getTime(new Date(a.publishedOn)), data)),
  );

  isViewAllButtonExist$ = combineLatest([this.rowSpan$, this.sortedData$]).pipe(
    map(([rowSpan, sortedData]) => (rowSpan === 2 ? sortedData.length > 4 : sortedData.length > 9)),
  );

  openCommunication(record: Widget_CommunicationDetailDto) {
    window.open(`/document/information/communication/${record.refNo}`, '_blank');
  }

  directToListing() {
    window.open('/listing/information/communication/communicationActiveView?urlParams={creatorExclude=true}', '_blank');
  }
}
