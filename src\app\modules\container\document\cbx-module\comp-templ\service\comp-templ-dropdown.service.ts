import { Injectable } from '@angular/core';

// import * as R from 'ramda';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject, combineLatest, distinctUntilChanged, map, Observable } from 'rxjs';

import { DocumentDropdownService } from '../../../service/document-dropdown.service';
import { DocumentDataQuery } from '../../../state';
import { CBX_URL } from 'src/app/config/constant';
import { CodelistItem } from 'src/app/entities/codelist';
import { ApiService } from 'src/app/services/api.service';
import { deepEqual, isNotEmptyOrNil } from 'src/app/utils';

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class CompTemplDropdownService extends DocumentDropdownService {
  dropdownStore = {
    listModules: new BehaviorSubject([]),
    listLevelType: new BehaviorSubject([]),
    listLevels: new BehaviorSubject([]),
    sectionStore: new BehaviorSubject([]),
  };
  dropdownOptionFilter = {
    levelFilter: (options: CodelistItem[], rowIndex?: number) => this.levelFilter(options, rowIndex),
  };
  constructor(protected readonly documentDataQuery: DocumentDataQuery, private readonly apiService: ApiService) {
    super();
  }

  private levelFilter(options: any[], rowIndex?: number): Observable<CodelistItem[]> {
    return combineLatest([
      this.documentDataQuery.select('applyModule'),
      this.documentDataQuery.selectForGrid('compTemplSectionList', rowIndex, 'levelType'),
    ]).pipe(
      map(([applyModule, levelType]) => {
        const module = applyModule?.module;
        const options2 = options.filter((option) => option?.module === module && levelType?.code === option?.levelType);

        return options2;
      }),
    );
  }
  registerBusinessLogic() {
    // const aql$ = this.getLookupList('INSP_SAMPLING_PLAN');
    const listModules$ = this.apiService.get(CBX_URL.listModules);

    listModules$.subscribe((listModules) => {
      const array = listModules as any[];

      const moduleList = array.filter((obj) => isNotEmptyOrNil(obj.label)).map((obj) => ({ ...obj, name: obj.label }));
      this.setDropdownStoreByName('listModules', moduleList);
    });
    const listLevel$ = this.apiService.get(CBX_URL.listLevel);
    listLevel$.subscribe((listLevel) => {
      const array = listLevel as any[];

      const listLevels = array
        .filter((obj) => isNotEmptyOrNil(obj.level))
        .map((obj) => ({ ...obj, name: obj.entityLookup }));
      this.setDropdownStoreByName('listLevels', listLevels);
    });
    this.documentDataQuery
      .select('applyModule')
      .pipe(distinctUntilChanged(deepEqual), untilDestroyed(this))
      .subscribe((applyModule) => {
        const options = [];
        options.push({ name: 'header', code: 'header' });
        if (applyModule?.module !== 'item') {
          options.push({ name: 'first child level', code: 'firstChildLevel' });
        }
        this.setDropdownStoreByName('listLevelType', options);
      });

    const sectionTable$: Observable<any> = this.documentDataQuery.select('compTemplSectionList');

    sectionTable$.subscribe((compTemplSectionList) => {
      const array = compTemplSectionList?.filter((o) => !!o?.sectionName).map((o) => ({ ...o, name: o.sectionName }));
      this.setDropdownStoreByName('sectionStore', array);
    });
  }
}
