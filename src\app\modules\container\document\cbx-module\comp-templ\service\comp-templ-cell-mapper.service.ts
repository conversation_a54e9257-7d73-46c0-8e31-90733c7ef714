import { Injectable } from '@angular/core';

import { addSuffix } from '../../../../../../utils';
import {
  editComponents,
  viewComponents,
} from '../../../grid-section-content/grid-section-grid/cell/cell-component-type';
import { CellMapperService } from '../../../service/cell-mapper.service';

@Injectable()
export class CompTemplCellMapperService extends CellMapperService {
  viewComponents = addSuffix({ ...viewComponents }, '');
  editComponents = addSuffix({ ...editComponents }, this.editSuffix);

  cellComponents = {
    ...this.cellComponents,
    ...this.viewComponents,
    ...this.editComponents,
  };
}
