import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, HostBinding, inject, TemplateRef } from '@angular/core';

import { HtmlToPlainTextPipe } from 'projects/chat/src/lib/pipe/html-to-plain-text.pipe';

import { AngularCellComponent, CellRendererAngularComp } from '../cell-renderer-params';
import { CellRendererParams } from 'src/app/modules/container/document/grid-section-content/grid-section-grid/cell/cell-component';
import { FlexiblePosition } from 'src/app/services/popover/popover-position';
import { PopoverRef } from 'src/app/services/popover/popover-ref';
import { PopoverService } from 'src/app/services/popover/popover.service';

@Component({
  selector: 'app-rich-text-cell',
  templateUrl: './rich-text-cell.component.html',
  styleUrls: ['./rich-text-cell.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule, HtmlToPlainTextPipe],
})
export class RichTextCellComponent extends AngularCellComponent implements CellRendererAngularComp {
  @HostBinding('class') class = 'app-text-cell block overflow-hidden position-relative';

  private readonly popper = inject(PopoverService);

  popoverRef: PopoverRef;
  params: CellRendererParams;

  htmlValue: string;

  agInit(params: CellRendererParams) {
    this.params = params;
    this.htmlValue = params.valueFormatted || params.value;
  }

  refresh(): boolean {
    return false;
  }

  open(event: MouseEvent, origin: HTMLElement, contentTemplate: TemplateRef<any>) {
    this.popoverRef = this.popper.open<any>({
      content: contentTemplate,
      width: this.host.nativeElement.offsetWidth,
      height: this.host.nativeElement.offsetHeight,
      positionType: 'flexible',
      positionParams: {
        origin,
        flexiblePositionType: FlexiblePosition.Overlap,
      },
    }).popoverRef;

    event.stopPropagation();
    event.preventDefault();
  }
}
