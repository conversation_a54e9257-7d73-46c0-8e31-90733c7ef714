$content-padding: 10px;
$cpm-height: 30px;
$cpmBarWrapperHeight: 30px;

:host {
  background-color: #f8f8f8;
}

.cpm-initialized-button {
  height: $cpm-height;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.27px;
  border-top: 1px solid var(--color-primary-white);
  background-color: var(--color-primary-black-85);
  padding: 0 8px;
  color: var(--color-primary-white);
}

.cpm-initialize-button {
  &:disabled {
    pointer-events: none;
  }
}

.show-cpm-bar-icon {
  transition: transform 0.2s ease-in-out;
  transform: rotate(0);
}

.rotate {
  transform: rotate(180deg);
}

.container {
  grid-template-columns: auto 1fr auto;
  position: relative;
}

.sidebar-wrapper {
  width: 198px;
  transition: width 0.2s ease-out;
  z-index: 1;
}

.right-panel-wrapper {
  width: 0;
  transition: width 0.2s ease-out;
  overflow: hidden;

  &.expand {
    width: 402px;
  }
}

.collapse-sidebar {
  width: 0;
}

.sidebar {
  grid-template-rows: auto 1fr auto auto;
  width: 198px;
  background: var(--color-primary-gray-85);

  &-title {
    font-size: 18px;
    font-weight: bold;
    letter-spacing: 0.22px;
    color: var(--color-text-inverse);
  }

  &-description {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
}

.back-button {
  font-size: 12px;
  transition: 0.2s ease-in-out;
  &:hover,
  &:focus {
    transform: translateX(-2px);
  }
}

.hide-sidebar-btn,
.open-sidebar-btn {
  outline: none;
  border: none;
  background-color: transparent;
  cursor: pointer;
  padding: 0;
  &:hover {
    color: var(--color-primary-light-blue);
  }
}

.hide-sidebar-btn {
  height: 24px;
  color: var(--color-primary-white);
  margin: 8px 12px 8px auto;
}

.open-sidebar-wrapper {
  width: 0;
  &.sidebar-collapse {
    width: 24px;
  }
}

.open-sidebar-btn {
  height: 100%;
  color: var(--color-primary-light-blue);

  &:hover {
    background-color: rgba(63, 77, 85, 0.06);
    z-index: 101;
  }
}

.document-content {
  padding-left: 16px;
  &.sidebar-collapse {
    padding-left: 0;
  }
}

.content-wrapper {
  grid-template-rows: auto auto 1fr auto auto;
  grid-template-areas:
    'header'
    'copyActions'
    'main'
    'editFooter'
    'cpmBar';
}

.content-header {
  grid-area: header;
}

.content-copy-actions {
  grid-area: copyActions;
}

.content-main {
  grid-area: main;
  grid-template-columns: auto 1fr;
}

.content-edit-footer {
  grid-area: editFooter;
}

.content-cpm-bar {
  grid-area: cpmBar;
}

.document-header {
  grid-auto-rows: auto;
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  z-index: 2;
  padding: 10px 48px 3px 16px;
}

.header-action-wrapper {
  height: auto;
  margin-bottom: ($content-padding - 6px);
  overflow: hidden;
}

.menu-bar-action-wrapper {
  max-width: 80%;
  align-self: center;
}

.link-bar-action-wrapper {
  height: 42px;
  overflow-x: scroll;
  overflow-y: hidden;
  margin-right: 16px;
}

.doc-info {
  &-title {
    line-height: 16px;
    margin-right: 6px;
    font-size: 14px;
    font-weight: 500;
    color: var(--color-primary-gray-72);
    word-break: break-word;
  }

  &-version {
    font-size: 12px;
    color: var(--color-primary-gray-72);
  }

  &-status {
    font-size: 12px;
    letter-spacing: 0.14px;
    color: var(--color-primary-gray);
    text-align: center;
    font-weight: bold;
    min-width: 54px;
    padding: 0 5px;
    opacity: 0.85;
    border-radius: 2px;
  }
}

.version-warning {
  width: 300px;
  border-radius: 2px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.4);
  background-color: #fde999;
  padding-right: 8px;
  padding-left: 4px;
  font-size: 11px;
  color: #485962;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
}

.info-icon {
  color: #485962;
}

.direct-page-hyperlink {
  margin-left: 5px;
  cursor: pointer;
}

.approval-button-wrapper,
.acknowledge-button-wrapper {
  position: sticky;
  top: 32px;
  text-align: right;
  padding-right: 30px;
  height: 0;
  z-index: 100;
}

.cpm-bar-wrapper {
  height: $cpmBarWrapperHeight;
}

.approval-button,
.acknowledge-button {
  margin-left: 10px;
  min-width: 80px;
  height: 32px;
  border-radius: 23px;
  box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.3);
  color: var(--color-primary-white);
  letter-spacing: 0.23px;
  margin-bottom: 8px;
}

.withdraw-button {
  background-color: var(--color-primary-gray);
}

.reject-button {
  background-color: #eb5858;
}

.approve-button,
.acknowledge-button {
  background-color: var(--color-secondary-green);
}

.status {
  margin: 0 4px;

  &-label {
    color: var(--color-text-default);
    background-color: var(--color-primary-gray-72);
    height: 14px;
    padding: 0 7px;
    opacity: 0.72;
    line-height: 14px;
    border-radius: 2px;
    font-size: 12px;
    font-weight: 400;
    text-align: center;
  }

  &-pending {
    background-color: var(--color-secondary-yellow);
  }

  &-creator {
    font-size: 12px;
    color: var(--color-primary-gray-72);
  }
}

.action-loading {
  transform: scale(0.7);
}

.horizontal-line {
  width: 0;
  height: 12px;
  border: solid 1px #bfbfbf;
  margin: 0 10px;
}

.close {
  top: 0;
  right: 12px;
}

.approval-label {
  color: var(--color-primary-white);
  padding: 0 7px;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  border-radius: 2px;
  height: 14px;
  line-height: 14px;
}

.status-type {
  font-size: 8px;
  font-weight: 500;
  text-align: center;
}

.approval-status-approved {
  background-color: var(--color-secondary-green);
}

.approval-status-rejected {
  background-color: #eb5858;
}

.approval-status-pending {
  background-color: #efab3d;
}

.approval-status-withdrawn {
  background-color: #bfbfbf;
}

.back-icon {
  color: #e6e6e6;
}
.loading-wrapper {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(188, 188, 188, 0.3);
}

.validation-loading {
  pointer-events: auto;
  opacity: 1;
}

.subject-view {
  color: var(--color-primary-gray);
  height: 19px;
  overflow: hidden;
}

.overlay-host {
  z-index: 2;
}

.doc-info-title2 {
  max-width: 570px;
  font-size: 20px;
  font-weight: 500;

  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.moduleStyle {
  border-radius: 5px;
  color: white;
  font-size: 20px;
  padding-left: 5px;
  padding-right: 5px;
  //text-align: center;
  white-space: nowrap;
}

.width-90 {
  width: 90%;
}

.padding-top-3 {
  padding-top: 3px;
}

.compare-btn {
  height: 52px;
  background-color: #fff;
  box-shadow: 6px 0 10px 0 rgba(72, 89, 98, 0.2);
  overflow: hidden;
  z-index: 1;
  grid-area: editFooter;
  text-align: right;
  align-items: center;
  display: flex;
  overflow-x: auto;
  padding-right: 48px;
  justify-content: flex-end;
}

.undo-button {
  background-color: gray;
}

.headerMidSection {
  flex-direction: row-reverse;
}
