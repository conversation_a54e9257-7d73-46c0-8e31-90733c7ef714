.border {
  width: 246px;
  padding: 8px 8px 8px 6px;
  margin-left: 10px;
  filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.4));
  background-color: var(--color-primary-gray);
  color: white;
  border-radius: 2px;

  &::before {
    content: '';
    width: 10px;
    height: 10px;
    background-color: var(--color-primary-gray);
    position: absolute;
    top: 48%;
    left: -5px;
    transform: rotate(45deg);
  }
}

.content {
  font-size: 11px;
  letter-spacing: 0.14px;
  line-height: 16px;
}

.icon-wrapper {
  margin-right: 2px;
}

.passed-icon {
  margin-right: 4px;
  color: var(--color-icon-inverse);
  height: 16px;
}

.dot-icon {
  border-radius: 50%;
  min-width: 4px;
  height: 4px;
  margin: 6px;
  margin-right: 10px;
  background-color: white;
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.sub-ul {
  padding-left: 12px;
  margin-top: 2px;
}
