import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostBinding,
  Input,
  OnDestroy,
  OnInit,
  Optional,
  ViewContainerRef,
  effect,
  inject,
  signal,
  Output,
  EventEmitter,
} from '@angular/core';

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import {
  CellClassParams,
  CellClickedEvent,
  ColDef,
  ColumnPivotModeChangedEvent,
  ColumnState,
  EditableCallbackParams,
  GridApi,
  GridOptions,
  GridReadyEvent,
  KeyCreatorParams,
  RowDataUpdatedEvent,
  RowDragCallbackParams,
  RowDragEndEvent,
  RowNode,
  IRowNode,
  RowSelectedEvent,
  ValueFormatterParams,
  ValueGetterParams,
  ValueSetterParams,
  CellFocusedEvent,
  Column,
} from 'ag-grid-community';
import * as R from 'ramda';
import { BehaviorSubject, combineLatest, NEVER, Observable, of, ReplaySubject, Subject } from 'rxjs';
import {
  auditTime,
  distinctUntilChanged,
  filter,
  finalize,
  map,
  shareReplay,
  startWith,
  switchMap,
  take,
  tap,
} from 'rxjs/operators';

import {
  FormDefine_FormFieldTypes,
  FormDefine_ReferenceData,
  FormDefine_RowMovedTransformHook,
} from '../../../../entities/api';
import { ColumnSize, FieldDefine, MenuItem } from '../../../../entities/form-field';
import { FormFieldTypes } from '../../../../entities/form-field-types';
import { AnyObject, ColumnDefs } from '../../../../interface/model';
import { GridContextParams } from '../../../../interface/model/grid-context-params';
import { CONDITION_SYMBOL, SearchFilter } from '../../../../services/search/search.service';
import {
  checkConditionsValidInObject,
  checkHookCondition,
  deepClone,
  deepEqual,
  divideCalculator,
  dotPath,
  generateUUID,
  getNumberFromString,
  immutableMove,
  isArray,
  isEmptyOrNil,
  isNotEmptyOrNil,
  resolveFormat,
} from '../../../../utils';
import { DocumentModuleActionService } from '../cbx-module/tokens';
import { PositionSectionContentDirective } from '../directive/position-section-content.directive';
import { SectionStateDirective } from '../directive/section-state.directive';
import {
  CellActionTrigger,
  CellEditorParams,
  CellRendererParams,
  CellValueChange,
  GroupRowTrigger,
} from '../grid-section-content/grid-section-grid/cell/cell-component';
import { GridSectionGridComponent } from '../grid-section-content/grid-section-grid/grid-section-grid/grid-section-grid.component';
import { GridSectionMenuBarComponent } from '../grid-section-content/grid-section-menu-bar/grid-section-menu-bar.component';
import { GridActionDispatcherService } from '../grid-section-content/service/grid-action-dispatcher.service';
import { GridCustomRowDragService } from '../grid-section-content/service/grid-custom-row-drag.service';
import { GridEventSideEffectService } from '../grid-section-content/service/grid-event-side-effect.service';
import { GridSectionService } from '../grid-section-content/service/grid-section.service';
import { InlineComparisonRowCellComponent } from '../inline-comparison/inline-comparison-row-cell.component';
import { InlineComparisonService } from '../inline-comparison/inline-comparison.service';
import { GridCascadeHandler } from '../presenter/cascade-presenter';
import { ComponentState } from '../service/cell-mapper-constant';
import { CellMapperService } from '../service/cell-mapper.service';
import { DataTransformService } from '../service/data-transform.service';
import { DocumentDynamicDataService } from '../service/document-dynamic-data.service';
import { DocumentDynamicFieldService } from '../service/document-dynamic-field.service';
import { DocumentFieldReadonlyService } from '../service/document-field-readonly.service';
import { DocumentGridApiService } from '../service/document-grid-api.service';
import { DocumentGridCellClickService } from '../service/document-grid-cell-click.service';
import { GridSectionHookToken } from '../service/hook/grid-section-hook';
import { ValidationHandler } from '../service/validation-handler.service';
import {
  DocumentComparisonQuery,
  DocumentDataQuery,
  DocumentDataService,
  DocumentDefineQuery,
  DocumentDefineService,
  DocumentDefineStore,
  DocumentStatus,
  DocumentStatusService,
  Section,
  SubSectionsQuery,
} from '../state';
import { DocumentCommentPanelService } from '../state/document-comment-panel.service';
import { DocumentCopyDataService } from '../state/document-copy-data.service';
import { DocumentGridCellValueChange, DocumentGridRowValueChange } from 'src/app/interface/model/web-socket-frame';
import { DocumentGridFilterService } from 'src/app/modules/container/document/service/document-grid-filter.service';
import { DocumentService } from 'src/app/modules/services/document.service';
import { FieldBackgroundColorfulService } from 'src/app/modules/services/field-background-colorful.service';
import { DocumentOptionRequireStrategy } from 'src/app/modules/shared/common/base-component/base-dropdown/document-option-require-strategy';
import { CellSortingEvent } from 'src/app/modules/shared/common/cbx-table/cell-action-event.model';
import { AgGridRowHeightDirective } from 'src/app/modules/shared/directives/ag-grid-row-height.directive';
import { CalculateHeightChildDirective } from 'src/app/modules/shared/directives/axis/calculate-height-child.directive';
import { CalculateHeightDirective } from 'src/app/modules/shared/directives/axis/calculate-height.directive';
import { ConfigurationService } from 'src/app/services/configuration.service';
import { NotificationService } from 'src/app/services/notification.service';
import { NumberFormatService } from 'src/app/services/number-format.service';
import { InputObservable } from 'src/app/utils/input-subject-observer';
import { nothing$ } from 'src/app/utils/observers';
import { DomainAttributeService } from 'src/app/workspace/service/domain-attribute.service';

function generateNewPathAndMove(
  movedData: AnyObject<any>[],
  parentCode: any,
  node: IRowNode,
  overIndex: number,
  treeField: string,
  sectionId: string,
  changes: DocumentGridRowValueChange[] = [],
  dynamicChanges: DocumentGridRowValueChange[] = [],
) {
  // move
  const fromIndex = movedData.findIndex((row) => row.id === node.id);
  let movedDataTemp = immutableMove(movedData, fromIndex, overIndex);
  const afterRowDelta = fromIndex > overIndex ? -1 : 0;
  changes.push({
    sectionId,
    changeType: 'move',
    rowId: node.id,
    afterRowId: movedData[overIndex + afterRowDelta]?.id,
    rowIndex: overIndex,
    fromRowIndex: fromIndex,
  });
  // change tree field value
  const currentCode = parentCode + node.key;
  const cloneRow = deepClone(movedDataTemp[overIndex]);
  if (cloneRow === null) {
    return { moveData: movedDataTemp, changes, dynamicChanges };
  }
  cloneRow[treeField] = currentCode;
  movedDataTemp.splice(overIndex, 1, cloneRow);
  // In drag & drop couldn't show update event in change history, so we need to add dynamicLogic
  dynamicChanges.push({ sectionId, changeType: 'update', rowId: node.id, content: cloneRow, rowIndex: fromIndex });

  // handle next leaf
  node.childrenAfterGroup.forEach((childNode: IRowNode) => {
    const currentCodeStr = `${currentCode}::`;
    movedDataTemp = generateNewPathAndMove(
      movedDataTemp,
      currentCodeStr,
      childNode,
      overIndex + 1,
      treeField,
      sectionId,
      changes,
      dynamicChanges,
    ).moveData;
  });
  return { moveData: movedDataTemp, changes, dynamicChanges };
}

const checklistFillableContentType = { TEXT: true, MEMO_TEXT: true };

const fieldFillableRuleMap = {
  [FormFieldTypes.checklistField]: (fillValue: any, currentValue: any) =>
    checklistFillableContentType[fillValue?.type] && checklistFillableContentType[currentValue?.type],
};

interface GridInvalidState {
  [fieldId: string]: {
    rowIdSet: Set<string>;
    hasColumnInvalid: boolean;
  };
}

@UntilDestroy()
@Component({
  selector: 'app-grid-section-container',
  templateUrl: './grid-section-container.component.html',
  styleUrls: ['./grid-section-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [GridSectionService, GridCustomRowDragService, DocumentOptionRequireStrategy, SectionStateDirective],
  standalone: true,
  imports: [
    CommonModule,
    PositionSectionContentDirective,
    GridSectionMenuBarComponent,
    CalculateHeightDirective,
    CalculateHeightChildDirective,
    AgGridRowHeightDirective,
    GridSectionGridComponent,
  ],
})
export class GridSectionContainerComponent implements OnInit, OnDestroy {
  @HostBinding('class') class = 'app-grid-section-container block';

  private readonly gridSectionHook = inject(GridSectionHookToken);
  private readonly vcr = inject(ViewContainerRef);
  private readonly validationHandler = inject(ValidationHandler);
  @Output() gridApiOutPut = new EventEmitter<GridReadyEvent>();

  private readonly dataInitializing$ = this.documentDataQuery
    .selectDataInitialized$()
    .pipe(filter((dataInitialized) => dataInitialized));

  @Input() detailExpandBtn: boolean;

  checkboxCol: ColDef = {
    colId: 'checkBox',
    headerComponent: 'commonCheckboxHeader',
    width: 37,
    minWidth: 37,
    pinned: 'left',
    resizable: false,
    lockPosition: true,
    cellClass: ['cbx-cell', 'edit-mode', 'built-in-cell'],
    // cellRenderer: FormFieldTypes.commonCheckbox,
    cellClassRules: {
      'section-row': (params) => !!params.node.allChildrenCount,
    },
    cellRendererSelector: (params) => {
      if (
        (params?.data?.treePath && params?.data?.treePath?.includes('::')) ||
        params?.node?.rowPinned === 'bottom' ||
        params?.node?.allChildrenCount
      ) {
        return { component: null };
      }
      return { component: FormFieldTypes.commonCheckbox };
    },
  };
  deleteRowCol: ColDef = {
    colId: 'deleteRowCol',
    width: 37,
    minWidth: 37,
    pinned: 'left',
    resizable: false,
    lockPosition: true,
    cellRenderer: FormFieldTypes.deleteRow,
    cellClass: ['cbx-cell', 'edit-mode', 'built-in-cell'],
  };

  fields$: Observable<FieldDefine[]>;
  sourceData$: Observable<AnyObject<any>[]>;
  data$: Observable<AnyObject<any>[]>;
  colDefs$: Observable<ColDef[]>;
  selectedRows: AnyObject<any>[];

  context: GridContextParams;
  components = this.cellMapperService.cellComponents;
  groupRowRendererParams: any;
  rowHeight$: Observable<number>;
  isAddRemoveColumn: boolean;
  isTreeData: boolean;
  treeField: string;
  showField: string;
  allowDuplicateTree: boolean;
  treeUniqueKeyField: string;

  groupDefaultExpanded: number; // 0 | 1 | -1
  switchColumnGroupId$ = new BehaviorSubject<string>('');
  autoGroupColumnDef$: Observable<ColDef>;
  enableGroupEdit: boolean;
  animateRows: boolean;

  checkboxes: boolean;

  getDataPath: (row: any) => string[];

  @Input() moduleId: string;
  @Input() tabId: string;
  section$ = new ReplaySubject<Section>(1);
  @InputObservable()
  @Input()
  section: Section;

  focusRow$: Observable<AnyObject<any>[]>;

  gridApiSubject$ = new Subject<GridApi>();
  gridApi$ = this.gridApiSubject$.asObservable().pipe(filter((gridApi) => !!gridApi));
  formId: string;

  data: AnyObject<any>[];
  fields: FieldDefine[];

  private readonly asyncError$ = this.documentDefineQuery.select((state) => state.ui.asyncError);

  private readonly gridError$ = this.section$.pipe(
    map((section) => section?.id),
    distinctUntilChanged(),
    switchMap((sectionId) =>
      sectionId ? this.asyncError$.pipe(map((asyncError) => asyncError.grids[sectionId])) : of(null),
    ),
    map((gridError) => [...(gridError?.rowField ?? []), ...(gridError?.column ?? [])]),
  );

  private getColumnId(column: Column) {
    return column.getId() === 'ag-Grid-AutoColumn' ? this.showField : column.getId();
  }

  private readonly invalidCache = signal<GridInvalidState>({});
  private invalid = false;

  private readonly ensureFirstInvalidColumnVisibleEffect = effect(() => {
    const invalidCache = this.invalidCache();
    if (this.invalid) {
      const firstErrorColumn = this.gridApi.getAllGridColumns().find((column) => {
        const colId = this.getColumnId(column);
        return invalidCache[colId];
      });

      if (firstErrorColumn) {
        this.gridApi.ensureColumnVisible(firstErrorColumn);
      }
    }
  });

  private readonly ensureInvalidGroupColumnOpenedEffect = effect(() => {
    const invalidCache = this.invalidCache();
    if (this.invalid) {
      const gridColumns = this.gridApi.getAllGridColumns();
      const parentGroupIds = new Set<string>([]);

      gridColumns.forEach((column) => {
        const colId = this.getColumnId(column);
        if (invalidCache[colId] && column.getColumnGroupShow()) {
          parentGroupIds.add(column.getParent()?.getGroupId());
        }
      });

      parentGroupIds.forEach((groupId: string) => {
        this.gridApi.setColumnGroupOpened(groupId, true);
      });
    }
  });

  private checkCellError(params: CellClassParams) {
    if (this.invalid) {
      const colId = this.getColumnId(params.column);
      const columnError = this.invalidCache()[colId];
      if (columnError) {
        return columnError.hasColumnInvalid || columnError.rowIdSet.has(params.data?.id);
      }
    }
    return false;
  }

  gridErrorMessages$ = this.section$.pipe(
    map((section) => section?.id),
    distinctUntilChanged(),
    switchMap((sectionId) =>
      sectionId ? this.documentDefineQuery.select((state) => state.ui.asyncError.grids[sectionId]) : of(null),
    ),
    map((gridError) => [...(gridError?.grid ?? []), ...(gridError?.row ?? [])]),
    map((errors) => errors.map((error) => error.errorMessage)),
  );

  isGridChange: boolean;
  showProposedChanges$ = this.documentDataQuery.select('showProposedChanges').pipe(distinctUntilChanged());

  hasInlinePending$ = this.inlineComparisonService.hasInlinePending$;

  footerStaticText$: Observable<string>;
  footerReferenceText$: Observable<string>;

  valueChange$ = new Subject<CellValueChange>();
  customValueChange$ = new Subject<CellValueChange>();
  treeValueChange$ = new Subject<CellValueChange>();
  actionBus$ = new Subject<CellActionTrigger>();
  gridScrollBus$ = new Subject<boolean>();
  autoFillBus$ = new Subject<any>();
  autoFillProcessing$ = new BehaviorSubject<boolean>(false);
  groupRowActionBus$ = new ReplaySubject<GroupRowTrigger>(1);
  sortingBus$ = new Subject<CellSortingEvent>();
  gridApi: GridApi;

  columnPivotModeChanged$ = new BehaviorSubject<ColumnPivotModeChangedEvent>(null);
  isPivotMode$ = this.columnPivotModeChanged$.pipe(map((columnPivot) => columnPivot?.api?.isPivotMode()));

  groupTotalRow$: Observable<'top' | 'bottom' | undefined> = this.isPivotMode$.pipe(
    map((isPivotMode) =>
      isPivotMode ? this.section.pivotMeta?.groupIncludeFooter : this.section.gridMeta?.groupConfig?.groupIncludeFooter,
    ),
    map((groupIncludeFooter) => (groupIncludeFooter ? 'bottom' : undefined)),
  );

  grandTotalRow$: Observable<'top' | 'bottom' | undefined> = this.isPivotMode$.pipe(
    map((isPivotMode) =>
      isPivotMode
        ? this.section.pivotMeta?.groupIncludeTotalFooter
        : this.section.gridMeta?.groupConfig?.groupIncludeTotalFooter,
    ),
    map((groupIncludeTotalFooter) => (groupIncludeTotalFooter ? 'bottom' : undefined)),
  );

  filterBus: { filters: SearchFilter[]; fieldDefine: FieldDefine }[] = [];
  filterBus$ = new BehaviorSubject<{ filters: SearchFilter[]; fieldDefine: FieldDefine }[]>(null);
  sortingChanged$ = new BehaviorSubject<boolean>(false);

  private valueGetterMapByType: Partial<{
    [key in FormFieldTypes]: (params: ValueGetterParams) => any;
  }>;

  statusChanged$ = this.documentStatusService.getDocumentStatus$();
  editStatus$ = this.statusChanged$.pipe(map((status) => status === DocumentStatus.Edit));
  viewStatus$ = this.statusChanged$.pipe(map((status) => status === DocumentStatus.View));

  private potentialNode?: RowNode;

  private readonly selectProgressingRowNodeSet = new Set<IRowNode>();

  gridOptions: GridOptions = {
    isExternalFilterPresent: () => isNotEmptyOrNil(this.filterBus),
    doesExternalFilterPass: this.doesExternalFilterPass.bind(this),
    autoSizePadding: 0,
    rowSelection: {
      mode: 'multiRow',
      headerCheckbox: false,
      checkboxes: false,
      hideDisabledCheckboxes: true,
    },
    cellSelection: true,
    onRowGroupOpened: () => {
      const { gridChapterIdList } = this.documentDynamicFieldService;
      if (isNotEmptyOrNil(gridChapterIdList) && gridChapterIdList?.indexOf(this.section?.id) !== -1) {
        setTimeout(() => {
          this.documentDynamicFieldService.gridChange$.next(true);
        }, 300);
      }
    },
  };

  groupRowDisplayType: string;
  autoGroupFieldDef: FieldDefine;

  hasCustomSelectedRow$: Observable<boolean>;

  enableValueMap = {
    [FormFieldTypes.number]: true,
    [FormFieldTypes.decimal]: true,
  };

  enableGroupMap = {
    [FormFieldTypes.text]: true,
    [FormFieldTypes.dropdown]: true,
    [FormFieldTypes.label]: true,
    [FormFieldTypes.hyperlink]: true,
    [FormFieldTypes.number]: true,
  };

  private readonly hyperlinkFieldMap = new Set<FormDefine_FormFieldTypes>([FormFieldTypes.hyperlink]);

  private readonly numberFieldMap = new Set<FormDefine_FormFieldTypes>([
    FormFieldTypes.number,
    FormFieldTypes.decimal,
    FormFieldTypes.seq,
  ]);

  private readonly imageFieldMap = new Set<FormDefine_FormFieldTypes>([
    FormFieldTypes.image,
    FormFieldTypes.moreImage,
    FormFieldTypes.color,
  ]);

  cellClicked: (event: CellClickedEvent) => void;

  actionExecuting$ = new BehaviorSubject(false);

  cellFocused$ = new Subject<CellFocusedEvent>();

  refreshFocusingData$ = new Subject<void>();

  // onceInView$ = onceInView(this.host.nativeElement).pipe(untilDestroyed(this), shareReplay(1));

  // sentinelHeight$: Observable<number>;

  treeField$: Observable<FieldDefine>;

  // focusingSection$ = this.section$.pipe(
  //   switchMap(({ id }) =>
  //     this.documentConcurrentEditService.latestFocusSectionId$.pipe(map((sectionId) => sectionId === id)),
  //   ),
  // );

  destroyed$ = new Subject<void>();

  sectionDisableAutoSize: boolean = false;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly host: ElementRef<HTMLElement>,
    private readonly subSectionsQuery: SubSectionsQuery,
    private readonly documentDataQuery: DocumentDataQuery,
    private readonly documentDataService: DocumentDataService,
    private readonly cellMapperService: CellMapperService,
    private readonly numberFormatService: NumberFormatService,
    private readonly documentDynamicDataService: DocumentDynamicDataService,
    private readonly documentDynamicFieldService: DocumentDynamicFieldService,
    private readonly documentFieldReadonlyService: DocumentFieldReadonlyService,
    private readonly documentDefineStore: DocumentDefineStore,
    private readonly documentDefineQuery: DocumentDefineQuery,
    private readonly notificationService: NotificationService,
    private readonly documentStatusService: DocumentStatusService,
    private readonly documentCopyDataService: DocumentCopyDataService,
    private readonly gridSectionService: GridSectionService,
    private readonly documentDefineService: DocumentDefineService,
    private readonly documentGridFilterService: DocumentGridFilterService,
    @Optional() private readonly documentModuleActionService: DocumentModuleActionService,
    @Optional() private readonly sectionStateDirective: SectionStateDirective,
    @Optional() private readonly documentGridApiService: DocumentGridApiService,
    private readonly documentGridCellClickService: DocumentGridCellClickService,
    private readonly gridEventSideEffectService: GridEventSideEffectService,
    private readonly actionDispatcherService: GridActionDispatcherService,
    private readonly gridCustomRowDragService: GridCustomRowDragService,
    private readonly configurationService: ConfigurationService,
    private readonly fieldBackgroundColorfulService: FieldBackgroundColorfulService,
    private readonly optionsRequireStrategy: DocumentOptionRequireStrategy,
    private readonly documentComparisonQuery: DocumentComparisonQuery,
    private readonly dataTransformService: DataTransformService,
    private readonly documentCommentPanelService: DocumentCommentPanelService,
    private readonly inlineComparisonService: InlineComparisonService,
    private readonly documentService: DocumentService,
    private readonly domainAttributeService: DomainAttributeService,
  ) {}

  ngOnInit() {
    this.focusRow$ = this.documentDynamicDataService.getFocusRow(this.section.id);
    this.gridOptions = {
      ...this.gridOptions,
      paginateChildRows: this.section.gridMeta?.paginateChildRows || false,
    };

    this.formId = this.documentDefineQuery.getValue()?.id;
    const { groupConfig, treeConfig } = this.section?.gridMeta ?? {};
    this.enableGroupEdit = groupConfig?.enableGroupEdit;
    this.animateRows = !!this.section?.gridMeta?.animateRows;
    this.sectionDisableAutoSize = !!this.section?.gridMeta?.disableAutoSize;
    this.groupDefaultExpanded = groupConfig?.groupDefaultExpanded ?? treeConfig?.groupDefaultExpanded ?? 0;
    this.groupRowDisplayType = groupConfig?.displayType;
    this.autoGroupFieldDef = groupConfig?.autoGroupField;
    const treeField = treeConfig?.treeField;
    this.treeField = treeField;
    this.isTreeData = !!treeField;
    this.showField = treeConfig?.showField ?? this.autoGroupFieldDef?.id ?? '';
    this.allowDuplicateTree = treeConfig?.allowDuplicateTree;
    this.treeUniqueKeyField = treeConfig?.treeUniqueKeyField;
    // TODO row height related params should use @Input send to child component to use
    this.context = {
      moduleId: this.moduleId,
      sectionId: this.section.id,
      lineNumber: this.section.gridMeta.lineNumber || 1,
      sortingBus$: this.sortingBus$,
      gridScrollBus$: this.gridScrollBus$,
      gridType: 'grid',
      tabId: this.tabId,
      entityName: this.section.gridMeta.entityName,
    };

    this.getDataPath = (row: any) => {
      const data = row[this.treeField];
      if (!data) {
        return [''];
      }
      return isArray(data) ? data : data.split('::');
    };

    this.fields$ = this.subSectionsQuery.getSubSection$(this.section.subSections[0]).pipe(
      map((subSection) => (subSection && subSection.fields) || []),
      switchMap((fields) => this.documentDynamicFieldService.getDynamicFieldLogical(of(fields), this.section.id)),
      distinctUntilChanged<FieldDefine[]>(deepEqual),
      tap((fields) => {
        this.fields = fields;
      }),
      tap(() => this.prepareRowRendererParams()),
      map((fields) =>
        fields.map((field) => {
          if (field?.type === FormFieldTypes.multiple) {
            const fixedWidth = `${100 / field.fields.length}%`;
            return {
              ...field,
              type: FormFieldTypes.fieldGroup,
              fields: field.fields.map((f) => ({ ...f, fixedWidth })),
            } as FieldDefine;
          }
          return field;
        }),
      ),
      switchMap((fields) =>
        this.configurationService.CONFIG.FLAG_APP_FIELD_BACKGROUND_COLORFUL
          ? this.fieldBackgroundColorfulService.backgroundColorMap$.pipe(
              map((backgroundColorMap) => {
                const sectionBackgroundColorMap = backgroundColorMap[this.moduleId]?.[this.section.id];

                return sectionBackgroundColorMap
                  ? this.deepAppendBackgroundColorMap(fields, sectionBackgroundColorMap)
                  : fields;
              }),
            )
          : of(fields),
      ),
      untilDestroyed(this),
      shareReplay(1),
    ) as Observable<FieldDefine[]>;

    this.sourceData$ = this.dataInitializing$.pipe(
      switchMap(() =>
        combineLatest([
          this.documentDataQuery.select(this.section.id),
          this.showProposedChanges$,
          this.documentComparisonQuery.getGridChangeMaxRowNum$(this.section.uiId),
        ]),
      ),
      map(([data, showProposedChanges, maxRowNum]) => [data ?? [], showProposedChanges, maxRowNum]),
      switchMap(([data, showProposedChanges, maxRowNum]) =>
        this.documentDynamicDataService.getFilterData(this.section.id, data).pipe(
          map((filteredData) => {
            if (
              showProposedChanges &&
              this.isGridChange &&
              isNotEmptyOrNil(maxRowNum) &&
              Number(maxRowNum) > 0 &&
              data.length < Number(maxRowNum)
            ) {
              const emptyRowsCount = Number(maxRowNum) - data.length;
              const emptyRows = R.repeat({}, emptyRowsCount);
              return R.concat(filteredData, emptyRows);
            }
            return filteredData;
          }),
        ),
      ),
      distinctUntilChanged(deepEqual),
      tap((data) => {
        this.data = data;
      }),
      untilDestroyed(this),
      shareReplay(1),
    );

    this.data$ = this.cellFocused$.pipe(
      startWith({ rowIndex: null, column: null }),
      auditTime(0),
      switchMap((focused) =>
        this.refreshFocusingData$.pipe(
          startWith(null),
          map(() => focused),
        ),
      ),
      switchMap(() => this.sourceData$),
      distinctUntilChanged(),
      // TODO:(ce): check do we need this
      // tap(() => this.viewportIndicatorDirective.trigger$.next(true)),
      untilDestroyed(this),
      shareReplay(1),
    );

    this.valueGetterMapByType = {
      [FormFieldTypes.itemCheckbox]: (params: ValueGetterParams) => params.node.isSelected(),
      [FormFieldTypes.fieldGroup]: (params: ValueGetterParams) => params.data,
    };

    const noTreeFieldFields$ =
      this.isTreeData && !this.section.gridMeta.treeConfig.autoGroupField
        ? this.fields$.pipe(
            map((fields) => fields.filter((field) => field.id !== this.treeField)),
            untilDestroyed(this),
            shareReplay(1),
          )
        : this.fields$;

    const deepFilterFields = (fields: FieldDefine[], strategy: (field: FieldDefine) => boolean): FieldDefine[] =>
      fields
        .filter(strategy)
        .map((field) => (field.fields ? { ...field, fields: deepFilterFields(field.fields, strategy) } : field));

    const viewColDefs$ = combineLatest([noTreeFieldFields$, this.switchColumnGroupId$]).pipe(
      map(([fields, switchColumnGroupId]) => ({
        fields: deepFilterFields(fields, (field) => !field.onlyExistInEditMode),
        switchColumnGroupId,
      })),
      map(({ fields, switchColumnGroupId }) => {
        const columnDefs = this.prependMultipleGroupCols(this.generateViewColDefs(fields, switchColumnGroupId), true);

        const showCheckboxCell = !!this.section.gridMeta?.alwaysSelectable;
        // const showDeleteRowCol = this.section.gridMeta?.showDeleteRowCol;
        return showCheckboxCell ? [this.checkboxCol, ...columnDefs] : [...columnDefs];
      }),
      untilDestroyed(this),
      shareReplay(1),
    );

    const copyColDefs$ = combineLatest([noTreeFieldFields$, this.switchColumnGroupId$]).pipe(
      map(([fields, switchColumnGroupId]) => ({
        fields: deepFilterFields(fields, (field) => !field.onlyExistInEditMode),
        switchColumnGroupId,
      })),
      map(({ fields, switchColumnGroupId }) => {
        const columnDefs = this.prependMultipleGroupCols(this.generateViewColDefs(fields, switchColumnGroupId));
        return [this.checkboxCol, ...columnDefs];
      }),
      untilDestroyed(this),
      shareReplay(1),
    );

    const partialCopyColDefs$ = combineLatest([noTreeFieldFields$, this.switchColumnGroupId$]).pipe(
      map(([fields, switchColumnGroupId]) => ({
        fields: deepFilterFields(fields, (field) => !field.onlyExistInEditMode),
        switchColumnGroupId,
      })),
      map(({ fields, switchColumnGroupId }) => {
        const columnDefs = this.prependMultipleGroupCols(this.generateViewColDefs(fields, switchColumnGroupId));
        return this.isShowCheckBox() ? [this.checkboxCol, ...columnDefs] : [...columnDefs];
      }),
      untilDestroyed(this),
      shareReplay(1),
    );

    this.statusChanged$
      .pipe(
        filter((status) => status === DocumentStatus.Copy),
        switchMap(() => combineLatest([this.gridApi$, this.documentStatusService.getGlobalCopyStatus$()])),
        untilDestroyed(this),
      )
      .subscribe({
        next: ([gridApi, checked]) => (checked ? gridApi.selectAll() : gridApi.deselectAll()),
      });

    this.statusChanged$
      .pipe(
        filter((status) => status === DocumentStatus.View),
        untilDestroyed(this),
      )
      .subscribe(() => this.masterToggle(false));

    this.statusChanged$
      .pipe(
        filter((status) => status === DocumentStatus.PartialCopy),
        switchMap(() => this.documentStatusService.getGlobalCopyStatus$()),
        untilDestroyed(this),
      )
      .subscribe((checked) => (this.isShowCheckBox() ? this.masterToggle(checked) : NEVER));

    const editColDefs$ = combineLatest([noTreeFieldFields$, this.switchColumnGroupId$]).pipe(
      map(([fields, switchColumnGroupId]) => ({
        fields: deepFilterFields(fields, (field) => !field.onlyExistInViewMode),
        switchColumnGroupId,
      })),
      map(({ fields, switchColumnGroupId }) => {
        const columnDefs = this.prependMultipleGroupCols(this.generateEditColDefs(fields, switchColumnGroupId));
        return columnDefs;
      }),
      untilDestroyed(this),
      shareReplay(1),
    );

    this.colDefs$ = this.statusChanged$.pipe(
      switchMap((status) => {
        switch (status) {
          case DocumentStatus.View: {
            return viewColDefs$;
          }
          case DocumentStatus.Copy: {
            return copyColDefs$;
          }
          case DocumentStatus.PartialCopy: {
            return partialCopyColDefs$;
          }
          case DocumentStatus.Edit: {
            return editColDefs$;
          }
          default: {
            return of([]);
          }
        }
      }),
    );

    const treeField$ = this.isTreeData
      ? this.section.gridMeta.treeConfig.autoGroupField
        ? of(this.section.gridMeta.treeConfig.autoGroupField)
        : this.fields$.pipe(map((fields) => fields.find((field) => field.id === this.treeField)))
      : null;

    this.autoGroupColumnDef$ = treeField$
      ? combineLatest([this.statusChanged$, this.data$]).pipe(
          switchMap(([status, data]) =>
            treeField$.pipe(
              map((field) => {
                const maxDepth = Math.max(...data.map((row) => row.ids?.length));
                if (isEmptyOrNil(field)) {
                  return null;
                }

                switch (status) {
                  case DocumentStatus.View:
                  case DocumentStatus.Copy:
                  case DocumentStatus.PartialCopy: {
                    return this.generateViewTreeGroupColDef(field, maxDepth);
                  }
                  case DocumentStatus.Edit: {
                    return this.generateEditTreeGroupColDef(field, maxDepth);
                  }
                  default:
                    return null;
                }
              }),
            ),
          ),
        )
      : of({
          cellRendererParams: (params: CellRendererParams) => {
            if (params.node.footer) {
              return {
                innerRenderer: 'Footer',
              };
            }
            return null;
          },
        });

    // row grouping
    setTimeout(() => {
      if (!this.isTreeData) {
        const autoGroupField$ = this.section.gridMeta.groupConfig?.autoGroupField
          ? of(this.section.gridMeta.groupConfig?.autoGroupField)
          : null;
        this.autoGroupColumnDef$ = autoGroupField$
          ? this.statusChanged$.pipe(
              switchMap((status) =>
                autoGroupField$.pipe(
                  map((field) => {
                    if (isEmptyOrNil(field)) {
                      return null;
                    }

                    switch (status) {
                      case DocumentStatus.View:
                      case DocumentStatus.Copy:
                      case DocumentStatus.PartialCopy: {
                        return this.generateViewTreeGroupColDef(field, 0);
                      }
                      case DocumentStatus.Edit: {
                        return this.generateEditTreeGroupColDef(field, 0);
                      }
                      default:
                        return null;
                    }
                  }),
                ),
              ),
            )
          : of({
              cellRendererParams: (params: CellRendererParams) => {
                if (params.node.footer) {
                  return {
                    innerRenderer: 'Footer',
                  };
                }
                return null;
              },
            });
      }
    }, 10);

    const fieldDefaultHeight = {
      // [FormFieldTypes.image]: 54,
      // [FormFieldTypes.textarea]: 54,
      // [FormFieldTypes.tableSelect]: 60,
      // [FormFieldTypes.inputSelect]: 60,
      // [FormFieldTypes.attachList]: 60,
      // [FormFieldTypes.attachTable]: 60,
      [FormFieldTypes.datetime]: 45,
    };

    if (this.tabId === 'tabItem') {
      this.rowHeight$ = this.fields$.pipe(
        map((fields) =>
          Math.max(
            ...fields.map((field) => fieldDefaultHeight[field.type] ?? 0),
            (this.section.gridMeta.lineNumber || 1) * 16 + 19,
          ),
        ),
      );
    } else {
      this.rowHeight$ = this.fields$.pipe(
        map((fields) =>
          Math.max(
            ...fields.map((field) => fieldDefaultHeight[field.type] ?? 0),
            (this.section.gridMeta.lineNumber || 1) * 16 + 16,
          ),
        ),
      );
    }

    this.handleCompare();
    this.handleFooter();

    this.cellClicked = this.documentGridCellClickService.cellClickMap[this.section.id]
      ? (event: CellClickedEvent) => {
          this.documentGridCellClickService.cellClickMap[this.section.id](event, this.host);
        }
      : // jira: PCHI-349, if grid cell have special click event, also need add this method.
        (event: CellClickedEvent) => {
          this.documentCommentPanelService.gridCellClick(event);
        };
    this.gridEventSideEffectService.registerSectionInView[this.section.id]?.(this.host.nativeElement);

    this.hasCustomSelectedRow$ = this.documentDynamicDataService.hasCustomSelectedRowMap[this.section.id];
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  // updateSectionHeight(height: number) {
  //   this.documentStatusService.updateSectionHeight(this.section.id, height);
  // }

  gridReady(event: GridReadyEvent) {
    this.gridApiSubject$.next(event.api);
    this.gridApi = event.api;
    this.gridApiOutPut.emit(event);
    this.gridSectionService.gridApi = event.api;
    this.documentGridApiService?.addGridApi(this.section.id, event.api);

    this.valueChange$.pipe(untilDestroyed(this)).subscribe(({ value, field, currentRowData }) => {
      if (field.id === 'tree' || field.id === 'treePath') {
        const { treeConfig } = this.section?.gridMeta ?? {};
        const treeField = treeConfig?.treeField;
        const showField = treeConfig?.showField ?? '';
        const allowDuplicateTree = treeConfig?.allowDuplicateTree;
        const treeUniqueKeyField = treeConfig?.treeUniqueKeyField;

        const treeValue: string = currentRowData[treeField];
        const parentCode =
          treeValue?.lastIndexOf('::') === -1 ? '' : treeValue?.substring(0, treeValue.lastIndexOf('::') + 2);
        const tree = parentCode + value;

        if (allowDuplicateTree) {
          this.documentDataService.updateCellValueByTreeValue1(
            this.section.id,
            currentRowData,
            field,
            parentCode,
            treeUniqueKeyField,
            showField,
            value,
          );
        } else {
          this.documentDataService.updateCellValueByTreeValue3(this.section.id, field, currentRowData?.id, tree);
        }
        this.documentDataService.updateCellValueByTreeValue(this.section.id, currentRowData, field, tree);
      } else if (this.section.id === 'elementList') {
        if (value?.code === 'SUBTOTAL' && field.id === 'sourceType') {
          this.documentDataService.updateCellValueByFieldId(
            { id: 'SubTotal', code: 'SubTotal', name: 'Formula', reference: 'SUBTOTAL' },
            'elementList',
            null,
            'source',
            currentRowData.id,
          );
        } else if (field.id === 'basis' && currentRowData?.sourceCurrency) {
          const sourceCurrency = currentRowData?.sourceCurrency;
          if (
            value?.name === '% of Subtotal' ||
            value?.name === '(1-x%) of Sub-total' ||
            value?.name === 'Sub-total/x%' ||
            value?.name === 'Sub-total/(1-x%)'
          ) {
            if (sourceCurrency.name !== '%') {
              this.documentDataService.updateCellValueByFieldId(
                { ...sourceCurrency, backupName: sourceCurrency.name, name: '%' },
                'elementList',
                null,
                'sourceCurrency',
                currentRowData.id,
              );
            }
          } else if (sourceCurrency.name === '%' && sourceCurrency.backupName) {
            this.documentDataService.updateCellValueByFieldId(
              { ...sourceCurrency, name: sourceCurrency.backupName },
              'elementList',
              null,
              'sourceCurrency',
              currentRowData.id,
            );
          }
        }
      }
      if (this.section.id === 'vpoItemList' && !currentRowData.itemId) {
        const { vpoItemList, vpoShipDtlDtoGroupList } = this.documentDataQuery.getValue();

        if (field.id === 'noOfInner') {
          let packPlanedQty = 0;
          vpoItemList
            .filter((vpoItem) => vpoItem.treePath.includes(`${currentRowData.treePath}::`))
            .forEach((vpoItem) => {
              vpoItem.isPackUpdateField = true;
              vpoItem.planedQty = vpoItem.qtyPerInnerCarton * value;
              packPlanedQty += vpoItem.planedQty;
              vpoItem.noOfInner = value;
              vpoItem.variance = vpoItem.planedQty - vpoItem.shipQty;
              vpoItem.noOfCartons = value / vpoItem.innerPerOuter;
            });

          vpoItemList
            .filter((vpoItem) => vpoItem.treePath === currentRowData.treePath)
            .forEach((vpoItem) => {
              vpoItem.planedQty = packPlanedQty;
              vpoItem.variance = vpoItem.planedQty - vpoItem.shipQty;
              vpoItem.noOfCartons = value / vpoItem.innerPerOuter;
            });
        }
        if (field.id === 'noOfCartons') {
          let packPlanedQty = 0;
          let packNoOfInner = 0;
          vpoItemList
            .filter((vpoItem) => vpoItem.treePath.includes(`${currentRowData.treePath}::`))
            .forEach((vpoItem) => {
              vpoItem.isPackUpdateField = true;
              vpoItem.noOfCartons = value;
              vpoItem.noOfInner = value * vpoItem.innerPerOuter;
              packNoOfInner = vpoItem.noOfInner;
              vpoItem.planedQty = vpoItem.qtyPerInnerCarton * vpoItem.noOfInner;
              packPlanedQty += vpoItem.planedQty;
              vpoItem.variance = vpoItem.planedQty - vpoItem.shipQty;
            });

          vpoItemList
            .filter((vpoItem) => vpoItem.treePath === currentRowData.treePath)
            .forEach((vpoItem) => {
              vpoItem.planedQty = packPlanedQty;
              vpoItem.variance = vpoItem.planedQty - vpoItem.shipQty;
              vpoItem.noOfInner = packNoOfInner;
            });
        }
        if (field.id === 'pricePerInner') {
          let shipDtlNoOfInners = 0;
          vpoShipDtlDtoGroupList.forEach((vpoShipDtlDtoGroup) =>
            vpoShipDtlDtoGroup
              .filter((vpoShipDtlDto) => vpoShipDtlDto.treePath === currentRowData.treePath)
              .forEach((vpoShipDtlDto) => {
                shipDtlNoOfInners += vpoShipDtlDto.noOfInner;
              }),
          );

          vpoItemList
            .filter((vpoItem) => vpoItem.treePath === currentRowData.treePath)
            .forEach((vpoItem) => {
              vpoItem.pricePerCarton = value * vpoItem.innerPerOuter;
              vpoItem.totalAmt = value * shipDtlNoOfInners;
            });
        }
        if (field.id === 'pricePerCarton') {
          let shipDtlNoOfInners = 0;
          vpoShipDtlDtoGroupList.forEach((vpoShipDtlDtoGroup) =>
            vpoShipDtlDtoGroup
              .filter((vpoShipDtlDto) => vpoShipDtlDto.treePath === currentRowData.treePath)
              .forEach((vpoShipDtlDto) => {
                shipDtlNoOfInners += vpoShipDtlDto.noOfInner;
              }),
          );
          vpoItemList
            .filter((vpoItem) => vpoItem.treePath === currentRowData.treePath)
            .forEach((vpoItem) => {
              vpoItem.pricePerInner = divideCalculator(value, vpoItem.innerPerOuter);
              vpoItem.totalAmt = vpoItem.pricePerInner * shipDtlNoOfInners;
            });
        }
        this.documentDataService.updateData({ vpoItemList });
      }

      if (this.section.id === 'packingListPrepackList') {
        if (field.id === 'expectedInnerQty') {
          const { packingListPrepackList } = this.documentDataQuery.getValue();
          packingListPrepackList
            .filter(
              (packingListPrepack) =>
                `${packingListPrepack.vendorPoShipDtlRef}/${packingListPrepack.vpoNo}` ===
                `${currentRowData.vendorPoShipDtlRef}/${currentRowData?.vpoNo}`,
            )
            .forEach((packingListPrepack) => {
              packingListPrepack.expectedOuterQty = value / currentRowData.innerPerOuter;
            });
        }
        if (field.id === 'expectedOuterQty') {
          const { packingListPrepackList } = this.documentDataQuery.getValue();
          packingListPrepackList
            .filter(
              (packingListPrepack) =>
                `${packingListPrepack.vendorPoShipDtlRef}/${packingListPrepack.vpoNo}` ===
                `${currentRowData.vendorPoShipDtlRef}/${currentRowData?.vpoNo}`,
            )
            .forEach((packingListPrepack) => {
              packingListPrepack.expectedInnerQty = value * currentRowData.innerPerOuter;
            });
        }
      }
      this.documentStatusService.setUserChangeValue(true);
    });

    this.treeValueChange$.pipe(untilDestroyed(this)).subscribe({
      next: ({ value, field, params }) => {
        const treeValue: string = params.data[this.treeField];
        const rowId: string = params.data?.id;
        const parentCode =
          treeValue?.lastIndexOf('::') === -1 ? '' : treeValue?.substring(0, treeValue.lastIndexOf('::') + 2);
        if (this.allowDuplicateTree) {
          this.documentDataService.updateCellValueByTreeValue1(
            this.section.id,
            params.data,
            field,
            parentCode,
            this.treeUniqueKeyField,
            this.showField,
            value,
          );
        } else {
          const newTree = parentCode + value;
          this.documentDataService.updateCellValueByTreeValue3(this.section.id, field, rowId, newTree);
        }
        this.documentStatusService.setUserChangeValue(true);
      },
    });

    this.fields$
      .pipe(
        this.documentDefineService.getRedrawRowFromColumnIds(
          this.cellMapperService.redrawRowFromColumnIds[this.section.id],
        ),
        switchMap((redrawRowFromColumnIds) =>
          isNotEmptyOrNil(redrawRowFromColumnIds)
            ? this.valueChange$.pipe(
                auditTime(0),
                filter(({ field }) => !!redrawRowFromColumnIds[field.id]),
              )
            : nothing$,
        ),
        untilDestroyed(this),
      )
      .subscribe({
        next: ({ node }) => event.api.redrawRows({ rowNodes: [node] }),
      });

    this.actionBus$.pipe(untilDestroyed(this)).subscribe(({ field, params, isGridAction, action }) => {
      const moduleAction = this.documentModuleActionService?.handleGridAction({
        action: field,
        records: [params.data],
        moduleId: this.moduleId,
        docRefNo: this.documentDataQuery.getValue().refNo,
      });
      if (moduleAction) {
        this.handleModuleAction(moduleAction, field);
        return;
      }

      if (isGridAction) {
        this.handleGridAction(action, { cellRendererParams: params });
      }
    });

    this.sectionStateDirective?.updateAgGridApi(event.api);

    this.gridCustomRowDragService.registerGridApi(event.api);
    this.gridCustomRowDragService.dragMove$.pipe(untilDestroyed(this)).subscribe({
      next: (e) => {
        this.setPotentialNode(e);
      },
    });

    this.gridCustomRowDragService.dragEnded$.pipe(untilDestroyed(this)).subscribe({
      next: ({ node, overNode, fieldDefine }) => {
        this.resetPotentialNode();

        if (node === overNode) {
          return;
        }

        const customRowDrag =
          this.gridEventSideEffectService.handleGridCustomRowDrag[this.section.id]?.[fieldDefine.id];
        if (customRowDrag) {
          this.data$.pipe(take(1), untilDestroyed(this)).subscribe({
            next: (rows) => {
              const movedData = customRowDrag(node, overNode, rows);
              if (movedData) {
                this.documentDataService.updateData({ [this.section.id]: movedData });
                this.documentStatusService.setUserChangeValue(true);
                this.gridEventSideEffectService.afterRowDragEnd[this.section.id]?.({
                  node,
                  overNode,
                  api: this.gridApi,
                } as any);
              }
            },
          });
        }
      },
    });

    const sectionId = isEmptyOrNil(this.section?.gridMeta?.useConfigFromSectionId)
      ? this.section.id
      : this.section.gridMeta?.useConfigFromSectionId;
    this.documentDynamicDataService.registerGridApi[sectionId]?.(event.api, this.destroyed$, this.section);
    this.sortingBus$.pipe(untilDestroyed(this)).subscribe((sorting) => this.sortChanged(sorting));
    this.autoFillBus$.pipe(untilDestroyed(this)).subscribe((autoFillBus) => this.autoFillProcessing$.next(autoFillBus));

    this.gridSectionHook.hookForContainer({
      destroyed$: this.destroyed$,
      valueChange$: this.valueChange$,
      treeValueChange$: this.treeValueChange$,
      customValueChange$: this.customValueChange$,
      section: this.section,
      fields$: this.fields$,
      refreshFocusingData$: this.refreshFocusingData$,
    });

    this.handleRecordCount();
    setTimeout(() => {
      if (this.documentStatusService.getDocumentStatus() === DocumentStatus.View) {
        this.fields?.forEach((field) => {
          if (field.defaultFilter) {
            const searchFilter: SearchFilter = {
              joinOperator: null,
              condition: field.defaultFilter,
            };
            const filters = [searchFilter];
            this.updateFilterBus({ filters, fieldDefine: field });
          }
          const filters: SearchFilter[] = [];
          if (field.defaultFilterList !== undefined) {
            field.defaultFilterList.forEach((defaultFilter) => {
              let joinOperator = null;
              if (defaultFilter.joinOperator !== undefined) {
                if (defaultFilter.joinOperator === 'and') {
                  joinOperator = CONDITION_SYMBOL.and;
                } else if (defaultFilter.joinOperator === 'globalOr') {
                  joinOperator = CONDITION_SYMBOL.globalOr;
                } else if (defaultFilter.joinOperator === 'or') {
                  joinOperator = CONDITION_SYMBOL.or;
                }
              }
              const defaultFilterCondition: SearchFilter = {
                condition: defaultFilter.condition,
                joinOperator,
              };
              filters.push(defaultFilterCondition);
            });
            this.updateFilterBus({ filters, fieldDefine: field });
          }
        });
      }
    }, 500);

    this.handleGridInvalid();
  }

  private handleGridInvalid() {
    this.gridError$.pipe(untilDestroyed(this)).subscribe({
      next: (cellErrors) => {
        this.invalid = !!cellErrors.length;

        const paths = cellErrors.map((cellError) => this.validationHandler.parsePath(cellError));

        const invalidCache: GridInvalidState = {};

        paths.forEach((path) => {
          if (!invalidCache[path.fieldId]) {
            invalidCache[path.fieldId] = { rowIdSet: new Set<string>(), hasColumnInvalid: false };
          }
          if (path.rowId) {
            invalidCache[path.fieldId].rowIdSet.add(path.rowId);
          } else {
            invalidCache[path.fieldId].hasColumnInvalid = true;
          }
        });
        this.invalidCache.set(invalidCache);
        this.gridSectionService.refreshGrid();
      },
    });
  }

  private handleRecordCount() {
    this.dataInitializing$
      .pipe(
        switchMap(() => this.documentDataQuery.select(this.section.id)),
        map((data) => data ?? []),
        switchMap((data) => this.documentDynamicDataService.getFilterData(this.section.id, data)),
        map((data) => data?.length ?? 0),
        distinctUntilChanged(deepEqual),
        untilDestroyed(this),
      )
      .subscribe((count) => this.documentService.updateRecordCountBySection(this.section.id, count));
  }

  cellFill({
    params,
    startIndex,
    endIndex,
    direction,
    value,
    rowData,
  }: {
    params: CellRendererParams;
    startIndex: number;
    endIndex: number;
    direction: 1 | -1;
    value: any;
    rowData: any;
  }) {
    const { field } = params;
    const sectionId = isEmptyOrNil(this.section?.gridMeta?.useConfigFromSectionId)
      ? this.section.id
      : this.section.gridMeta?.useConfigFromSectionId;

    let pointer = startIndex + direction;
    const bound = endIndex + direction;

    const isReadonly = (rowParams) =>
      this.documentFieldReadonlyService.getCellReadonlyLogic(sectionId, field, rowParams);

    const canRenderComponent = field.onlyRenderInEditMode
      ? () => false
      : field.editRenderConditions
      ? (data) => checkConditionsValidInObject(field.editRenderConditions, data)
      : field.renderConditions
      ? (data) => checkConditionsValidInObject(field.renderConditions, data)
      : () => true;

    const fieldFillableRule = fieldFillableRuleMap[field.type] ?? (() => true);

    const hasEditor = this.cellMapperService.getEditorComponent(params, field);

    while (pointer !== bound) {
      const data = rowData[pointer];
      const rowIndex = pointer;
      const node = this.gridApi.getRowNode(data.id);

      if (isEmptyOrNil(node)) {
        pointer += direction;
        // eslint-disable-next-line no-continue
        continue;
      }
      const cellRenderer = this.gridApi.getCellRendererInstances({
        rowNodes: [node],
        columns: [field.id],
      })?.[0] as any;

      const isEditable = hasEditor
        ? params.column.isCellEditable(node)
        : !isReadonly(cellRenderer?.params) &&
          canRenderComponent(node.data) &&
          fieldFillableRule(value, this.gridApi.getValue(params.column, node));

      if (isEditable) {
        this.checkFillValueValid(value, field, sectionId, rowIndex, data?.id)
          .pipe(take(1))
          .subscribe((isFillValueValid) => {
            if (isFillValueValid) {
              if (this.cellMapperService.separateGridCellFillValueSetter[sectionId]?.[field.id]) {
                this.documentStatusService.setUserChangeValue(true);
                this.cellMapperService.separateGridCellFillValueSetter[sectionId][field.id](data, field, value);
                return;
              }
              let targetValue: any;

              if (this.documentDynamicDataService.customCellFillMap[field.type]) {
                targetValue = this.documentDynamicDataService.customCellFillMap[field.type](
                  this.section.id,
                  data,
                  field,
                  value,
                  this.gridApi.getValue(params.column, node),
                );

                this.gridApi.refreshCells({ rowNodes: [node], columns: [field.id], force: true });
              } else {
                targetValue = value;
              }

              this.documentDataService.updateCellValueByRowId(this.section.id, data, field, targetValue, this.fields);
              this.valueChange$.next({ value: targetValue, field, rowId: data.id, currentRowData: data, node });
            }
          });
      }

      pointer += direction;
    }

    // TODO:(ce) check do we need to move this to concurrent service
    this.refreshFocusingData$.next();
  }

  checkFillValueValid(
    fillValue: any,
    field: FieldDefine,
    sectionId: string,
    rowIndex: number,
    rowId: string,
  ): Observable<boolean> {
    if (isNotEmptyOrNil(fillValue) && field.type === FormFieldTypes.dropdown) {
      const cascadeHandler = new GridCascadeHandler(field, sectionId, rowIndex, rowId, null);
      return this.optionsRequireStrategy
        .getOption(field, cascadeHandler, rowIndex)
        .pipe(
          map(
            (options) =>
              !!options?.find((option) => option?.name === resolveFormat(field.format || '{name}', fillValue)),
          ),
        );
    }
    return of(true);
  }

  onSelectRowChanged(selectedRows: any) {
    this.selectedRows = selectedRows;
    if (
      this.documentStatusService.getDocumentStatus() === DocumentStatus.Copy ||
      this.documentStatusService.getDocumentStatus() === DocumentStatus.PartialCopy ||
      this.documentStatusService.getDocumentStatus() === DocumentStatus.View
    ) {
      this.documentCopyDataService.updateData({ [this.section.id]: selectedRows });
    }
  }

  setPotentialNode(event: { node: IRowNode; overNode?: IRowNode }) {
    const { node, overNode } = event;

    if (!overNode || overNode === this.potentialNode) {
      return;
    }

    this.potentialNode?.setHighlighted(null);

    if (node === overNode) {
      this.potentialNode = null;
      return;
    }

    // No highlight setting function in IRowNode. Workaround is cast to 'RowNode'. (AG-7352)
    this.potentialNode = overNode as RowNode;

    const highlightPosition = node.rowIndex < overNode.rowIndex ? 1 : 0;

    (overNode as RowNode).setHighlighted(highlightPosition);
  }

  resetPotentialNode() {
    this.potentialNode?.setHighlighted(null);
    this.potentialNode = null;
  }

  closestSameTypeParentNode(
    type: string,
    key: string,
    node?: IRowNode | null,
  ): { node: IRowNode; direction: 'topToLow' } | null {
    if (!node) {
      return null;
    }
    if (node.data?.[key] === type) {
      return { node, direction: 'topToLow' };
    }

    return this.closestSameTypeParentNode(type, key, node.parent);
  }

  closestSameTypeChildNode(
    type: string,
    key: string,
    node?: IRowNode | null,
  ): { node: IRowNode; direction: 'lowToTop' } | null {
    if (!node || !type) {
      return null;
    }
    if (node.data?.[key] === type) {
      return { node, direction: 'lowToTop' };
    }

    return this.closestSameTypeChildNode(type, key, node.childrenAfterGroup?.[0]);
  }

  notSameLevelNode(testNode: IRowNode, currentNode: IRowNode | null): boolean {
    if (!testNode) {
      return true;
    }
    if (!currentNode) {
      return true;
    }
    if (currentNode === testNode) {
      return false;
    }
    return this.notSameLevelNode(testNode.parent, currentNode.parent);
  }

  parentIncludeNode(testNode: IRowNode, currentNode: IRowNode | null): boolean {
    if (!currentNode) {
      return false;
    }
    if (currentNode === testNode) {
      return true;
    }
    return this.parentIncludeNode(testNode, currentNode.parent);
  }

  moveInArray(arr: any[], movingCount: number, fromIndex: number, toIndex: number) {
    const element = arr.splice(fromIndex, movingCount);
    arr.splice(toIndex, 0, ...element);
  }

  // node == moving node
  onRowDragEnd(rowDragEnd: RowDragEndEvent) {
    this.resetPotentialNode();

    const changes: DocumentGridRowValueChange[] = [];
    const dynamicChanges: DocumentGridRowValueChange[] = [];
    const cellChanges: DocumentGridCellValueChange[] = [];

    const movingNode1 = rowDragEnd?.node;
    if (!this.section.gridMeta?.treeConfig) {
      const parentNode = movingNode1?.parent?.parent?.parent ?? movingNode1?.parent?.parent ?? movingNode1?.parent;
      parentNode?.allLeafChildren?.sort((a, b) => a.rowIndex - b.rowIndex);
      const maxRowIndex = Math.max(...(parentNode?.allLeafChildren ?? []).map((node) => node.rowIndex));
      const orginalAllNodes = new Array(maxRowIndex + 1).fill(undefined);
      parentNode?.allLeafChildren.forEach((node) => {
        orginalAllNodes[node.rowIndex] = node;
      });
      parentNode?.childrenAfterGroup.forEach((node) => {
        orginalAllNodes[node.rowIndex] = node;
      });
      const pageSize = this.gridApi.paginationGetPageSize();
      const whichPage = Math.floor(movingNode1.rowIndex / pageSize) + 1;
      const startIndexInThePage = (whichPage - 1) * pageSize;
      const endIndexInThePage = (whichPage - 1) * pageSize + pageSize - 1;
      const rowDatasInThePage = orginalAllNodes.slice(startIndexInThePage, endIndexInThePage + 1);
      let currentHeight = 0;
      let realTargetIndex;
      for (let i = 0; i < rowDatasInThePage.length; i += 1) {
        const rowTop = currentHeight;
        currentHeight += rowDatasInThePage[i].rowHeight;
        if (rowDragEnd.y >= rowTop && rowDragEnd.y < currentHeight) {
          realTargetIndex = rowDatasInThePage[i].rowIndex;
        }
      }
      if (realTargetIndex === undefined || realTargetIndex === null) {
        return;
      }
      rowDragEnd.overIndex = realTargetIndex;
      rowDragEnd.overNode = orginalAllNodes.find((node: { rowIndex: any }) => node.rowIndex === realTargetIndex);
    }
    let overNode1 = rowDragEnd?.overNode;
    const isGroup = overNode1?.group && overNode1?.allLeafChildren;
    overNode1 = isGroup ? overNode1.allLeafChildren.find((child) => child.firstChild) : overNode1;
    let movingData = movingNode1?.data;
    const overData = overNode1?.data;
    if (movingData?.testType?.name !== overData.testType?.name) {
      return;
    }
    if (rowDragEnd.node === rowDragEnd.overNode) {
      return;
    }

    const treeFieldSeparator = this.section.gridMeta?.treeConfig?.treeFieldSeparator ?? '/';

    const groupField = this.section.gridMeta?.groupConfig?.groupField;

    if (movingData && overData && groupField) {
      const multipleGroupFields = this.section?.gridMeta?.groupConfig?.multipleGroupFields;
      const suppressCrossGroup = this.section?.gridMeta?.groupConfig?.suppressCrossGroup;

      if (movingData[groupField] !== overData[groupField] && !!suppressCrossGroup) {
        let sectionLabel = this.section?.gridMeta?.groupConfig?.sectionLabel;
        if (sectionLabel) {
          sectionLabel = sectionLabel.slice(1, -1);
        }
        movingData = sectionLabel
          ? { ...movingData, [groupField]: overData[groupField], [sectionLabel]: overData[sectionLabel] }
          : { ...movingData, [groupField]: overData[groupField] };
      }
      multipleGroupFields?.forEach((multipleGroupField) => {
        if (movingData[multipleGroupField] !== overData[multipleGroupField]) {
          movingData = { ...movingData, [multipleGroupField]: overData[multipleGroupField] };
        }
      });

      this.gridApi.clearFocusedCell();

      const rowIndex = this.documentDataQuery.getValue()[this.section.id].findIndex((r) => r.id === movingData.id);

      this.documentDataService.updateRowValueByIndex(this.section.id, rowIndex, movingData);

      changes.push({
        sectionId: this.section.id,
        changeType: 'update',
        rowId: movingData.id,
        content: movingData,
        rowIndex,
      });
    }

    this.sourceData$.pipe(take(1), untilDestroyed(this)).subscribe((rows) => {
      let movedData: any[];
      const treeField = this.section.gridMeta.treeConfig?.treeField;
      if (treeField !== undefined) {
        let newRowData = [...rows];
        if (this.section.gridMeta.treeConfig?.restrictedSameLayerDrag) {
          const { tieredParentMap, tieredKey } = this.section.gridMeta.treeConfig;

          const movingNode = rowDragEnd.node;
          const movingType: string = movingNode.data[tieredKey];

          const { node: overNode, direction } =
            this.closestSameTypeParentNode(movingType, tieredKey, rowDragEnd.overNode) ??
            this.closestSameTypeChildNode(tieredParentMap[movingType], tieredKey, rowDragEnd.overNode) ??
            {};

          if (!overNode) {
            return;
          }

          if (this.parentIncludeNode(movingNode, overNode)) {
            return;
          }

          const fromIndex = rows.findIndex((row) => row === movingNode.data) ?? 0;
          const overIndex = rows.findIndex((row) => row === overNode.data) ?? 0;
          const movingCount = (movingNode.allChildrenCount ?? 0) + 1;
          const overCount = overNode.allChildrenCount ?? 0;
          const fromOffset = overIndex > fromIndex ? movingCount - 1 : 0;
          const toOffset = overIndex > fromIndex ? overCount : 0;
          const directionOffset = direction === 'lowToTop' ? (overIndex > fromIndex ? 0 - overCount : 1) : 0;
          const toIndex = overIndex - fromOffset + toOffset + directionOffset;

          if (fromIndex === toIndex) {
            return;
          }

          const overGroupNode = direction === 'lowToTop' ? overNode : overNode.parent;

          if (movingNode.parent?.data?.[treeField] !== overGroupNode?.data?.[treeField]) {
            const parentId = overGroupNode?.data[treeField];
            const { length } = parentId;
            for (let baseIndex = 0; baseIndex < movingCount; baseIndex += 1) {
              const targetIndex = baseIndex + fromIndex;
              const row = deepClone(newRowData[targetIndex]);
              row[treeField] = [...parentId, ...row[treeField].slice(length)];
              newRowData.splice(targetIndex, 1, row);
              // In drag & drop couldn't show update event in change history, so we need to add dynamicLogic
              changes.push({
                sectionId: this.section.id,
                changeType: 'update',
                rowId: row.id,
                content: row,
                rowIndex: targetIndex,
              });
            }

            const dynamicResult = this.documentDynamicDataService.executeDynamicRowDragLogic(
              this.section.id,
              rowDragEnd,
              newRowData,
            );

            if (dynamicResult?.changes) {
              changes.push(...dynamicResult.changes);
            }

            if (dynamicResult?.result) {
              newRowData = dynamicResult.result;
            }
          }

          const afterRowDelta =
            direction === 'lowToTop'
              ? overIndex > fromIndex
                ? 0
                : overNode.data.type === 'placeholderGroup'
                ? 0
                : -1
              : overIndex > fromIndex
              ? movingNode.data.type === 'placeholder' && overNode.data.type === 'placeholderGroup'
                ? 0
                : toOffset
              : -1;

          changes.push({
            sectionId: this.section.id,
            changeType: 'move',
            rowId: movingNode.id,
            rowIndex: toIndex,
            afterRowId: newRowData[overIndex + afterRowDelta]?.id,
          });
          for (let i = 1; i < movingCount; i += 1) {
            changes.push({
              sectionId: this.section.id,
              changeType: 'move',
              rowId: newRowData[fromIndex + i].id,
              afterRowId: newRowData[fromIndex + i - 1].id,
              rowIndex: toIndex + i,
              fromRowIndex: fromIndex + i,
            });
          }

          this.moveInArray(newRowData, movingCount, fromIndex, toIndex);

          movedData = newRowData;
        } else {
          const movingNode = rowDragEnd.node;
          const { overNode } = rowDragEnd;
          if (this.notSameLevelNode(movingNode, overNode)) {
            return;
          }
          let parentCode = overNode.parent.data ? `${overNode.parent?.data[treeField]}${treeFieldSeparator}` : ''; // overNode.data.tree + '/';
          if (rowDragEnd.overIndex === 0) {
            parentCode = '';
          }

          movedData = newRowData;
          const result = generateNewPathAndMove(
            movedData,
            parentCode,
            rowDragEnd.node,
            rowDragEnd.overIndex,
            treeField,
            this.section.id,
          );
          movedData = result.moveData;
          changes.push(...result.changes);
          dynamicChanges.push(...result.dynamicChanges);
        }
      } else {
        if (rowDragEnd.overNode.group) {
          return;
        }
        const fromIndex = rows.findIndex((row) => row.id === rowDragEnd.node.id);
        const toIndex = rows.findIndex((row) => row.id === rowDragEnd.overNode.id);
        if (fromIndex === -1 || toIndex === -1) {
          return;
        }
        movedData = immutableMove(rows, fromIndex, toIndex);
        const afterRowDelta = fromIndex > toIndex ? -1 : 0;

        changes.push({
          sectionId: this.section.id,
          changeType: 'move',
          rowId: rows[fromIndex].id,
          afterRowId: rows[toIndex + afterRowDelta]?.id,
          rowIndex: toIndex,
          fromRowIndex: fromIndex,
        });
      }

      const dragRegenerateSeq = this.section?.gridMeta?.dragRegenerateSeq ?? false;
      if (dragRegenerateSeq) {
        const regenerateSeqWrapper = this.gridSectionService.generateNewSeqNos(
          movedData,
          this.fields,
          this.moduleId,
          this.section.id,
        );
        movedData = regenerateSeqWrapper.result;
        this.documentDataService.dynamicRowCellChanges$.next({ changes: regenerateSeqWrapper.changes });
        cellChanges.push(...regenerateSeqWrapper.changes);
      }

      let transformedData = movedData;
      if (this.section.gridMeta?.rowMovedTransformHooks) {
        const rowMovedWrapper = this.rowMovedHooksProcessor(
          movedData,
          rowDragEnd.node.id,
          this.section.gridMeta?.rowMovedTransformHooks,
          this.section.id,
        );
        transformedData = rowMovedWrapper.rows;
        cellChanges.push(...rowMovedWrapper.changes);
      }

      this.documentDataService.updateDataChangeByUserTree([this.section.id], true);
      this.documentDataService.directlyUpdateData({ [this.section.id]: transformedData });
      this.documentStatusService.setUserChangeValue(true);
      this.gridApi.clearFocusedCell();
      this.gridEventSideEffectService.afterRowDragEnd[this.section.id]?.(rowDragEnd);
      this.documentDataService.rowChanges$.next({ changes });
      this.documentDataService.dynamicRowCellChanges$.next({ changes: cellChanges });
      this.documentDataService.dynamicRowChanges$.next({ changes: dynamicChanges });
    });
  }

  private movingInPage(movingNode1, rowDragEnd, pageSize) {
    const parentNode = movingNode1?.parent?.parent?.parent ?? movingNode1?.parent?.parent ?? movingNode1?.parent;
    parentNode?.allLeafChildren?.sort((a, b) => a.rowIndex - b.rowIndex);
    const maxRowIndex = parentNode?.allLeafChildren
      ? Math.max(...parentNode.allLeafChildren.map((node) => node.rowIndex))
      : 0;
    const orginalAllNodes = new Array(maxRowIndex + 1).fill(undefined);
    parentNode?.allLeafChildren.forEach((node) => {
      orginalAllNodes[node.rowIndex] = node;
    });
    parentNode?.childrenAfterGroup.forEach((node) => {
      orginalAllNodes[node.rowIndex] = node;
    });
    const whichPage = Math.floor(movingNode1.rowIndex / pageSize) + 1;
    const startIndexInThePage = (whichPage - 1) * pageSize;
    const endIndexInThePage = (whichPage - 1) * pageSize + pageSize - 1;
    const rowDatasInThePage = orginalAllNodes.slice(startIndexInThePage, endIndexInThePage + 1);
    let currentHeight = 0;
    let realTargetIndex;
    for (let i = 0; i < rowDatasInThePage.length; i += 1) {
      const rowTop = currentHeight;
      currentHeight += rowDatasInThePage[i].rowHeight;
      if (rowDragEnd.y >= rowTop && rowDragEnd.y < currentHeight) {
        realTargetIndex = rowDatasInThePage[i].rowIndex;
      }
    }
    if (R.isNil(realTargetIndex)) {
      return rowDragEnd;
    }
    rowDragEnd.overIndex = realTargetIndex;
    rowDragEnd.overNode = orginalAllNodes.find((node: { rowIndex: any }) => node.rowIndex === realTargetIndex);
    return rowDragEnd;
  }

  private rowMovedHookProcessor(
    rows: any[],
    rowId: string,
    hook: FormDefine_RowMovedTransformHook,
    sectionId: string,
    changes: DocumentGridCellValueChange[],
  ): { rows: any[]; changes: DocumentGridCellValueChange[] } {
    const passConditions =
      hook.conditions?.every((condition) => {
        if (condition.type === 'everyRow') {
          return rows.every((row) => checkHookCondition(row, condition));
        }
        if (condition.type === 'currentRow') {
          const row = rows.find(({ id }) => id === rowId);
          return checkHookCondition(row, condition);
        }
        if (condition.type === 'firstRow') {
          return checkHookCondition(rows[0], condition);
        }
        return rows.some((row) => checkHookCondition(row, condition));
      }) ?? true;
    if (!passConditions) {
      return { rows, changes };
    }

    const rowIndex =
      hook.anchor === 'firstConditionRow'
        ? rows.findIndex((row) => hook.anchorConditions.every((condition) => checkHookCondition(row, condition)))
        : hook.anchor === 'firstRow'
        ? 0
        : hook.anchor === 'currentRow'
        ? rows.findIndex(({ id }) => id === rowId)
        : -1;

    const targetRow = rows[rowIndex];
    if (targetRow) {
      const newRows = hook.actions.reduce((acc, action) => {
        if (action.type === 'setValue') {
          changes.push({
            sectionId,
            rowId: targetRow.id,
            rowIndex,
            path: action.path.join('.'),
            content: action.value,
            fieldId: action.path.join('.'),
          });
          return R.assocPath([rowIndex, ...action.path], action.value)(acc);
        }
        if (action.type === 'appendValue') {
          const appendValue = { ...action.value, id: generateUUID() };
          const content = R.append(appendValue)(R.path(action.path, targetRow));

          changes.push({
            sectionId,
            rowId: targetRow.id,
            rowIndex,
            path: action.path.join('.'),
            content,
            fieldId: action.path.join('.'),
          });
          return R.assocPath([rowIndex, ...action.path], content)(acc);
        }
        return acc;
      }, rows) as any[];
      return { rows: newRows, changes };
    }
    return { rows, changes };
  }

  private rowMovedHooksProcessor(rows: any[], rowId: string, hooks: FormDefine_RowMovedTransformHook[], sectionId) {
    return hooks.reduce((acc, hook) => this.rowMovedHookProcessor(acc.rows, rowId, hook, sectionId, acc.changes), {
      rows,
      changes: [],
    });
  }

  rowDataUpdated(rowDataUpdated: RowDataUpdatedEvent) {
    this.gridEventSideEffectService.afterRowDataUpdated[this.section.id]?.(rowDataUpdated);
  }

  selectChildren(node: IRowNode, isSelected: boolean) {
    node.childrenAfterGroup?.forEach((child) => {
      if (child.isSelected() !== isSelected) {
        this.selectProgressingRowNodeSet.add(child);
        this.selectChildren(child, isSelected);
      }
    });
  }

  unSelectParent(node: IRowNode) {
    const { parent } = node;
    if (parent?.isSelected()) {
      this.selectProgressingRowNodeSet.add(parent);
      this.unSelectParent(parent);
    }
  }

  rowSelected(rowSelectedEvent: RowSelectedEvent) {
    if (this.section.gridMeta?.treeConfig?.autoCheckChildren || this.section.gridMeta?.groupConfig?.autoCheckChildren) {
      const { node } = rowSelectedEvent;
      if (this.selectProgressingRowNodeSet.has(node)) {
        this.selectProgressingRowNodeSet.delete(node);
        return;
      }
      const isSelected = node.isSelected();

      this.selectChildren(node, isSelected);
      if (!isSelected) {
        this.unSelectParent(node);
      }
      this.selectProgressingRowNodeSet.forEach((rowNode) => rowNode.setSelected(isSelected));
    }
    this.documentDynamicDataService.handleRowSelectMap[this.section.id]?.(rowSelectedEvent);
    this.refreshFocusingData$.next();
  }

  switchGridColumn(menu: MenuItem) {
    this.switchColumnGroupId$.next(menu.id);
  }

  private generateViewTreeGroupColDef(fieldDefine: FieldDefine, maxDepth: number) {
    const sectionId = isEmptyOrNil(this.section?.gridMeta?.useConfigFromSectionId)
      ? this.section.id
      : this.section.gridMeta?.useConfigFromSectionId;
    const businessSelector = this.cellMapperService.businessSelector[sectionId]?.[fieldDefine.id];
    const selectorFun = businessSelector || this.cellMapperService.getViewComponent.bind(this.cellMapperService);
    const valueGetter =
      this.cellMapperService.separateGridValueGetter[sectionId]?.[fieldDefine.id] ??
      this.valueGetterMapByType[fieldDefine.type]?.(fieldDefine) ??
      (fieldDefine.mapping ? (params: ValueGetterParams) => dotPath(fieldDefine.mapping, params.data) : null);
    const size = fieldDefine.sizeWhenEditing ?? fieldDefine.size;

    const canRenderComponent = fieldDefine.onlyRenderInEditMode
      ? () => false
      : fieldDefine.viewRenderConditions
      ? (params: CellRendererParams) => checkConditionsValidInObject(fieldDefine.viewRenderConditions, params.data)
      : fieldDefine.renderConditions
      ? (params: CellRendererParams) => checkConditionsValidInObject(fieldDefine.renderConditions, params.data)
      : () => true;

    const headerName = fieldDefine.hideLabel ? '' : fieldDefine.label;

    const width = this.documentService.getGridFieldWidth(fieldDefine.type, size);
    const minWidth = this.documentService.getGridMinWidth(headerName, fieldDefine.type);
    const maxWidth = this.documentService.getGridMaxWidth(headerName, width);

    return {
      headerClass: 'header-column',
      headerComponent: R.isNil(fieldDefine.headerComponent) ? 'HeaderCell' : fieldDefine.headerComponent,
      headerComponentParams: {
        fieldDefine,
        edit: false,
        ...this.buildFilterParams(fieldDefine),
      },
      headerName,
      colId: fieldDefine.id,
      pinned: fieldDefine.isFrozenColumn ? 'left' : null,
      width: maxDepth > 2 ? width + 10 * maxDepth : width,
      minWidth,
      maxWidth,
      suppressSizeToFit: fieldDefine.suppressSizeToFit,
      suppressMovable: fieldDefine.suppressMovable,
      suppressSpanHeaderHeight: true,
      resizable: true,
      editable: false,
      cellRendererParams: () => ({
        suppressCount: true,
        valueChange$: this.valueChange$,
        actionBus$: this.actionBus$,
        groupRowActionBus$: this.groupRowActionBus$,
        context: this.context,
        innerRendererSelector: (params: CellRendererParams) => {
          if (!canRenderComponent(params)) {
            return { component: 'emptyCellComponent' };
          }

          if (params.column.isRowGroupDisplayed(params.column.getColId())) {
            const isRootLevel = params.node.level === -1;
            if (isRootLevel) {
              params.value = 'Total';
              return { component: 'Footer', params };
            }
          }
          return selectorFun(params, fieldDefine, ComponentState.View);
        },
        sortingBus$: this.sortingBus$,
      }),
      valueGetter,
      columnGroupShow: fieldDefine.columnGroupShow,
      enablePivot: fieldDefine.enablePivot ?? this.enableGroupMap[fieldDefine.type],
      enableValue: fieldDefine.enableValue ?? this.enableValueMap[fieldDefine.type],
      enableRowGroup: fieldDefine.enableRowGroup ?? this.enableGroupMap[fieldDefine.type],
      keyCreator: (params: KeyCreatorParams) =>
        fieldDefine.format ? resolveFormat(fieldDefine.format, params.value) : params.value,
      cellClassRules: {
        pointer: () => this.documentCommentPanelService.checkShowCanComment(),
        'right-triangle-grid': (params) => this.documentCommentPanelService.checkShowCellHasComment(params),
        'comment-selected-field': (params) =>
          this.documentCommentPanelService.checkSelectedCellComment(fieldDefine, params),
        'can-comment-cell': (params) => this.documentCommentPanelService.checkCellCanComment(params),
        'state-backend-invalid': (params) => this.checkCellError(params),
      },
    } as ColDef;
  }

  private generateCustomGroupColDef(fieldDefine: FieldDefine, isEditMode: boolean) {
    const width = this.documentService.getGridFieldWidth(fieldDefine.type, fieldDefine.size);
    const minWidth = this.documentService.getGridMinWidth(fieldDefine.label, fieldDefine.type);
    const maxWidth = this.documentService.getGridMaxWidth(fieldDefine.label, width);

    const hasReadonlyLogic = this.documentFieldReadonlyService.checkFieldHasReadonlyLogic(this.section.id, fieldDefine);

    const isReadonly = hasReadonlyLogic
      ? (params: EditableCallbackParams | CellClassParams) =>
          this.documentFieldReadonlyService.getCellReadonlyLogic(params.context.sectionId, fieldDefine, params)
      : () => false;

    const field = {
      id: this.autoGroupFieldDef.id,
      label: fieldDefine?.label,
      hint: fieldDefine?.hint,
      isFrozenColumn: fieldDefine?.isFrozenColumn,
      visible: fieldDefine?.visible,
      readonly: fieldDefine?.readonly,
    } as FieldDefine;

    const fields: FieldDefine[] = [field];

    const valueGetter = (params) => (params.data ? (params.data[field.id] ? params.data[field.id] : '') : '');

    const isFieldCompare = (params: CellRendererParams | CellClassParams | CellEditorParams | EditableCallbackParams) =>
      this.isFieldCompare(fieldDefine, params);

    return {
      headerClass: 'header-column',
      headerComponent: 'HeaderCell',
      headerComponentParams: {
        fieldDefine: field,
        edit: false,
        ...this.buildFilterParams(field),
      },
      headerName: field.label,
      cellRenderer: 'agGroupCellRenderer',
      cellEditor: 'groupCol',
      showRowGroup: true,
      hide: false,
      resizable: true,
      suppressSizeToFit: fieldDefine.suppressSizeToFit,
      editable: isEditMode ? (params: EditableCallbackParams) => !isReadonly(params) : false,
      cellEditorParams: () => ({
        suppressCount: true,
        valueChange$: this.valueChange$,
        actionBus$: this.actionBus$,
        groupRowActionBus$: this.groupRowActionBus$,
        context: this.context,
        sortingBus$: this.sortingBus$,
        fields,
        groupConfig: this.section.gridMeta.groupConfig,
        moduleId: this.moduleId,
        sectionId: this.section.id,
        data$: this.data$,
        isEditMode,
        section: this.section,
      }),
      pinned: field.isFrozenColumn ? 'left' : null,
      width,
      minWidth,
      maxWidth,
      colId: field.id,
      valueGetter,
      suppressSpanHeaderHeight: true,
      cellRendererParams: () => ({
        suppressCount: true,
        valueChange$: this.valueChange$,
        actionBus$: this.actionBus$,
        groupRowActionBus$: this.groupRowActionBus$,
        context: this.context,
        innerRenderer: 'groupCol',
        sortingBus$: this.sortingBus$,
        fields,
        groupConfig: this.section.gridMeta.groupConfig,
        moduleId: this.moduleId,
        sectionId: this.section.id,
        data$: this.data$,
        isEditMode,
        section: this.section,
      }),
      valueSetter: (params: ValueSetterParams) => {
        const { data, newValue, node } = params;
        this.valueChange$.next({ value: newValue, field: fieldDefine, rowId: data.id, currentRowData: data, node });
        return true;
      },
      cellClassRules: {
        'read-only': (params) => (isFieldCompare(params) ? true : isEditMode ? isReadonly(params) : true),
        'child-row': (params) => !params.node.allChildrenCount,
      },
      enablePivot: fieldDefine.enablePivot ?? this.enableGroupMap[fieldDefine.type],
      enableValue: fieldDefine.enableValue ?? this.enableValueMap[fieldDefine.type],
      enableRowGroup: fieldDefine.enableRowGroup ?? this.enableGroupMap[fieldDefine.type],
      keyCreator: (params: KeyCreatorParams) =>
        fieldDefine.format ? resolveFormat(fieldDefine.format, params.value) : params.value,
    } as ColDef;
  }

  private generateViewColDefs(
    fields: FieldDefine[],
    switchColumnGroupId?: string,
    isExpansionGroup?: boolean,
  ): ColDef[] {
    let targetField;
    let targetIndex;
    let sectionLabel = this.section?.gridMeta?.groupConfig?.sectionLabel;
    if (sectionLabel) {
      sectionLabel = sectionLabel.slice(1, -1);
    }
    const sectionId = isEmptyOrNil(this.section?.gridMeta?.useConfigFromSectionId)
      ? this.section.id
      : this.section.gridMeta?.useConfigFromSectionId;

    const columnDefs: ColDef[] = fields.map((fieldDefine: FieldDefine, index: number) => {
      const getFieldDefine = this.cellMapperService.hasFieldDefineGetter(sectionId, fieldDefine.id)
        ? (row: any) => this.getFieldDefine(fieldDefine, row)
        : () => fieldDefine;

      const cellStyleFun = this.documentDynamicFieldService.checkCellStyleLogic(sectionId, fieldDefine.id)
        ? (params: CellClassParams) => this.documentDynamicFieldService.getCellStyle(sectionId, fieldDefine.id, params)
        : () => {};

      let hideColumn = false;
      const headerName = fieldDefine.hideLabel ? '' : fieldDefine.label;
      const width = this.documentService.getGridFieldWidth(fieldDefine.type, fieldDefine.size);
      const minWidth = this.documentService.getGridMinWidth(headerName, fieldDefine.type);
      const maxWidth = this.documentService.getGridMaxWidth(headerName, width);

      const comparator = this.documentGridFilterService.getComparator(fieldDefine);
      const colSpan = this.cellMapperService.viewColSpanRules[sectionId]?.[fieldDefine.id] ?? null;

      if (fieldDefine.type === FormFieldTypes.expansionGroup && fieldDefine.visible) {
        return {
          headerClass: 'header-column',
          headerName,
          comparator,
          colId: fieldDefine.id,
          field: fieldDefine.id,
          pinned: fieldDefine.isFrozenColumn ? 'left' : null,
          hide: !fieldDefine.visible,
          width,
          minWidth,
          maxWidth,
          resizable: true,
          suppressSizeToFit: fieldDefine.suppressSizeToFit,
          suppressMovable: fieldDefine.suppressMovable,
          suppressSpanHeaderHeight: true,
          cellClass: ['cbx-cell'],
          children: this.generateViewColDefs(fieldDefine.fields, switchColumnGroupId, true),
          openByDefault: fieldDefine.openByDefault,
          groupId: fieldDefine.id,
          enablePivot: fieldDefine.enablePivot ?? this.enableGroupMap[fieldDefine.type],
          enableValue: fieldDefine.enableValue ?? this.enableValueMap[fieldDefine.type],
          enableRowGroup: fieldDefine.enableRowGroup ?? this.enableGroupMap[fieldDefine.type],
          keyCreator: (params: KeyCreatorParams) =>
            fieldDefine.format ? resolveFormat(fieldDefine.format, params.value) : params.value,
          rowGroupIndex: fieldDefine.rowGroupIndex,
        } as ColDef;
      }

      if (fieldDefine.id === this.autoGroupFieldDef?.id) {
        targetField = fieldDefine;
        targetIndex = index;
        hideColumn = true;
      }

      const businessSelector =
        this.cellMapperService.viewBusinessSelector[sectionId]?.[fieldDefine.id] ??
        this.cellMapperService.businessSelector[sectionId]?.[fieldDefine.id];
      const selectorFun = businessSelector || this.cellMapperService.getViewComponent.bind(this.cellMapperService);

      const rowGroup = this.isRowGroup(fieldDefine) || null;

      const canRenderComponent = fieldDefine.onlyRenderInEditMode
        ? () => false
        : fieldDefine.viewRenderConditions
        ? (params: CellRendererParams) =>
            checkConditionsValidInObject(getFieldDefine(params?.data).viewRenderConditions, params.data)
        : fieldDefine.renderConditions
        ? (params: CellRendererParams) =>
            checkConditionsValidInObject(getFieldDefine(params?.data).renderConditions, params.data)
        : () => true;

      const isAgGridAutoColumn = (params) => params?.colDef?.colId === 'ag-Grid-AutoColumn';

      if (fieldDefine.type === FormFieldTypes.sparkline) {
        return {
          colId: fieldDefine.id,
          headerName,
          cellClass: ['cbx-cell'],
          width: 240,
          minWidth: 240,
          maxWidth: 400,
          pinned: fieldDefine.isFrozenColumn ? 'left' : null,
          resizable: true,
          cellRendererSelector: (params: CellRendererParams) => {
            if (!canRenderComponent(params)) {
              return { component: 'emptyCellComponent' };
            }
            if ((!params.data || params.data.type === 'Section') && fieldDefine.aggFunc && this.autoGroupFieldDef) {
              return { component: 'aggFunc' };
            }
            return selectorFun(params, fieldDefine, ComponentState.View);
          },
        };
      }

      return {
        headerClass: 'header-column',
        headerComponent: R.isNil(fieldDefine.headerComponent) ? 'HeaderCell' : fieldDefine.headerComponent,
        headerComponentParams: {
          fieldDefine,
          edit: false,
          ...this.buildFilterParams(fieldDefine),
          sortable: this.documentGridFilterService.canFilter(fieldDefine),
          useAgGridSort: true,
        },
        headerName,
        comparator,
        colId: fieldDefine.id,
        field:
          this.autoGroupFieldDef && fieldDefine.id === this.section?.gridMeta?.groupConfig?.groupField
            ? sectionLabel
            : fieldDefine.id,
        pinned: fieldDefine.isFrozenColumn ? 'left' : null,
        rowGroup,
        hide:
          this.gridSectionService.isSystemField(fieldDefine) ||
          !fieldDefine.visible ||
          (switchColumnGroupId &&
            fieldDefine.switchColumnGroupId &&
            fieldDefine.switchColumnGroupId !== switchColumnGroupId) ||
          hideColumn,
        width,
        minWidth,
        maxWidth,
        suppressSizeToFit: fieldDefine.suppressSizeToFit,
        suppressMovable: fieldDefine.suppressMovable,
        suppressSpanHeaderHeight: true,
        resizable: true,
        editable: false,
        cellRenderer: this.detailExpandBtn && fieldDefine.id === 'vpoNo' ? 'agGroupCellRenderer' : 'emptyCellComponent',
        cellRendererSelector: (params: CellRendererParams) => {
          if (
            params.colDef?.headerComponentParams?.fieldDefine?.uiId ===
            'ui.tabProductionStatus.inspectReportProductionStatusList.description'
          ) {
            if (params.node?.footer) {
              params.value = 'Total';
              return { component: FormFieldTypes.footer, params };
            }
            if (params.node?.rowPinned === 'bottom') {
              params.value = 'Total (%)';
              return { component: FormFieldTypes.footer, params };
            }
          }

          if (!canRenderComponent(params)) {
            return { component: 'emptyCellComponent' };
          }

          if (isAgGridAutoColumn(params)) {
            return { component: FormFieldTypes.text };
          }

          if ((!params.data || params.data.type === 'Section') && fieldDefine.aggFunc && this.autoGroupFieldDef) {
            return { component: 'aggFunc' };
          }

          const changedFieldDefine = getFieldDefine(params?.data);

          return selectorFun(params, changedFieldDefine, ComponentState.View);
        },
        cellRendererParams: (params: CellRendererParams) => {
          const changedFieldDefine = getFieldDefine(params?.data);

          return {
            valueChange$: this.valueChange$,
            actionBus$: this.actionBus$,
            groupRowActionBus$: this.groupRowActionBus$,
            sortingBus$: this.sortingBus$,
            field: isAgGridAutoColumn(params) ? { ...changedFieldDefine, format: null } : changedFieldDefine,
            gridSectionService: this.gridSectionService,
            section: this.section,
          };
        },
        cellClass: [
          'cbx-cell',
          this.getCellClassByFieldDefine(fieldDefine),
          ...(fieldDefine.cssClass ? [fieldDefine.cssClass] : []),
        ],
        cellClassRules: {
          ...this.cellMapperService.cellClassRules,
          'read-only': () => false,
          'row-read-only': (params) => this.documentFieldReadonlyService.getRowReadonlyLogic(sectionId, params),
          'edit-mode': () => false,
          'font-bold': (params) => params.node.footer,
          pointer: () => this.documentCommentPanelService.checkShowCanComment(),
          'right-triangle-grid': (params) => this.documentCommentPanelService.checkShowCellHasComment(params),
          'comment-selected-field': (params) =>
            this.documentCommentPanelService.checkSelectedCellComment(fieldDefine, params),
          'can-comment-cell': (params) => this.documentCommentPanelService.checkCellCanComment(params),
          'state-backend-invalid': (params) => this.checkCellError(params),
        },
        valueFormatter: (params: ValueFormatterParams) =>
          this.cellMapperService.getFormattedValue(params, getFieldDefine(params?.data), false),
        valueGetter: (params: ValueGetterParams) => {
          const changedFieldDefine = getFieldDefine(params?.data);

          return (
            this.cellMapperService.separateGridValueGetter[sectionId]?.[changedFieldDefine.id]?.(params) ??
            this.valueGetterMapByType[changedFieldDefine.type]?.(params) ??
            dotPath(changedFieldDefine?.mapping ?? changedFieldDefine.id, params.data)
          );
        },
        columnGroupShow: fieldDefine.columnGroupShow,
        aggFunc: fieldDefine.aggFunc ?? null,
        defaultAggFunc: fieldDefine.defaultAggFunc ?? null,
        enablePivot: fieldDefine.enablePivot ?? this.enableGroupMap[fieldDefine.type],
        enableValue: fieldDefine.enableValue ?? this.enableValueMap[fieldDefine.type],
        enableRowGroup: fieldDefine.enableRowGroup ?? this.enableGroupMap[fieldDefine.type],
        cellStyle: cellStyleFun,
        keyCreator: (params: KeyCreatorParams) =>
          fieldDefine.format ? resolveFormat(fieldDefine.format, params.value) : params.value,
        colSpan,
        sortable: fieldDefine.id === this.section?.gridMeta?.groupConfig?.groupSortingField,
        sort:
          fieldDefine.id === this.section?.gridMeta?.groupConfig?.groupSortingField
            ? this.section?.gridMeta?.groupConfig?.sort
            : null,
      } as ColDef;
    });

    if (this.autoGroupFieldDef && targetField) {
      columnDefs.splice(targetIndex, 0, this.generateCustomGroupColDef(targetField, false));
    }

    const newColumnDefs: ColDef[] = [...columnDefs];

    if (isExpansionGroup) {
      return newColumnDefs;
    }
    this.documentComparisonQuery
      .getAddedOrDeltedRowBySectionId$(this.section.id)
      .pipe(untilDestroyed(this))
      .subscribe((fieldCompare: { fromAddOrDeleteRow: string; isApprove: boolean }) => {
        if (fieldCompare?.fromAddOrDeleteRow && fieldCompare?.isApprove) {
          const field = {
            id: 'compareBtn',
            uiId: `${this.section.uiId}.compareBtn`,
          } as FieldDefine;

          newColumnDefs.unshift({
            headerName: '',
            field: 'compareBtn',
            cellRenderer: InlineComparisonRowCellComponent,
            resizable: true,
            pinned: 'left',
            headerComponentParams: {
              fieldDefine: field,
            },
            lockPosition: true,
          });
        }
        this.gridOptions.columnDefs = newColumnDefs;
        // this.gridApi.refreshCells();
        // this.gridOptions.api?.setColumnDefs(newColumnDefs);
        this.cdr.detectChanges();
      });

    return newColumnDefs;
  }

  private buildFilterParams(fieldDefine: FieldDefine) {
    const canFilter = this.documentGridFilterService.canFilter(fieldDefine);

    if (!canFilter) {
      return {};
    }

    const isRequirementField =
      fieldDefine.type === FormFieldTypes.radio &&
      fieldDefine.id?.startsWith('result') &&
      this.section.id.startsWith('factoryAuditRequirements');

    const filterType = this.documentGridFilterService.getFilterType(fieldDefine);
    const hasCodelist = this.documentGridFilterService.isValueFilter(fieldDefine) || isRequirementField;
    const codelistOptions$ = this.documentGridFilterService.getCurrentCodelistOptions(
      fieldDefine,
      this.data$,
      isRequirementField,
    );

    return {
      filter: true,
      filterType,
      filterConditions$: this.filterBus$.pipe(
        map(
          (
            filterBus: {
              filters: SearchFilter[];
              fieldDefine: FieldDefine;
            }[],
          ) => {
            if (isEmptyOrNil(filterBus)) {
              return null;
            }
            const value = filterBus.find(({ fieldDefine: field }) => field.id === fieldDefine.id);

            return isEmptyOrNil(value) ? null : value.filters;
          },
        ),
      ),
      applyFilter: (filters: SearchFilter[]) => {
        if (filters.length === 0) {
          this.removeFilterBus(fieldDefine);
          this.documentService.updateRecordCountBySection(this.section.id, this.gridApi.getDisplayedRowCount());
        } else {
          filters = filters.map((searchFilter) => ({
            ...searchFilter,
            condition: decodeURIComponent(searchFilter.condition),
          }));
          this.updateFilterBus({ filters, fieldDefine });
        }
      },
      codelistOptions$,
      hasCodelist,
      isRequirementField,
    };
  }

  private removeFilterBus(value: FieldDefine) {
    const index = this.filterBus.findIndex(({ fieldDefine }) => fieldDefine.id === value.id);
    if (index !== -1) {
      this.filterBus.splice(index, 1);
    }
    this.filterBus$.next(this.filterBus);
    this.gridApi.onFilterChanged();
  }

  private updateFilterBus(newFilterBus: { filters: SearchFilter[]; fieldDefine: FieldDefine }) {
    const { fieldDefine } = newFilterBus;

    const isRequirementField =
      fieldDefine.type === FormFieldTypes.radio &&
      fieldDefine.id?.startsWith('result') &&
      this.section.id.startsWith('factoryAuditRequirements');

    if (isRequirementField) {
      const newFilters = newFilterBus.filters?.map((newFilter) => {
        if (newFilter?.condition?.includes('Yes')) {
          newFilter = { ...newFilter, condition: newFilter.condition.replace('Yes', 'true') };
        }
        if (newFilter?.condition?.includes('No')) {
          newFilter = { ...newFilter, condition: newFilter.condition.replace('No', 'false') };
        }
        return newFilter;
      });

      newFilterBus = { filters: newFilters, fieldDefine };
    }

    if (isEmptyOrNil(this.filterBus)) {
      this.filterBus.push(newFilterBus);
    } else {
      const index = this.filterBus.findIndex((value) => value.fieldDefine.id === fieldDefine.id);

      if (index !== -1) {
        this.filterBus[index] = newFilterBus;
      } else {
        this.filterBus.push(newFilterBus);
      }

      if (fieldDefine?.filter?.useYesNoOptions && this.section.id.startsWith('factoryAuditRequirements')) {
        this.filterBus = this.filterBus.filter((newFilter) => {
          if (newFilter.fieldDefine?.id?.includes('result')) {
            return newFilter.fieldDefine.id === fieldDefine.id;
          }
          return true;
        });
      }
    }

    this.filterBus$.next(this.filterBus);
    this.gridApi.onFilterChanged();

    const updateFilterBus = this.filterBus.map((filterBus) => ({
      ...filterBus,
      filters: filterBus.filters.map((searchFilter) => ({
        ...searchFilter,
        condition: searchFilter.condition.includes('%')
          ? encodeURIComponent(searchFilter.condition)
          : searchFilter.condition,
      })),
    }));
    this.filterBus$.next(updateFilterBus);

    this.documentService.updateRecordCountBySection(this.section.id, this.gridApi.getDisplayedRowCount());
  }

  doesExternalFilterPass(node: RowNode): boolean {
    const { data: rowData } = node;

    if (isEmptyOrNil(rowData)) {
      return false;
    }

    return (
      this.documentStatusService.excelExporting ||
      this.documentGridFilterService.doesFilterPass(rowData, this.filterBus, this.data, this.section.id)
    );
  }

  private generateEditTreeGroupColDef(fieldDefine: FieldDefine, maxDepth: number) {
    const sectionId = isEmptyOrNil(this.section?.gridMeta?.useConfigFromSectionId)
      ? this.section.id
      : this.section.gridMeta?.useConfigFromSectionId;
    const cellStyleFun = this.documentDynamicFieldService.checkCellStyleLogic(sectionId, fieldDefine.id)
      ? (params: CellClassParams) => this.documentDynamicFieldService.getCellStyle(sectionId, fieldDefine.id, params)
      : () => {};
    const businessSelector = this.cellMapperService.businessSelector[sectionId]?.[fieldDefine.id];
    const editSelectorFun = businessSelector || this.cellMapperService.getEditComponent.bind(this.cellMapperService);

    const valueGetter =
      this.cellMapperService.separateGridValueGetter[sectionId]?.[fieldDefine.id] ??
      this.valueGetterMapByType[fieldDefine.type]?.(fieldDefine) ??
      (fieldDefine.mapping ? (params: ValueGetterParams) => dotPath(fieldDefine.mapping, params.data) : null);

    const getFieldDefine = this.cellMapperService.hasFieldDefineGetter(sectionId, fieldDefine.id)
      ? (row: any) => this.getFieldDefine(fieldDefine, row)
      : () => fieldDefine;

    const editorSelectorFun = this.cellMapperService.getEditorComponent.bind(this.cellMapperService);

    const canRenderComponent = fieldDefine.onlyRenderInEditMode
      ? () => false
      : fieldDefine.editRenderConditions
      ? (params: CellRendererParams) => checkConditionsValidInObject(fieldDefine.editRenderConditions, params.data)
      : fieldDefine.renderConditions
      ? (params: CellRendererParams) => checkConditionsValidInObject(fieldDefine.renderConditions, params.data)
      : () => true;

    const hasReadonlyLogic =
      this.documentFieldReadonlyService.checkCellHasReadonlyLogic(sectionId, fieldDefine) ||
      this.documentFieldReadonlyService.checkSectionHasRowReadonlyLogic(sectionId);

    const isCheckbox = (params: CellClassParams | CellRendererParams) =>
      isNotEmptyOrNil(fieldDefine.checkboxConditions)
        ? checkConditionsValidInObject(fieldDefine.checkboxConditions, params.data)
        : true;

    const isReadonly = (params: CellRendererParams | CellClassParams | CellEditorParams | EditableCallbackParams) => {
      if (hasReadonlyLogic) {
        return this.documentFieldReadonlyService.getCellReadonlyLogic(sectionId, getFieldDefine(params?.data), params);
      }
      return false;
    };

    const isFieldCompare = (params: CellRendererParams | CellClassParams) => this.isFieldCompare(fieldDefine, params);

    const getComponentState = (params: CellRendererParams) =>
      isFieldCompare(params)
        ? ComponentState.View
        : hasReadonlyLogic
        ? isReadonly(params)
          ? ComponentState.View
          : ComponentState.Edit
        : ComponentState.Edit;

    const valueChange$ = this.cellMapperService.separateGridValueSetter[sectionId]?.[fieldDefine.id]
      ? this.customValueChange$
      : fieldDefine.id === this.treeField
      ? this.treeValueChange$
      : this.valueChange$;

    const headerName = fieldDefine.hideLabel ? '' : fieldDefine.label;
    const width = this.documentService.getGridFieldWidth(fieldDefine.type, fieldDefine.size);
    const minWidth = this.documentService.getGridMinWidth(headerName, fieldDefine.type);
    const maxWidth = this.documentService.getGridMaxWidth(headerName, width);

    return {
      rowDrag: fieldDefine.draggableConditions
        ? (params) => checkConditionsValidInObject(fieldDefine.draggableConditions, params.data)
        : fieldDefine.draggable ?? false,
      headerClass: 'header-column',
      headerComponent: fieldDefine?.headerCheckboxSelection
        ? 'commonCheckboxHeader'
        : R.isNil(fieldDefine.headerComponent)
        ? 'HeaderCell'
        : fieldDefine.headerComponent,
      headerComponentParams: {
        fieldDefine,
        edit: true,
        ...this.buildFilterParams(fieldDefine),
        justifyContent: fieldDefine?.headerCheckboxSelection ? 'start' : undefined,
      },
      headerName,
      colId: fieldDefine.id,
      pinned: fieldDefine.isFrozenColumn ? 'left' : null,
      width: maxDepth > 2 ? width + 10 * maxDepth : width,
      minWidth,
      maxWidth,
      suppressSizeToFit: fieldDefine.suppressSizeToFit,
      suppressMovable: fieldDefine.suppressMovable,
      resizable: true,
      editable: false,
      cellRendererParams: {
        checkbox: (params: CellRendererParams) => {
          if (fieldDefine.disableCheckbox) {
            return false;
          }
          if (isNotEmptyOrNil(fieldDefine.checkboxConditions)) {
            return checkConditionsValidInObject(fieldDefine.checkboxConditions, params.data);
          }
          if (params?.data?.treePath?.includes('::')) {
            return false;
          }
          return true;
        },
        suppressCount: true,
        suppressSpanHeaderHeight: true,
        valueChange$,
        actionBus$: this.actionBus$,
        groupRowActionBus$: this.groupRowActionBus$,
        sortingBus$: this.sortingBus$,
        context: this.context,
        innerRendererSelector: (params: CellRendererParams) => {
          if (!canRenderComponent(params)) {
            return { component: 'emptyCellComponent' };
          }

          if (params.column.isRowGroupDisplayed(params.column.getColId())) {
            const isRootLevel = params.node.level === -1;
            if (isRootLevel) {
              params.value = 'Total';
              return { component: 'Footer', params };
            }
          }

          return editSelectorFun(params, fieldDefine, getComponentState(params), isReadonly(params));
        },
      },
      valueGetter,
      valueSetter: (params: ValueSetterParams) => {
        const { newValue, data, node } = params;
        valueChange$.next({ value: newValue, field: fieldDefine, rowId: data.id, currentRowData: data, node });
        return true;
      },
      cellEditorSelector: (params: CellEditorParams) => {
        const changedFieldDefine = getFieldDefine(params?.data);

        return {
          component: editorSelectorFun(params, changedFieldDefine),
          params: { ...params, field: changedFieldDefine },
        };
      },
      cellStyle: cellStyleFun,
      cellClass: ['cbx-cell', 'edit-mode'],
      cellClassRules: {
        'read-only': (params: CellClassParams) =>
          isFieldCompare(params) ? true : hasReadonlyLogic ? isReadonly(params) : !isCheckbox(params),
        'state-backend-invalid': (params) => this.checkCellError(params),
      },
      enablePivot: fieldDefine.enablePivot ?? this.enableGroupMap[fieldDefine.type],
      enableValue: fieldDefine.enableValue ?? this.enableValueMap[fieldDefine.type],
      enableRowGroup: fieldDefine.enableRowGroup ?? this.enableGroupMap[fieldDefine.type],
      keyCreator: (params: KeyCreatorParams) =>
        fieldDefine.format ? resolveFormat(fieldDefine.format, params.value) : params.value,
    } as ColDef;
  }

  private generateEditColDefs(
    fields: FieldDefine[],
    switchColumnGroupId?: string,
    isExpansionGroup?: boolean,
  ): ColDef[] {
    const sectionId = isEmptyOrNil(this.section?.gridMeta?.useConfigFromSectionId)
      ? this.section.id
      : this.section.gridMeta?.useConfigFromSectionId;
    const rowReadonlyCellClassRule = this.documentFieldReadonlyService.checkSectionHasRowReadonlyLogic(sectionId)
      ? (params: CellClassParams) => this.documentFieldReadonlyService.getRowReadonlyLogic(sectionId, params)
      : () => false;

    let targetField;
    let targetIndex;
    let sectionLabel = this.section?.gridMeta?.groupConfig?.sectionLabel;
    if (sectionLabel) {
      sectionLabel = sectionLabel.slice(1, -1);
    }

    const canPasteFieldTypes = {
      [FormFieldTypes.text]: true,
      [FormFieldTypes.textarea]: true,
      [FormFieldTypes.number]: true,
      [FormFieldTypes.breakdownYear]: true,
      [FormFieldTypes.decimal]: true,
      [FormFieldTypes.hyperlinkText]: true,
    };

    const columnDefs: ColDef[] = fields.map((fieldDefine: FieldDefine, index: number) => {
      const cellStyleFun = this.documentDynamicFieldService.checkCellStyleLogic(sectionId, fieldDefine.id)
        ? (params: CellClassParams) => this.documentDynamicFieldService.getCellStyle(sectionId, fieldDefine.id, params)
        : () => {};
      const getFieldDefine = this.cellMapperService.hasFieldDefineGetter(sectionId, fieldDefine.id)
        ? (row: any) => this.getFieldDefine(fieldDefine, row)
        : () => fieldDefine;

      let hideColumn = false;
      const size = fieldDefine.sizeWhenEditing ?? fieldDefine.size;
      const canRenderComponent = fieldDefine.onlyRenderInEditMode
        ? () => false
        : fieldDefine.editRenderConditions
        ? (params: CellRendererParams | CellEditorParams | EditableCallbackParams) =>
            checkConditionsValidInObject(getFieldDefine(params?.data).editRenderConditions, params.data)
        : fieldDefine.renderConditions
        ? (params: CellRendererParams | CellEditorParams) =>
            checkConditionsValidInObject(getFieldDefine(params?.data).renderConditions, params.data)
        : () => true;

      const headerName = fieldDefine.hideLabel ? '' : fieldDefine.label;
      const width = this.documentService.getGridFieldWidth(fieldDefine.type, size);
      const minWidth = this.documentService.getGridMinWidth(headerName, fieldDefine.type);
      const maxWidth = this.documentService.getGridMaxWidth(headerName, width);

      if (fieldDefine.type === FormFieldTypes.expansionGroup && fieldDefine.visible) {
        return {
          headerClass: 'header-column',
          headerName,
          colId: fieldDefine.id,
          field: fieldDefine.id,
          pinned: fieldDefine.isFrozenColumn ? 'left' : null,
          hide: !fieldDefine.visible,
          width,
          minWidth,
          maxWidth,
          resizable: true,
          cellClass: ['cbx-cell', 'edit-mode'],
          children: this.generateEditColDefs(fieldDefine.fields, switchColumnGroupId, true),
          openByDefault: fieldDefine.openByDefault,
          groupId: fieldDefine.id,
        } as ColDef;
      }

      if (fieldDefine.id === this.autoGroupFieldDef?.id) {
        targetField = fieldDefine;
        targetIndex = index;
        hideColumn = true;
      }

      const businessSelector =
        this.cellMapperService.editBusinessSelector[sectionId]?.[fieldDefine.id] ??
        this.cellMapperService.businessSelector[sectionId]?.[fieldDefine.id];
      const editSelectorFun = businessSelector || this.cellMapperService.getEditComponent.bind(this.cellMapperService);
      const editorSelectorFun = this.cellMapperService.getEditorComponent.bind(this.cellMapperService);

      const rowGroup = this.isRowGroup(fieldDefine) || null;

      const hasReadonlyLogic =
        this.documentFieldReadonlyService.checkCellHasReadonlyLogic(sectionId, fieldDefine) ||
        this.documentFieldReadonlyService.checkSectionHasRowReadonlyLogic(sectionId);

      const isFieldCompare = (
        params: CellRendererParams | CellClassParams | CellEditorParams | EditableCallbackParams,
      ) => this.isFieldCompare(fieldDefine, params);

      const isReadonly = (params: CellRendererParams | CellClassParams | CellEditorParams | EditableCallbackParams) => {
        if (hasReadonlyLogic) {
          return this.documentFieldReadonlyService.getCellReadonlyLogic(
            sectionId,
            getFieldDefine(params?.data),
            params,
          );
        }
        return false;
      };

      const getComponentState = (params: CellRendererParams) =>
        isFieldCompare(params)
          ? ComponentState.View
          : hasReadonlyLogic
          ? isReadonly(params)
            ? ComponentState.View
            : ComponentState.Edit
          : ComponentState.Edit;

      const colSpan = this.cellMapperService.editColSpanRules[sectionId]?.[fieldDefine.id] ?? null;

      const valueChange$ = this.cellMapperService.separateGridValueSetter[sectionId]?.[fieldDefine.id]
        ? this.customValueChange$
        : fieldDefine.id === this.treeField
        ? this.treeValueChange$
        : this.valueChange$;

      const comparator = this.documentGridFilterService.getComparator(fieldDefine);

      if (fieldDefine.type === FormFieldTypes.sparkline) {
        return {
          colId: fieldDefine.id,
          headerName,
          cellClass: ['cbx-cell', 'edit-mode', 'read-only'],
          width: 240,
          minWidth: 240,
          maxWidth: 400,
          pinned: fieldDefine.isFrozenColumn ? 'left' : null,
          resizable: true,
          cellRendererSelector: (params: CellRendererParams) => {
            if (!canRenderComponent(params)) {
              return { component: 'emptyCellComponent' };
            }
            if ((!params.data || params.data.type === 'Section') && fieldDefine.aggFunc && this.autoGroupFieldDef) {
              return { component: 'aggFunc' };
            }
            return editSelectorFun(params, getFieldDefine(params?.data), getComponentState(params), true);
          },
        };
      }

      return {
        headerClass: 'header-column',
        headerComponent: R.isNil(fieldDefine.headerComponent) ? 'HeaderCell' : fieldDefine.headerComponent,
        headerComponentParams: {
          fieldDefine,
          edit: true,
          ...this.buildFilterParams(fieldDefine),
          sortable: false,
          useAgGridSort: true,
        },
        headerName,
        comparator,
        colId: fieldDefine.id,
        field:
          this.autoGroupFieldDef && fieldDefine.id === this.section?.gridMeta?.groupConfig?.groupField
            ? sectionLabel
            : fieldDefine.id,
        pinned: fieldDefine.isFrozenColumn ? 'left' : null,
        rowGroup,
        hide:
          this.gridSectionService.isSystemField(fieldDefine) ||
          !fieldDefine.visible ||
          (switchColumnGroupId &&
            fieldDefine.switchColumnGroupId &&
            fieldDefine.switchColumnGroupId !== switchColumnGroupId) ||
          hideColumn,
        width,
        minWidth,
        maxWidth,
        suppressSizeToFit: fieldDefine.suppressSizeToFit,
        suppressMovable: fieldDefine.suppressMovable,
        suppressSpanHeaderHeight: true,
        resizable: true,
        editable: (params: EditableCallbackParams) =>
          !isFieldCompare(params) &&
          !fieldDefine.readonly &&
          !isReadonly(params) &&
          canRenderComponent(params) &&
          !!editorSelectorFun(params, getFieldDefine(params?.data)),
        suppressPaste: (params: EditableCallbackParams) => {
          const changedFieldDefineType = getFieldDefine(params?.data).type;

          return !canPasteFieldTypes[changedFieldDefineType];
        },
        cellEditorSelector: (params: CellEditorParams) => {
          const changedFieldDefine = getFieldDefine(params?.data);

          return {
            component: editorSelectorFun(params, changedFieldDefine),
            params: { ...params, field: changedFieldDefine },
          };
        },
        cellRendererSelector: (params: CellRendererParams) => {
          if (this.detailExpandBtn && params?.colDef?.colId === 'vpoNo') {
            return { component: 'agGroupCellRenderer' };
          }
          if (
            params.colDef?.headerComponentParams?.fieldDefine?.uiId ===
            'ui.tabProductionStatus.inspectReportProductionStatusList.description'
          ) {
            if (params.node?.footer) {
              params.value = 'Total';
              return { component: FormFieldTypes.footer, params };
            }
            if (params.node?.rowPinned === 'bottom') {
              params.value = 'Total (%)';
              return { component: FormFieldTypes.footer, params };
            }
          }
          if (!canRenderComponent(params)) {
            return { component: 'emptyCellComponent' };
          }
          // TODO: should remove
          if (fieldDefine.aggFunc === 'moreAction') {
            return { component: 'moreAction' };
          }
          if ((!params.data || params.data.type === 'Section') && fieldDefine.aggFunc && this.autoGroupFieldDef) {
            return { component: 'aggFunc' };
          }
          return editSelectorFun(params, getFieldDefine(params?.data), getComponentState(params), true);
        },
        colSpan,
        cellRendererParams: (params: CellRendererParams) => ({
          valueChange$,
          actionBus$: this.actionBus$,
          groupRowActionBus$: this.groupRowActionBus$,
          sortingBus$: this.sortingBus$,
          autoFillBus$: this.autoFillBus$,
          field: getFieldDefine(params?.data),
          gridSectionService: this.gridSectionService,
          section: this.section,
        }),
        cellEditorParams: (params: CellEditorParams) => ({
          valueChange$,
          actionBus$: this.actionBus$,
          groupRowActionBus$: this.groupRowActionBus$,
          sortingBus$: this.sortingBus$,
          autoFillBus$: this.autoFillBus$,
          field: getFieldDefine(params?.data),
        }),
        cellClass: [
          'cbx-cell',
          'edit-mode',
          this.getCellClassByFieldDefine(fieldDefine),
          ...(fieldDefine.cssClass ? [fieldDefine.cssClass] : []),
        ],
        cellClassRules: {
          'read-only': (params) => (isFieldCompare(params) ? true : hasReadonlyLogic ? isReadonly(params) : false),
          'row-read-only': rowReadonlyCellClassRule,
          'section-row': (params) =>
            (!!params.node.allChildrenCount && !this.isTreeData) || (!canRenderComponent(params) && this.isTreeData),
          pointer: () => this.documentCommentPanelService.checkShowCanComment(),
          'right-triangle-grid': (params) => this.documentCommentPanelService.checkShowCellHasComment(params),
          'comment-selected-field': (params) =>
            this.documentCommentPanelService.checkSelectedCellComment(fieldDefine, params),
          'can-comment-cell': (params) => this.documentCommentPanelService.checkCellCanComment(params),
          'state-backend-invalid': (params) => this.checkCellError(params),
        },
        valueFormatter: (params: ValueFormatterParams) =>
          this.cellMapperService.getFormattedValue(params, getFieldDefine(params?.data), true),
        valueGetter: (params: ValueGetterParams) => {
          const changedFieldDefine = getFieldDefine(params?.data);

          return (
            this.cellMapperService.separateGridValueGetter[sectionId]?.[changedFieldDefine.id]?.(params) ??
            this.valueGetterMapByType[changedFieldDefine.type]?.(params) ??
            dotPath(changedFieldDefine.mapping ?? changedFieldDefine.id, params.data)
          );
        },
        valueSetter: (params: ValueSetterParams) => {
          const { newValue, data, node } = params;
          const changedFieldDefine = getFieldDefine(data);
          const value = this.coerceNumberValue(changedFieldDefine, newValue);
          const rowId = data.newRowId || data.id;

          valueChange$.next({ value, field: changedFieldDefine, rowId, currentRowData: data, node });
          return true;
        },
        columnGroupShow: fieldDefine.columnGroupShow,
        aggFunc: fieldDefine.aggFunc ?? null,
        rowDrag: (params) => {
          const changedFieldDefine = getFieldDefine(params?.data);

          if (changedFieldDefine.draggableConditions) {
            return checkConditionsValidInObject(getFieldDefine(params?.data).draggableConditions, params.data);
          }

          return changedFieldDefine.draggable;
        },
        rowGroupIndex: fieldDefine.rowGroupIndex,
        cellStyle: cellStyleFun,
        enablePivot: fieldDefine.enablePivot ?? this.enableGroupMap[fieldDefine.type],
        enableValue: fieldDefine.enableValue ?? this.enableValueMap[fieldDefine.type],
        enableRowGroup: fieldDefine.enableRowGroup ?? this.enableGroupMap[fieldDefine.type],
        sortable: fieldDefine.id === this.section?.gridMeta?.groupConfig?.groupSortingField,
        sort:
          fieldDefine.id === this.section?.gridMeta?.groupConfig?.groupSortingField
            ? this.section?.gridMeta?.groupConfig?.sort
            : null,
        keyCreator: (params: KeyCreatorParams) =>
          fieldDefine.format ? resolveFormat(fieldDefine.format, params.value) : params.value,
      } as ColDef;
    });

    if (this.autoGroupFieldDef && targetField) {
      columnDefs.splice(targetIndex, 0, this.generateCustomGroupColDef(targetField, !targetField.readonly));
    }
    const showCheckboxCell =
      this.hiddenButtonByInspectReportItemsList(sectionId) &&
      !this.section.gridMeta?.disableCheckbox &&
      (this.section.gridMeta?.buttonBar?.rowActionButtons?.length ||
        this.section.gridMeta?.buttonBar?.gridActionButtons?.length ||
        this.section.gridMeta?.buttonBar?.treeActionButtons?.length);

    const showDeleteRowCol = this.section.gridMeta?.showDeleteRowCol;
    const showDragCell = !this.section.gridMeta?.disableRowDrag;

    const { draggableConditions } = this.section.gridMeta ?? {};

    const dragColWrapper: ColDef[] =
      this.hiddenButtonByInspectReportItemsList(sectionId) && showDragCell
        ? [
            {
              cellRenderer: 'emptyCellComponent',
              colId: 'drag',
              rowDrag: draggableConditions
                ? (params: RowDragCallbackParams) => checkConditionsValidInObject(draggableConditions, params.data)
                : (params) => !params?.data?.treePath?.includes('::'),
              rowDragText: (params) => `No.${params.rowNode.rowIndex + 1}`,
              width: 27,
              minWidth: 27,
              pinned: 'left',
              resizable: false,
              lockPosition: true,
              cellClass: ['cbx-cell', 'grid-action-cell', 'edit-mode', 'built-in-cell'],
              menuTabs: [],
            },
          ]
        : [];

    return isExpansionGroup
      ? [...columnDefs]
      : [
          ...dragColWrapper,
          ...(showDeleteRowCol ? [this.deleteRowCol] : []),
          ...(showCheckboxCell && !showDeleteRowCol ? [this.checkboxCol] : []),
          ...columnDefs,
        ];
  }

  coerceNumberValue(field: FieldDefine, value: any) {
    if (field?.type === FormFieldTypes.decimal || field?.type === FormFieldTypes.number) {
      return getNumberFromString(value);
    }

    return value;
  }

  hiddenButtonByInspectReportItemsList(sectionId: string): boolean {
    //  Enable concurrent edit mode for the "Inspection Report" module.
    //  "Items" table-- > "Drag to update sorting" and "Checkbox" should hidden
    const enabledConcurrent = this.domainAttributeService.enabledConcurrentModules.includes(this.moduleId);
    return !(enabledConcurrent && this.moduleId === 'inspectReport' && sectionId === 'inspectReportItemsList');
  }

  private isRenderComponent(onlyRenderInAnotherMode: boolean, fieldDefine: FieldDefine, params: CellRendererParams) {
    return onlyRenderInAnotherMode
      ? false
      : fieldDefine.renderConditions
      ? checkConditionsValidInObject(fieldDefine.renderConditions, params.data)
      : true;
  }

  private getFieldDefine(fieldDefine: FieldDefine, row: any): FieldDefine {
    return this.cellMapperService.getFieldDefine(this.section.id, fieldDefine, row);
  }

  private handleFooter() {
    if (!this.section.gridMeta.footer) {
      return;
    }

    this.footerStaticText$ = of(this.section.gridMeta.footer.staticText).pipe(
      filter(isNotEmptyOrNil),
      untilDestroyed(this),
    );

    const footerParamList$ = this.section.gridMeta.footer.referenceDataList.map(
      (referenceData: FormDefine_ReferenceData) =>
        this.documentDataQuery.select(referenceData.fieldId).pipe(
          map((value) => dotPath(referenceData.mapping, value) || value),
          map((value) => this.numberFormatService.transform(value, referenceData.decimalPlaces)),
          distinctUntilChanged(),
          untilDestroyed(this),
        ),
    );
    this.footerReferenceText$ = combineLatest(footerParamList$).pipe(map((strList) => strList.join(' ')));
  }

  private handleModuleAction(moduleAction: Observable<any>, field: FieldDefine) {
    this.documentDefineStore.setLoading(true);

    moduleAction
      .pipe(
        finalize(() => {
          this.documentDefineStore.setLoading(false);
        }),
        untilDestroyed(this),
      )
      .subscribe({
        next: (data) => this.documentDataService.initStoreData(data),
        error: (error: any) => {
          const errorMsg = error?.error?.message;
          this.notificationService.open({
            message: field.actionParams?.failedMessage ? field.actionParams.failedMessage : errorMsg,
            type: 'warn',
            duration: 6000,
          });
          console.error(error);
        },
      });
  }

  private handleGridAction(actionName: any, additionalParams?: any) {
    const action = actionName?.id ? actionName : this.section.gridMeta.actionMap?.[actionName];
    if (!action) {
      return;
    }
    const params1 = additionalParams?.cellRendererParams;
    params1?.node?.setSelected(true);

    this.actionExecuting$.next(true);

    this.data$
      .pipe(
        take(1),
        switchMap((data) => {
          const params = {
            gridSectionService: this.gridSectionService,
            moduleId: this.moduleId,
            fields: this.fields,
            sectionId: this.section.id,
            rowData: data,
            selectedRows: this.selectedRows || [],
            additionalParams,
            node: params1?.node?.parent,
            groupRowData: params1?.node?.parent?.allLeafChildren?.map((node) => node.data),
            originParams: additionalParams?.cellRendererParams,
            vcr: this.vcr,
          };
          return this.actionDispatcherService.execute(action, params);
        }),
        finalize(() => {
          params1?.node?.setSelected(false);
          this.actionExecuting$.next(false);
        }),
        untilDestroyed(this),
      )
      .subscribe({
        error: (error: string | Error) => {
          console.error(error);
        },
      });
  }

  private handleCompare() {
    this.documentComparisonQuery
      .isGridChange$(this.section.uiId)
      .pipe(distinctUntilChanged(), untilDestroyed(this))
      .subscribe((isGridChange) => {
        this.isGridChange = isGridChange;
        this.cdr.detectChanges();
      });
  }

  onDragStopped(columnDefsList: ColumnDefs[]): void {
    if (this.gridApi.isPivotMode()) {
      return;
    }

    const entityName = this.section.gridMeta?.entityName;
    const gridCacheExtraDocKey = this.section.gridMeta?.gridCacheExtraDocKey;
    const { uiId } = this.section;
    const { formId } = this;
    this.isAddRemoveColumn = false;
    let frozenIndex = 0;
    columnDefsList.forEach((columnDef) => {
      if (columnDef.isFrozenColumn) {
        frozenIndex += 1;
      }
    });

    // convert columnDefsList to newSortColumnDefsList before call api
    const subSectionId = this.section.subSections[0];
    this.subSectionsQuery
      .getSubSection$(subSectionId)
      .pipe(
        map((subSection) => subSection?.fields),
        take(1),
        switchMap((fields: FieldDefine[]) => {
          const sortColumnDefsList: FieldDefine[] = [];
          const groupFieldMap = new Map();
          const unselected = [];
          fields.forEach((field) => {
            if (field.fields) {
              field.fields.forEach((subField) => {
                groupFieldMap.set(subField.id, { ...field });
              });
            }
            if (columnDefsList.findIndex((column) => column.id === field.id) < 0) {
              unselected.push(field);
            }
          });

          const groupSubFieldMap = new Map();
          columnDefsList.forEach((columnDef, index) => {
            const { isFrozenColumn, size, visible, children } = columnDef;
            const group = groupFieldMap.get(columnDef.id);
            if (columnDef.id === this.showField) {
              return;
            }
            if (isEmptyOrNil(group)) {
              let field = fields.find((gridColumnDef) => gridColumnDef.id === columnDef.id);
              if (isNotEmptyOrNil(field)) {
                if (children && field?.fields) {
                  const subFields = [];
                  const childFieldsDefines = field?.fields;
                  childFieldsDefines.forEach((subCol) => {
                    const targetSubField = children.find((child) => child?.id === subCol?.id);
                    if (targetSubField) {
                      subFields.push({
                        ...subCol,
                        isFrozenColumn: targetSubField?.isFrozenColumn,
                        size: targetSubField?.size,
                        visible: targetSubField?.visible,
                      });
                    } else {
                      subFields.push({
                        ...subCol,
                      });
                    }
                  });

                  field = { ...field, fields: subFields };
                }

                sortColumnDefsList.push({ ...field, isFrozenColumn, size: size as ColumnSize, visible });
              }
            } else if (sortColumnDefsList.findIndex((column) => column.id === group.id) < 0) {
              sortColumnDefsList.push(group);
            }
            // handle fields in group
            if (!isEmptyOrNil(group) && sortColumnDefsList.findIndex((column) => column.id === group.id) >= 0) {
              const groupColumn = fields.find((column) => column.id === group.id);
              if (isNotEmptyOrNil(groupColumn.fields)) {
                const field = groupColumn.fields.find((gridColumnDef) => gridColumnDef.id === columnDef.id);
                sortColumnDefsList.forEach((column) => {
                  if (column.id === group.id && isNotEmptyOrNil(field)) {
                    column.fields = column.fields.map((subField) =>
                      subField.id === field.id
                        ? { ...field, isFrozenColumn, size: size as ColumnSize, visible }
                        : subField,
                    );
                  }
                });
              }
            }

            // handle dynamic fields
            if (entityName === 'FactoryAuditTemplateRequirement') {
              const existingDynamicIndex = sortColumnDefsList.findIndex(
                (item) => item.id === columnDef.id && this.fields?.find((f) => f.id === item.id && f.isDynamic),
              );

              if (existingDynamicIndex > -1) {
                sortColumnDefsList.splice(existingDynamicIndex, 1, { ...columnDef, isDynamic: true } as FieldDefine);
              } else if (this.fields?.find((field) => field.id === columnDef.id && field.isDynamic)) {
                sortColumnDefsList.push({ ...columnDef, isDynamic: true } as FieldDefine);
              }
            }

            if (columnDef.id === 'ag-Grid-AutoColumn') {
              const field = fields.find(
                (gridColumnDef) => gridColumnDef.isTreeField || gridColumnDef.id === this.showField,
              );
              sortColumnDefsList.push({ ...field, isFrozenColumn, size: size as ColumnSize, visible });
            }

            groupSubFieldMap.set(columnDef.id, index);
          });

          const newSortColumnDefsList: FieldDefine[] = sortColumnDefsList.map((columnDef: FieldDefine) => {
            if (columnDef?.fields) {
              const diff = (a: any, b: any) => groupSubFieldMap.get(a.id) - groupSubFieldMap.get(b.id);
              let newFields = R.sort(diff, [...columnDef.fields]);

              newFields = newFields.map((field) => ({
                ...field,
                suppressSizeToFit: columnDef?.suppressSizeToFit || this.sectionDisableAutoSize,
              }));

              return {
                ...columnDef,
                fields: newFields,
                children: newFields,
                suppressSizeToFit: columnDef?.suppressSizeToFit || this.sectionDisableAutoSize,
              };
            }
            return { ...columnDef, suppressSizeToFit: columnDef?.suppressSizeToFit || this.sectionDisableAutoSize };
          });

          this.handleFATemplMoreAction(fields, unselected, newSortColumnDefsList);

          newSortColumnDefsList.push(
            ...unselected.filter(
              (field) => newSortColumnDefsList.findIndex((sortColumn) => sortColumn.id === field.id) < 0,
            ),
          );

          let gridCacheExtraParam;
          if (isNotEmptyOrNil(gridCacheExtraDocKey)) {
            gridCacheExtraParam = this.gridSectionService.getNestedValue(
              this.documentDataQuery.getValue(),
              gridCacheExtraDocKey,
            );
          }

          return this.gridSectionService
            .updateGridColumnDefs(
              entityName,
              uiId,
              formId,
              frozenIndex,
              false,
              newSortColumnDefsList,
              gridCacheExtraParam,
            )
            .pipe(switchMap(() => of(newSortColumnDefsList)));
        }),
      )
      .subscribe({
        next: (newSortColumnDefsList) => {
          this.documentDefineService.updateGridSubSection(subSectionId, newSortColumnDefsList);
        },
        error: (error) => {
          console.error(error);
        },
      });
  }

  private isRowGroup(field: FieldDefine) {
    if (!this.section?.gridMeta?.groupConfig) {
      return false;
    }

    const rowGroupField = this.section.gridMeta.groupConfig.groupField;
    return rowGroupField === field.id;
  }

  private prepareRowRendererParams() {
    this.groupRowRendererParams = {
      minWidth: 200,
      suppressDoubleClickExpand: true,
      innerRenderer: 'groupRow',
      suppressCount: true,
      pinned: true,
      fullWidth: true,
      treeConfig: this.section.gridMeta.treeConfig,
      groupConfig: this.section.gridMeta.groupConfig,
      treeActionButtons: this.section.gridMeta.buttonBar?.treeActionButtons,
      moduleId: this.moduleId,
      sectionId: this.section.id,
      fields: this.fields,
      groupRowActionBus$: this.groupRowActionBus$,
      data$: this.data$,
    };
  }

  private prependMultipleGroupCols(cols: ColDef[], isReadonly?: boolean) {
    if (!this.section?.gridMeta?.groupConfig?.multipleGroupFields) {
      return cols;
    }

    if (this.section.gridMeta.groupConfig.displayType === 'groupRows') {
      return this.handleGroupRowsColumnDefs(cols, isReadonly);
    }

    return [
      ...this.section.gridMeta.groupConfig.multipleGroupFields.map((g) => ({
        field: g,
        rowGroup: true,
        hide: true,
      })),
      ...cols,
    ];
  }

  sortChanged({ fieldId, sort }: CellSortingEvent) {
    // const autoSuffix = /_\d?$/;
    // const sortings = multiSort
    //   ? this.columnApi
    //       .getColumnState()
    //       .filter(({ sort }) => sort)
    //       .map(({ colId, sort, sortIndex }) => ({ fieldId: colId.replace(autoSuffix, ''), type: sort, sortIndex }))
    //       .filter(({ fieldId: sortId }) => sortId !== fieldId)
    //   : [];
    // console.log(fieldId);
    // console.log(sort);
    // console.log(multiSort);
    // console.log(sortings);
    // const columnStates = sortings.map(
    //   (sort, i) => ({ colId: sort.fieldId, sort: sort.type, sortIndex: i } as ColumnState),
    // );
    const columnStates = [{ colId: fieldId, sort } as ColumnState];
    this.sortingChanged$.next(true);

    this.gridApi.applyColumnState({ state: columnStates, defaultState: { sort: null } });
  }

  private deepAppendBackgroundColorMap(fields: FieldDefine[], sectionBackgroundColorMap: any): FieldDefine[] {
    if (!fields) {
      return fields;
    }
    return fields.map((field) => {
      const columnBackgroundColorMap = sectionBackgroundColorMap[field.id];
      return {
        ...field,
        fields: this.deepAppendBackgroundColorMap(field.fields, sectionBackgroundColorMap),
        ...(columnBackgroundColorMap ? { backgroundColorMap: columnBackgroundColorMap } : {}),
      };
    });
  }

  private getCellClassByFieldDefine(define: FieldDefine): string {
    if (define.type === FormFieldTypes.empty) {
      return 'built-in-cell';
    }

    if (this.hyperlinkFieldMap.has(define.type) || define.referenceModule) {
      return 'hyperlink-cell';
    }

    if (this.imageFieldMap.has(define.type)) {
      return 'image-cell';
    }

    if (define.type === FormFieldTypes.decimal) {
      return `number-cell decimal-${define.decimalPlaces ?? 4}-cell`;
    }

    if (this.numberFieldMap.has(define.type)) {
      return 'number-cell';
    }

    return '';
  }

  isShowCheckBox() {
    const entityName = this.section?.gridMeta?.entityName;
    const childLevelEntitys = this.dataTransformService.loadPartialCopyChildLevelEntitys(this.moduleId);
    return childLevelEntitys?.includes(entityName);
  }

  masterToggle(checked: boolean) {
    if (checked) {
      this.gridApi?.selectAll();
    } else {
      this.gridApi?.deselectAll();
    }
    this.gridApi?.refreshCells();
  }

  isFieldCompare(field: FieldDefine, params: any): boolean {
    const businessKey = this.documentCommentPanelService.getBusinessKeyOfRow(params);
    const isFieldCompare = this.documentComparisonQuery.checkCellHasComparison(field, params, businessKey);
    return isFieldCompare;
  }

  private handleFATemplMoreAction(fields: FieldDefine[], unselected: FieldDefine[], newSortColumnDefsList: any[]) {
    if (this.section.id === 'factoryAuditTemplateRequirementsList') {
      const targetIndex = fields.findIndex((f) => f.id === 'moreAction');
      if (targetIndex > -1) {
        const moreActionIndex = unselected.findIndex((f) => f.id === 'moreAction');
        if (moreActionIndex > -1) {
          unselected.splice(moreActionIndex, 1);
        }
        if (newSortColumnDefsList.findIndex((f) => f.id === 'moreAction') > -1) return;

        const moreAction = fields.find((f) => f.id === 'moreAction');
        const insertIndex = Math.min(targetIndex, newSortColumnDefsList.length);
        newSortColumnDefsList.splice(insertIndex, 0, moreAction);
      }
    }
  }

  handleGroupRowsColumnDefs(cols: ColDef[], isReadonly?: boolean) {
    if (isReadonly) {
      return [
        ...this.section.gridMeta.groupConfig.multipleGroupFields.map((g) => ({
          innerRenderer: 'groupRow',
          field: g,
          rowGroup: true,
          hide: true,
        })),
        {
          headerClass: 'header-column',
          headerComponent: 'HeaderCell',
          headerComponentParams: {
            edit: false,
            sortable: false,
          },
          field: '',
          minWidth: 48,
          pinned: 'left',
          lockPosition: true,
          editable: false,
          resizable: false,
          hide: false,
        },
        ...cols,
      ];
    }

    return [
      ...this.section.gridMeta.groupConfig.multipleGroupFields.map((g) => ({
        innerRenderer: 'groupRow',
        field: g,
        rowGroup: true,
        hide: true,
      })),
      ...cols,
    ];
  }
}
