import { DIALOG_DATA } from '@angular/cdk/dialog';
import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Inject } from '@angular/core';

import { TranslocoModule } from '@ngneat/transloco';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { ColDef, GridApi, GridReadyEvent } from 'ag-grid-community';
import { BehaviorSubject, Observable, of, switchMap } from 'rxjs';
import { catchError, map, take, tap } from 'rxjs/operators';

import { ReadUserDetailData } from '../../read-user-detail/read-user-detail-dialog/read-user-detail-data';
import { AcknowledgedUserDetailData } from './acknowledged-user-detail-data';
import { CBX_URL, DETAIL_ROW_HEIGHT } from 'src/app/config/constant';
import { AcknowledgedUserDetailDialogData } from 'src/app/interface/model';
import { CbxDialogHeaderComponent } from 'src/app/modules/shared/common/cbx-dialog-header/cbx-dialog-header.component';
import { CbxTableCellMapperService } from 'src/app/modules/shared/common/cbx-table/cbx-table-cell-mapper.service';
import { CbxTableComponent } from 'src/app/modules/shared/common/cbx-table/cbx-table.component';
import { ApiService } from 'src/app/services/api.service';

@UntilDestroy()
@Component({
  selector: 'app-acknowledged-user-detail-dialog',
  templateUrl: './acknowledged-user-detail-dialog.component.html',
  styleUrls: ['./acknowledged-user-detail-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [AsyncPipe, TranslocoModule, CbxDialogHeaderComponent, CbxTableComponent],
})
export class AcknowledgedUserDetailDialogComponent {
  private readonly apiService = inject(ApiService);

  private readonly recordCache = new Map<string, AcknowledgedUserDetailData[]>();

  readonly cbxTableCellComponents = inject(CbxTableCellMapperService).components;

  readonly currentStatus$ = new BehaviorSubject<'ACKNOWLEDGED' | 'UNACKNOWLEDGED'>('ACKNOWLEDGED');

  readonly loading$ = new BehaviorSubject<boolean>(true);

  readonly rowHeight = DETAIL_ROW_HEIGHT;

  readonly defaultColDef: ColDef = {
    cellRenderer: 'textComponent',
    resizable: true,
    minWidth: 220,
    cellClass: 'popup-table-cell',
  };

  readonly colDefs: ColDef[] = [
    {
      field: 'userName',
      headerName: 'User Name',
      cellRenderer: 'avatarComponent',
    },
    {
      field: 'loginId',
      headerName: 'User ID',
      cellRenderer: 'linkComponent',
    },
    {
      field: 'actionDate',
      headerName: 'Acknowledged on',
      cellRenderer: 'dateComponent',
    },
  ];

  title: string;
  acknowledgedTabLabel: string;
  unacknowledgedTabLabel: string;
  currentRecord: AcknowledgedUserDetailData[];
  gridApi: GridApi;

  constructor(@Inject(DIALOG_DATA) public data: AcknowledgedUserDetailDialogData) {
    const { title, moduleId, refNo, acknowledgedRatio, unacknowledgedRatio } = data;
    this.title = title;

    this.acknowledgedTabLabel = `Acknowledged ${acknowledgedRatio}`;
    this.unacknowledgedTabLabel = `Unacknowledged ${unacknowledgedRatio}`;

    this.handleCurrentStatusChange(moduleId, refNo);
  }

  gridReady(ready: GridReadyEvent) {
    this.gridApi = ready.api;
  }

  handleCurrentStatusChange(moduleId: string, refNo: string) {
    this.currentStatus$
      .pipe(
        tap(() => this.loading$.next(true)),
        switchMap((status: 'ACKNOWLEDGED' | 'UNACKNOWLEDGED') => {
          if (this.recordCache.has(status)) {
            return of(this.recordCache.get(status));
          }

          return this.getRecord$(moduleId, refNo, status);
        }),
        untilDestroyed(this),
      )
      .subscribe((record) => {
        this.currentRecord = record;
        this.loading$.next(false);
      });
  }

  private getRecord$(
    moduleId: string,
    refNo: string,
    status: 'ACKNOWLEDGED' | 'UNACKNOWLEDGED',
  ): Observable<ReadUserDetailData[]> {
    return this.apiService
      .get<ReadUserDetailData[]>(CBX_URL.getAcknowledgedUserDetailList(moduleId, refNo, status))
      .pipe(
        take(1),
        map((userList) => userList ?? []),
        tap((userList) => this.recordCache.set(status, userList)),
        catchError(() => []),
      );
  }
}
