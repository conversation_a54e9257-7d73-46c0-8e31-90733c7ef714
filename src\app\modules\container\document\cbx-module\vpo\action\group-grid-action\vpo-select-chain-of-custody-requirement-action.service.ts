import { Dialog } from '@angular/cdk/dialog';
import { Injectable } from '@angular/core';

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { filter, of } from 'rxjs';

import { AbstractGroupRowUIAction } from '../../../../grid-section-content/group-row-action/model/abstract-group-row-ui-action';
import { DocumentDataQuery, DocumentDataService } from '../../../../state';
import { ChainOfCustodySelect } from '../../model/chain-of-custody-select-data';
import { CBX_URL } from 'src/app/config/constant';
import { defaultDialogConfig } from 'src/app/config/dialog';
import { Vpo_VpoChainOfCustodyDto } from 'src/app/entities/api';
import { TableSelectDialogData, TableSelectDialogResult } from 'src/app/interface/model';
import { TableSelectDialogComponent } from 'src/app/modules/shared/common/table-select-dialog/table-select-dialog.component';
import { ApiService } from 'src/app/services/api.service';
import { deepClone } from 'src/app/utils';

@UntilDestroy()
@Injectable()
export class VpoSelectChainOfCustodyRequirementAction extends AbstractGroupRowUIAction {
  selectChainOfCustody: ChainOfCustodySelect[];
  constructor(
    private readonly dialog: Dialog,
    private readonly documentDataService: DocumentDataService,
    private readonly documentDataQuery: DocumentDataQuery,
    private readonly apiService: ApiService,
  ) {
    super();
  }

  process() {
    const itemRefNo = this.groupRowData[0]?.itemRefNo;
    const vpoRefNo = this.documentDataQuery.getValue().refNo;
    const { viewName, title, selectionMode } = this.actionContext.actionParams.popupMeta;
    const documentViewData = this.documentDataQuery.getFilteredValue();
    const urlParamsString = `itemRefNo=${itemRefNo},vpoRefNo=${vpoRefNo}`;

    const dialogRef = this.dialog.open<TableSelectDialogResult, TableSelectDialogData, TableSelectDialogComponent>(
      TableSelectDialogComponent,
      {
        ...defaultDialogConfig,
        maxWidth: '960px',
        width: '80vw',
        data: {
          title,
          viewId: viewName,
          viewData: documentViewData,
          additionalSearchCondition: { urlParams: `{${urlParamsString}}` },
          rowSelection: selectionMode,
        },
      },
    );

    dialogRef.closed
      .pipe(
        filter((result) => result?.type === 'done'),
        untilDestroyed(this),
      )
      .subscribe(({ payload }) => {
        const { docRef, itemRef } = payload[0];
        this.apiService
          .get(CBX_URL.updateVpoChainOfCustody(docRef, itemRef))
          .subscribe((vpoChainOfCustodyList: Vpo_VpoChainOfCustodyDto[]) => {
            const existingCocList: Vpo_VpoChainOfCustodyDto[] =
              this.documentDataQuery.getValue().vpoChainOfCustodyList ?? [];
            const cocMap = new Map();
            vpoChainOfCustodyList.forEach((vpoChainOfCustody) => {
              cocMap.set(`${vpoChainOfCustody.sectionName}-${vpoChainOfCustody.description}`, vpoChainOfCustody);
            });
            const newCocList = [];
            existingCocList.forEach((existingCoc) => {
              if (
                cocMap.get(`${existingCoc.sectionName}-${existingCoc.description}`) !== undefined &&
                cocMap.get(`${existingCoc.sectionName}-${existingCoc.description}`) !== null &&
                existingCoc.itemRefNo === itemRef
              ) {
                const existingCocDeepClone = deepClone(existingCoc);
                const coc = cocMap.get(`${existingCoc.sectionName}-${existingCoc.description}`);
                existingCocDeepClone.chainOfCustodySupplier = coc.chainOfCustodySupplier;
                existingCocDeepClone.countryOfOrigin = coc.countryOfOrigin;
                existingCocDeepClone.city = coc.city;
                existingCocDeepClone.customFields = coc.customFields;
                existingCocDeepClone.supplierRemarks = coc.supplierRemarks;
                newCocList.push(existingCocDeepClone);
              } else {
                newCocList.push(existingCoc);
              }
            });
            this.documentDataService.updateData({ vpoChainOfCustodyList: newCocList });
          });
      });
    return of(true);
  }
}
