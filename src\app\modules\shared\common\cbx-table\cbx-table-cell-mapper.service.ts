import { Injectable } from '@angular/core';

import { CommonCheckboxCellComponent } from '../cells/edit-cells/common-checkbox-cell/common-checkbox-cell.component';
import { CommonCheckboxHeaderComponent } from '../cells/edit-cells/common-checkbox-header/common-checkbox-header.component';
import { CommonNumberEditCellComponent } from '../cells/edit-cells/common-number-edit-cell/common-number-edit-cell.component';
import { CommonSelectionEditCellComponent } from '../cells/edit-cells/common-selection-edit-cell/common-selection-edit-cell.component';
import { CommonNumberViewCellComponent } from '../cells/view-cells/common-number-view-cell/common-number-view-cell.component';
import { ActionCellComponent } from './action-cell/action-cell.component';
import { AttachmentCellComponent } from './attachment-cell/attachment-cell.component';
import { AvatarCellComponent } from './avatar-cell/avatar-cell.component';
import { ButtonCellComponent } from './button-cell/button-cell.component';
import { CodelistLinkCellComponent } from './codelist-link-cell/codelist-link-cell.component';
import { CodelistPopupCellComponent } from './codelist-popup-cell/codelist-popup-cell.component';
import { ColorCellComponent } from './color-cell/color-cell.component';
import { ColorImageCellComponent } from './color-image-cell/color-image-cell.component';
import { DuedayCellComponent } from './cpm-dueday-cell/dueday-cell.component';
import { StatusCellComponent } from './cpm-status-cell/status-cell.component';
import { DateCellComponent } from './date-cell/date-cell.component';
import { DatetimeCellComponent } from './datetime-cell/datetime-cell.component';
import { GroupCellComponent } from './group-cell/group-cell.component';
import { ImageLinkCellComponent } from './image-link-cell/image-link-cell.component';
import { LinkCellComponent } from './link-cell/link-cell.component';
import { LinkCellEllipsisComponent } from './link-ellipsis-cell/link-cell-ellipsis.component';
import { LinkHighlightCellComponent } from './link-highlight-cell/link-highlight-cell.component';
import { LinkManyCellComponent } from './link-many-cell/link-many-cell.component';
import { LinkMultipleCellComponent } from './link-multiple-cell/link-multiple-cell.component';
import { LinkNewCellComponent } from './link-new-cell/link-new-cell.component';
import { MultipleDateCellComponent } from './multiple-date-cell/multiple-date-cell.component';
import { NotificationReferencesLinkCellComponent } from './notification-references-link-cell/notification-references-link-cell.component';
import { NotificationSubjectLinkCellComponent } from './notification-subject-link-cell/notification-subject-link-cell.component';
import { RelatedActionCellComponent } from './related-action-cell/related-action-cell.component';
import { RichTextCellComponent } from './rich-text-cell/rich-text-cell.component';
import { SearchAllActionCellComponent } from './search-all-action-cell/search-all-action-cell.component';
import { TagsCellComponent } from './tags-cell/tags-cell.component';
import { TextCellComponent } from './text-cell/text-cell.component';
import { TextHighlightCellComponent } from './text-highlight-cell/text-highlight-cell.component';
import { VpoShipmentStatusCellComponent } from './vpo-shipment-status-cell/vpo-shipment-status-cell.component';
import { GridComponents } from 'src/app/interface/model/grid';
import { ListingViewCostSheetNoComponent } from 'src/app/modules/container/document/cbx-module/vq/cell/view-cell';
import { CpmTaskUpdatedOnCellComponent } from 'src/app/modules/container/document/cpm/cpm-task-updated-on-cell/cpm-task-updated-on-cell-component';
import { TextCellViewComponent } from 'src/app/modules/container/document/grid-section-content/grid-section-grid/cell/view-cell';
import { RelatedLinkCellComponent } from 'src/app/modules/container/document/related-link-cell/related-link-cell.component';

@Injectable({ providedIn: 'root' })
export class CbxTableCellMapperService {
  components: GridComponents = {
    commonCheckbox: CommonCheckboxCellComponent,
    commonCheckboxHeader: CommonCheckboxHeaderComponent,
    actionComponent: ActionCellComponent,
    relatedActionComponent: RelatedActionCellComponent,
    textComponent: TextCellComponent,
    linkComponent: LinkCellComponent,
    linkManyCellComponent: LinkManyCellComponent,
    linkMultipleComponent: LinkMultipleCellComponent,
    linkOrNewComponent: LinkNewCellComponent,
    colorComponent: ColorCellComponent,
    colorImageComponent: ColorImageCellComponent,
    imageLinkComponent: ImageLinkCellComponent,
    searchAllActionComponent: SearchAllActionCellComponent,
    textHighlightComponent: TextHighlightCellComponent,
    linkHighlightComponent: LinkHighlightCellComponent,
    linkCellEllipsis: LinkCellEllipsisComponent,
    dateComponent: DateCellComponent,
    datetimeComponent: DatetimeCellComponent,
    statusCellComponent: StatusCellComponent,
    duedayCellComponent: DuedayCellComponent,
    cpmCellUpdatedOnComponent: CpmTaskUpdatedOnCellComponent,
    listingViewCostSheetNoComponent: ListingViewCostSheetNoComponent,
    attachmentComponent: AttachmentCellComponent,
    relatedLinkCellComponent: RelatedLinkCellComponent,
    common_number_edit: CommonNumberEditCellComponent,
    common_number_view: CommonNumberViewCellComponent,
    common_selection_edit: CommonSelectionEditCellComponent,
    notificationSubjectLinkCellComponent: NotificationSubjectLinkCellComponent,
    notificationReferencesLinkCellComponent: NotificationReferencesLinkCellComponent,
    vpoShipmentStatusCellComponent: VpoShipmentStatusCellComponent,
    tagsCellComponent: TagsCellComponent,
    codelistLinkCellComponent: CodelistLinkCellComponent,
    codelistPopupCellComponent: CodelistPopupCellComponent,
    buttonComponent: ButtonCellComponent,
    textCellViewComponent: TextCellViewComponent,
    multipleDateCellComponent: MultipleDateCellComponent,
    groupCellComponent: GroupCellComponent,
    avatarComponent: AvatarCellComponent,
    richTextComponent: RichTextCellComponent,
  };
}
