import { HttpErrorResponse, HttpEvent, HttpEventType } from '@angular/common/http';
import { Directive, EventEmitter, inject, Input, Output } from '@angular/core';

import { TranslocoService } from '@ngneat/transloco';
import * as R from 'ramda';
import { switchMap, filter } from 'rxjs';

import { AzureUploadService } from '../../../../services/azure-upload.service';
import { DomainAttributeService } from '../../../../workspace/service/domain-attribute.service';
import { UploadResponse } from 'src/app/interface/model/file-upload';
import { FileService } from 'src/app/modules/services/file.service';
import { ConfigurationService } from 'src/app/services/configuration.service';
import { NotificationService } from 'src/app/services/notification.service';
import { isNotEmptyOrNil } from 'src/app/utils';
import { getFileExtension, getTypeClass, isWildcard } from 'src/app/utils/file-util';

@Directive()
export class FileUploadDirective {
  @Input() urlType: string;
  @Input() acceptFileType: string;
  @Input() maxFileSize: number;
  @Input() maximumFileCount = -1;
  @Input() nowFileCount = 0;
  @Input() enableUpload = true;
  @Input() promptUploadResult: boolean;
  @Input() limitationFileSize = '10MB';
  @Input() skipFileTypeValidate = false;

  @Output() fileSelected: EventEmitter<File[]> = new EventEmitter<File[]>();
  @Output() uploadStarted: EventEmitter<any> = new EventEmitter();
  @Output() uploadSucceed: EventEmitter<UploadResponse> = new EventEmitter<UploadResponse>();
  @Output() uploadFailed: EventEmitter<any> = new EventEmitter();
  @Output() progressChanged: EventEmitter<number> = new EventEmitter<number>();

  azureUploadService = inject(AzureUploadService, { optional: true });
  protected domainAttributeService = inject(DomainAttributeService, { optional: true });

  limitationSize = this.configurationService.CONFIG.FILE_UPLOAD_LIMITATION ?? 10;
  sizeLimit = 2 ** 20 * this.limitationSize;

  constructor(
    protected readonly fileService: FileService,
    protected readonly notificationService: NotificationService,
    protected readonly translocoService: TranslocoService,
    protected readonly configurationService: ConfigurationService,
  ) {}

  protected upload(file: File) {
    this.uploadStarted.emit();
    const fileUpload =
      this.urlType === 'image'
        ? this.fileService.uploadImage$(file)
        : this.urlType === 'importRawData'
        ? this.fileService.ImportRawData$(file)
        : this.fileService.uploadFile$(file);

    this.fileService
      .validateBlacklistedFile([file])
      .pipe(
        filter((isBlacklisted) => !isBlacklisted),
        switchMap(() => fileUpload),
      )
      .subscribe({
        next: (event: HttpEvent<any>) => {
          switch (event.type) {
            case HttpEventType.Sent:
              break;
            case HttpEventType.Response:
              if (event.status >= 200 && event.status < 300) {
                if (this.promptUploadResult) {
                  let message = '';
                  if (event.status !== 200) {
                    message = this.translocoService.translate('uploadfilex1fail', { $1: `${file.name}` });
                  } else {
                    message = this.translocoService.translate('uploadfilex1success', {
                      $1: `${file.name}`,
                    });
                  }
                  this.notificationService.open({ message, type: event.status === 200 ? 'primary' : 'warn' });
                  this.uploadFailed.emit();
                  break;
                }
                this.uploadSucceed.emit({ originalEvent: event, file } as UploadResponse);
              } else {
                const message = this.translocoService.translate('x1uploaderrorstatuscodex2', {
                  $1: `${file.name}`,
                  $2: `${event.status}`,
                });
                this.notificationService.open({ message, type: 'warn' });
                this.uploadFailed.emit();
              }

              break;
            case HttpEventType.UploadProgress: {
              if (event.loaded) {
                const progress = Math.round((event.loaded * 100) / event.total);
                this.progressChanged.emit(progress);
              }

              break;
            }
            default:
          }
        },
        error: (error) => {
          if (this.domainAttributeService.isAzureBlob() && this.azureUploadService.isCancelUpload$.getValue()) {
            console.info('cancel upload file to azure blob.');
            console.warn(error);
            return;
          }
          let message = this.translocoService.translate('x1uploaderrormessagex2', {
            $1: `${file.name}`,
            $2: `${error.message}`,
          });
          const aa = error as HttpErrorResponse;
          if (aa.error && aa.error.message) {
            message = `${aa.error.message}`;
          }
          this.notificationService.open({ message, type: 'warn' });
          this.uploadFailed.emit();
        },
      });
  }

  protected getValidFiles(files: FileList) {
    const errors = this.getFileArray(files)
      .map((file) => this.generateErrorMessage(file))
      .filter((errMsg) => isNotEmptyOrNil(errMsg));

    if (errors.length > 0) {
      const message = errors.reduce((acc, error) => `${acc}${error} \n\n`, '');
      this.notificationService.open({ message, type: 'warn' });
    }
    return this.getFileArray(files)
      .filter((file) => file.size < this.sizeLimit)
      .slice(0, this.getQuota());
  }

  protected getFileArray(fileList: FileList): File[] {
    return Array.from(fileList);
  }

  protected getQuota(): number {
    return this.maximumFileCount === -1 ? Infinity : this.maximumFileCount - this.nowFileCount;
  }

  private isFileTypeValid(file: File): boolean {
    if (
      this.acceptFileType === '*' ||
      getFileExtension(file.name).toLowerCase() === 'glb' ||
      getFileExtension(file.name).toLowerCase() === 'ai' ||
      getFileExtension(file.name).toLowerCase() === 'pdf'
    ) {
      return true;
    }
    const getAcceptFileTypes = (acceptFileTypes: string[]) => R.map((type: string) => type.trim())(acceptFileTypes);
    const anyType = R.any((type: string) =>
      isWildcard(type)
        ? getTypeClass(file.type) === getTypeClass(type)
        : file.type === type || getFileExtension(file.name).toLowerCase() === getFileExtension(type).toLowerCase(),
    );
    return R.pipe(R.split(','), getAcceptFileTypes, anyType)(this.acceptFileType);
  }

  generateErrorMessage(file: File) {
    if (this.acceptFileType && !this.isFileTypeValid(file) && file.size < this.sizeLimit) {
      return this.translocoService.translate('x1hasinvalidfiletypeonlyacceptx2', {
        $1: `${file.name}`,
        $2: `${this.acceptFileType}`,
      });
    }
    if (file.size > this.sizeLimit) {
      return this.translocoService.translate('x1isgreaterthenmaximunuploadsizex2', {
        $1: `${file.name}`,
        $2: `${this.limitationSize}MB`,
      });
    }
    return null;
  }
}
