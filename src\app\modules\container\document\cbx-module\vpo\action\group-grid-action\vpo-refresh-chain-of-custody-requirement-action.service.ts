import { Dialog } from '@angular/cdk/dialog';
import { Injectable } from '@angular/core';

import { <PERSON><PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';

import { generalAlertDialogConfig } from '../../../../../../../config/dialog';
import { Vpo_VpoChainOfCustodyDto } from '../../../../../../../entities/api';
import { AbstractGroupRowUIAction } from '../../../../grid-section-content/group-row-action/model/abstract-group-row-ui-action';
import { DocumentDataQuery, DocumentDataService } from '../../../../state';
import { CBX_URL } from 'src/app/config/constant';
import { AlertDialogComponent } from 'src/app/modules/shared/common/warning-dialog/alert-dialog.component';
import { ApiService } from 'src/app/services/api.service';

@UntilDestroy()
@Injectable()
export class VpoRefreshChainOfCustodyRequirementAction extends AbstractGroupRowUIAction {
  constructor(
    private readonly dialog: Dialog,
    private readonly documentDataService: DocumentDataService,
    private readonly documentDataQuery: DocumentDataQuery,
    private readonly apiService: ApiService,
  ) {
    super();
  }

  validate(): Observable<boolean> {
    return this.openWarningDialog().closed.pipe(map((result) => result?.type === 'done'));
  }

  process() {
    const itemRefNo = this.groupRowData[0]?.itemRefNo;
    this.apiService
      .get(CBX_URL.refreshVpoChainOfCustody(itemRefNo))
      .subscribe((vpoChainOfCustodyList: Vpo_VpoChainOfCustodyDto[]) => {
        const existingCocList: Vpo_VpoChainOfCustodyDto[] =
          this.documentDataQuery.getValue().vpoChainOfCustodyList ?? [];
        const result = vpoChainOfCustodyList?.map((vpoChainOfCustody) => ({
          ...vpoChainOfCustody,
          isFromTemplate: true,
        }));
        const newCocList = existingCocList.filter((coc) => coc.itemRefNo !== itemRefNo);
        newCocList.push(...result);

        this.documentDataService.updateData({ vpoChainOfCustodyList: newCocList });
      });

    return of(true);
  }

  openWarningDialog() {
    return this.dialog.open<any>(AlertDialogComponent, {
      ...generalAlertDialogConfig,
      data: {
        messageButtons: 'YesNo',
        message:
          'We will reset the details below for this item and get the latest template from item module. Are you sure to continue?',
        title: 'warning',
      },
    });
  }
}
