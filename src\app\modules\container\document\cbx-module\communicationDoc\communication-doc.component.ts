import { ChangeDetectionStrategy, Component } from '@angular/core';

import { DocumentComponentStrategy } from '../../../document-switch/document-component-strategy';
import { GridActionDispatcherService } from '../../grid-section-content/service/grid-action-dispatcher.service';
import { CellMapperModuleToken } from '../../service/cell-mapper-constant';
import { CellMapperService } from '../../service/cell-mapper.service';
import { DocumentDropdownService } from '../../service/document-dropdown.service';
import { DocumentDynamicDataService } from '../../service/document-dynamic-data.service';
import { DocumentDynamicFieldService } from '../../service/document-dynamic-field.service';
import { DocumentFacadeService } from '../../service/document-facade.service';
import { DocumentFieldReadonlyService } from '../../service/document-field-readonly.service';
import { FieldMapperModuleToken } from '../../service/field-mapper-constant';
import { FieldMapperService } from '../../service/field-mapper.service';
import { CommunicationDocCellMapperService } from './service/communication-doc-cell-mapper.service';
import { CommunicationDocDropdownService } from './service/communication-doc-dropdown.service';
import { CommunicationDocDynamicDataService } from './service/communication-doc-dynamic-data.service';
import { CommunicationDocDynamicFieldService } from './service/communication-doc-dynamic-field.service';
import { CommunicationDocFieldMapperService } from './service/communication-doc-field-mapper.service';
import { CommunicationDocFieldReadonlyService } from './service/communication-doc-field-readonly.service';
import { CommunicationDocGridActionDispatcherService } from './service/communication-doc-grid-action-dispatcher.service';

@Component({
  selector: 'app-communication-doc',
  template: '',
  standalone: true,

  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    DocumentFacadeService,
    {
      provide: DocumentDynamicDataService,
      useClass: CommunicationDocDynamicDataService,
    },
    {
      provide: DocumentDynamicFieldService,
      useClass: CommunicationDocDynamicFieldService,
    },
    {
      provide: DocumentDropdownService,
      useClass: CommunicationDocDropdownService,
    },
    {
      provide: DocumentFieldReadonlyService,
      useClass: CommunicationDocFieldReadonlyService,
    },
    CellMapperService,
    {
      provide: CellMapperModuleToken,
      useClass: CommunicationDocCellMapperService,
    },
    FieldMapperService,
    {
      provide: FieldMapperModuleToken,
      useClass: CommunicationDocFieldMapperService,
    },
    {
      provide: GridActionDispatcherService,
      useClass: CommunicationDocGridActionDispatcherService,
    },
  ],
})
export default class CommunicationDocComponent extends DocumentComponentStrategy {
  constructor(private readonly documentFacadeService: DocumentFacadeService) {
    super();
    this.documentFacadeService.registerBusinessLogic();
  }
}
