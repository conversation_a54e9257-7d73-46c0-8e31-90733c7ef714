import { Injectable } from '@angular/core';

import { RowClassParams } from 'ag-grid-community';

import { isEmptyOrNil, isNotEmptyOrNil } from '../../../../../../utils';
import { GridStyleService } from '../../../grid-section-content/service/grid-style.service';

@Injectable()
export class VpoGroupRowStyleService extends GridStyleService {
  rowClassRulesMap = {
    ...this.rowClassRulesMap,
    vpoChainOfCustodyList: {
      'cbx-empty-row-section': (params: RowClassParams) =>
        isEmptyOrNil(params?.data?.groupKey2) && isNotEmptyOrNil(params?.data?.groupKey1),
    },
  };
}
