import { Dialog } from '@angular/cdk/dialog';
import { inject, Injectable } from '@angular/core';

import { Until<PERSON><PERSON>roy } from '@ngneat/until-destroy';
import { Observable, of } from 'rxjs';
import { catchError, filter, map, switchMap } from 'rxjs/operators';

import { defaultDialogConfig } from '../../../../../../../config/dialog';
import { Vpo_VpoChainOfCustodyDto } from '../../../../../../../entities/api';
import { isNotNil } from '../../../../../../../utils';
import { DocumentService } from '../../../../../../services/document.service';
import {
  GridGroupingSectionEditorDialogComponent,
  GridGroupingSectionEditorDialogData,
} from '../../../../dialog/grid-grouping-section-editor-dialog/grid-grouping-section-editor-dialog.component';
import { AbstractGridUiAction } from '../../../../grid-section-content/grid-action/model/abstract-grid-ui-action';
import { DocumentDataQuery, DocumentDataService } from '../../../../state';

@UntilDestroy()
@Injectable()
export class VpoAddChainOfCustodyRequirementAction extends AbstractGridUiAction {
  private readonly dialog = inject(Dialog);
  private readonly documentDataService = inject(DocumentDataService);
  private readonly documentDataQuery = inject(DocumentDataQuery);
  private readonly documentService = inject(DocumentService);

  process(): Observable<any> {
    const existingRowData: Vpo_VpoChainOfCustodyDto[] = this.documentDataQuery.getValue().vpoChainOfCustodyList ?? [];

    return this.dialog
      .open<any, GridGroupingSectionEditorDialogData, GridGroupingSectionEditorDialogComponent>(
        GridGroupingSectionEditorDialogComponent,
        {
          ...defaultDialogConfig,
          width: '380px',
          data: {
            title: 'New Requirement',
            inputLabel: 'Requirement Name',
            sectionValue: '',
          },
        },
      )
      .closed.pipe(
        filter((result) => result?.payload),
        switchMap((result) => {
          const sectionName = result.payload;
          const sequences = existingRowData.map((row) => row.sectionSeqNo).filter((seq) => isNotNil(seq));
          const maxBaseSeq = Math.max(...sequences, 0);

          return this.documentService.newLineGetDefaultValues(this.moduleId, this.sectionId).pipe(
            catchError(() => {
              console.error('Cannot get the default value.');
              return of({});
            }),
            map((defaultValue) => {
              const rowData = this.gridSectionService.generateRows(
                this.fields,
                null,
                [
                  {
                    ...defaultValue,
                    sectionName,
                    sectionSeqNo: maxBaseSeq + 1,
                  },
                ],
                this.seqId,
                false,
              );
              this.documentDataService.updateData({ vpoChainOfCustodyList: [...existingRowData, ...rowData] });
            }),
          );
        }),
      );
  }

  finalizeProcess() {
    super.finalizeProcess();

    const groupNodeList = [];
    this.gridSectionService.gridApi.forEachNode((node) => {
      if (node.group) {
        groupNodeList.push(node);
      }
    });

    this.gridSectionService.gridApi.redrawRows({ rowNodes: groupNodeList });
  }
}
