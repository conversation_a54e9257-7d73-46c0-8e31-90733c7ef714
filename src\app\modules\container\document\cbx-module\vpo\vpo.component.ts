import { ChangeDetectionStrategy, Component } from '@angular/core';

import { Until<PERSON><PERSON>roy, untilDestroyed } from '@ngneat/until-destroy';
import * as R from 'ramda';
import { filter } from 'rxjs/operators';

import { deepClone, isNotEmptyOrNil } from '../../../../../utils';
import { DocumentComponentStrategy } from '../../../document-switch/document-component-strategy';
import { GridActionDispatcherService } from '../../grid-section-content/service/grid-action-dispatcher.service';
import { GridStyleService } from '../../grid-section-content/service/grid-style.service';
import { GroupRowActionDispatcherService } from '../../grid-section-content/service/group-row-action-dispacher.service';
import { GroupGridActionDispatcherService } from '../../group-grid-container/service/group-grid-action-dispatcher.service';
import { CellMapperModuleToken } from '../../service/cell-mapper-constant';
import { CellMapperService } from '../../service/cell-mapper.service';
import { DocumentActionDispatcherService } from '../../service/document-action-dispatcher.service';
import { DocumentActionService } from '../../service/document-action.service';
import { DocumentDropdownService } from '../../service/document-dropdown.service';
import { DocumentDynamicDataService } from '../../service/document-dynamic-data.service';
import { DocumentDynamicFieldService } from '../../service/document-dynamic-field.service';
import { DocumentFacadeService } from '../../service/document-facade.service';
import { DocumentFieldReadonlyService } from '../../service/document-field-readonly.service';
import { FieldMapperService } from '../../service/field-mapper.service';
import { SectionHeaderContentMapperService } from '../../service/section-header-content-mapper.service';
import { SECTION_MAPPER_SERVICE } from '../../service/section-mapper';
import { DocumentDataQuery } from '../../state';
import { DocumentCommentPanelService } from '../../state/document-comment-panel.service';
import { DocumentDataActionService } from '../../state/document-data-action.service';
import {
  VpoDocumentState,
  VpoDto,
  VpoItemDto,
  VpoOverrideItemCsDto,
  VpoOverrideShipDtlCsDto,
  VpoOverrideShipDtlDto,
  VpoShipDto,
} from './model/vpo-dto';
import { VpoActionService } from './service/vpo-action.service';
import { VpoCellMapperService } from './service/vpo-cell-mapper.service';
import { VpoCommentPanelService } from './service/vpo-comment-panel.service';
import { VpoDocumentActionDispatcherService } from './service/vpo-document-action-dispatcher.service';
import { VpoDropdownService } from './service/vpo-dropdown.service';
import { VpoDynamicDataService } from './service/vpo-dynamic-data.service';
import { VpoDynamicFieldService } from './service/vpo-dynamic-field.service';
import { VpoFieldReadonlyService } from './service/vpo-field-readonly.service';
import { VpoGridActionDispatcherService } from './service/vpo-grid-action-dispatcher.service';
import { VpoGroupGridActionDispatcherService } from './service/vpo-group-grid-action-dispatcher.service';
import { VpoGroupRowActionDispatcherService } from './service/vpo-group-row-action-dispatcher.service';
import { VpoGroupRowStyleService } from './service/vpo-group-row-style.service';
import { VpoSectionHeaderContentMapperService } from './service/vpo-section-header-content-mapper.service';
import { VpoSectionMapperServiceTsService } from './service/vpo-section-mapper.service.ts.service';
import { VpoTraceabilityMapService } from './service/vpo-traceability-map.service';
import { Vpo_VpoShipDtlDto, Vpo_VpoShipDto } from 'src/app/entities/api';

@UntilDestroy()
@Component({
  selector: 'app-vpo',
  template: '',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    DocumentFacadeService,
    {
      provide: DocumentDynamicDataService,
      useClass: VpoDynamicDataService,
    },
    {
      provide: DocumentDynamicFieldService,
      useClass: VpoDynamicFieldService,
    },
    {
      provide: DocumentDropdownService,
      useClass: VpoDropdownService,
    },
    {
      provide: DocumentFieldReadonlyService,
      useClass: VpoFieldReadonlyService,
    },
    CellMapperService,
    {
      provide: CellMapperModuleToken,
      useClass: VpoCellMapperService,
    },
    FieldMapperService,
    {
      provide: SectionHeaderContentMapperService,
      useClass: VpoSectionHeaderContentMapperService,
    },
    {
      provide: DocumentActionService,
      useClass: VpoActionService,
    },
    {
      provide: DocumentActionDispatcherService,
      useClass: VpoDocumentActionDispatcherService,
    },
    {
      provide: GridActionDispatcherService,
      useClass: VpoGridActionDispatcherService,
    },
    {
      provide: GroupRowActionDispatcherService,
      useClass: VpoGroupRowActionDispatcherService,
    },
    {
      provide: GroupGridActionDispatcherService,
      useClass: VpoGroupGridActionDispatcherService,
    },
    VpoTraceabilityMapService,
    {
      provide: SECTION_MAPPER_SERVICE,
      useClass: VpoSectionMapperServiceTsService,
    },
    {
      provide: DocumentCommentPanelService,
      useClass: VpoCommentPanelService,
    },
    {
      provide: GridStyleService,
      useClass: VpoGroupRowStyleService,
    },
  ],
})
export default class VpoComponent extends DocumentComponentStrategy {
  constructor(
    private readonly documentFacadeService: DocumentFacadeService,
    private readonly documentDataQuery: DocumentDataQuery,
    private readonly documentDataActionService: DocumentDataActionService,
  ) {
    super();
    this.documentDataActionService.dataTransferFromApiToStore$.next(this.dataTransferFromApiToStore);

    this.documentDataActionService.dataTransferFromStoreToApi$.next(this.dataTransferFromStoreToApi);

    const dataInitialized$ = this.documentDataQuery.selectDataInitialized$().pipe(
      filter((dataInitialized) => dataInitialized),
      untilDestroyed(this),
    );

    dataInitialized$.subscribe(() => this.documentFacadeService.registerBusinessLogic());
  }

  private dataTransferFromApiToStore(data: VpoDto): VpoDocumentState {
    const vpoItemList: VpoItemDto[] = data?.vpoItemList as VpoItemDto[];

    const vpoItemCsList: VpoOverrideItemCsDto[][] = data?.vpoItemList
      ?.map((item) => item.vpoItemCsList)
      .filter(isNotEmptyOrNil);

    const vpoShipDtlCsGroupList: VpoOverrideShipDtlCsDto[][] = data?.vpoShipDtlDtoGroupList
      ?.map((list) => R.flatten(list.map((ship) => ship.vpoShipDtlCsDto).filter(isNotEmptyOrNil)))
      .filter(isNotEmptyOrNil);

    const vpoShipDtlDtoGroupList: VpoOverrideShipDtlDto[][] = data?.vpoShipDtlDtoGroupList?.map((list) =>
      list.map((ship) => {
        const vpoShipId = data?.vpoShipList.find((shipList) => shipList.shipmentNo === ship.shipmentNo);
        return { ...ship, vpoShipId };
      }),
    );

    return { ...data, vpoItemList, vpoItemCsList, vpoShipDtlCsGroupList, vpoShipDtlDtoGroupList };
  }

  private dataTransferFromStoreToApi(originalvpoData: VpoDocumentState): VpoDto {
    const vpoData = deepClone(originalvpoData);
    const vpoItemList = vpoData.vpoItemList?.map((vpoItem) => {
      const vpoItemCsList =
        vpoData.vpoItemCsList?.find((csList) => csList.find((cs) => cs.refNo === vpoItem.refNo)) || [];
      return { ...vpoItem, vpoItemCsList };
    });

    const vpoShipList: VpoShipDto[] = [];
    const vpoShipDtlDtoGroupList = vpoData.vpoShipDtlDtoGroupList?.map((dtlDtoGroupList) => {
      const dtoGroupList = dtlDtoGroupList.map((group) => {
        const vpoShipDtlCsDto =
          vpoData.vpoShipDtlCsGroupList?.find((dtlCsGroup) =>
            dtlCsGroup.find(
              (csGroup) => csGroup.vpoItemRef === group.vpoItemRef && csGroup.shipmentNo === group.shipmentNo,
            ),
          ) || [];
        return { ...group, vpoShipDtlCsDto };
      });

      const vpoShipId: VpoShipDto = {
        ...dtoGroupList[0].vpoShipId,
        shipmentNo: dtoGroupList[0].shipmentNo,
        // refNo: dtoGroupList[0].refNo ?? dtoGroupList[0].shipmentNo,
      };
      vpoShipList.push(vpoShipId);

      return dtoGroupList.map<Vpo_VpoShipDtlDto>((ship) => ({ ...ship, vpoShipId: {} as Vpo_VpoShipDto }));
    });

    delete vpoData.vpoItemCsList;
    delete vpoData.vpoShipDtlCsGroupList;

    return {
      ...vpoData,
      vpoShipDtlDtoGroupList,
      vpoShipList,
      vpoItemList,
    };
  }
}
