import { Dialog } from '@angular/cdk/dialog';
import { NgIf } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Inject,
  Input,
  Output,
  ViewContainerRef,
  inject,
} from '@angular/core';

import { Until<PERSON><PERSON>roy } from '@ngneat/until-destroy';

import { BaseImageComponent } from '../base-component/base-image/base-image.component';
import { BasePdfComponent } from '../base-component/base-pdf/base-pdf.component';
import { PdfViewerDialogComponent } from '../pdf-viewer-dialog/pdf-viewer-dialog.component';
import { IconComponent } from 'src/app/component/icon/icon.component';
import { defaultImageGalleryDialogConfig } from 'src/app/config/image-gallery';
import { ImageGalleryViewDialogData, ItemImage, PdfViewerDialogData } from 'src/app/interface/model';
import { FileInfo } from 'src/app/interface/model/file-info';
import { ImageLeafletGalleryViewDialogComponent } from 'src/app/modules/container/document/dynamic-form-field/view-field/image-leaflet-gallery-view-dialog/image-leaflet-gallery-view-dialog.component';
import { WINDOW } from 'src/app/services/window.service';

@UntilDestroy()
@Component({
  selector: 'app-file-viewer',
  templateUrl: './file-viewer.component.html',
  styleUrls: ['./file-viewer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [NgIf, BaseImageComponent, BasePdfComponent, IconComponent],
})
export class FileViewerComponent {
  vcr = inject(ViewContainerRef, { skipSelf: true });

  @Input() attachment: FileInfo;
  @Input() edit: boolean;

  @Output() removeFile = new EventEmitter<FileInfo>();

  imageFile = {
    jpg: true,
    jpeg: true,
    png: true,
    gif: true,
    tif: true,
  };

  pdfFile = {
    pdf: true,
  };

  constructor(
    @Inject(WINDOW)
    private readonly window: Window,
    private readonly dialog: Dialog,
  ) {}

  onClick() {
    if (
      this.attachment.fileExtension === 'jpg' ||
      this.attachment.fileExtension === 'jpeg' ||
      this.attachment.fileExtension === 'png'
    ) {
      const images = [];
      const activeIndex = 0;

      const file = {
        id: this.attachment.id,
        original: this.attachment,
      };

      images.push({
        file,
      });

      this.openDialog(images, activeIndex);
    } else if (this.attachment.fileExtension === 'pdf') {
      this.openPdfViewerDialog();
    } else {
      this.window.open(this.attachment.url as string, '_blank');
    }
  }

  openDialog(images: ItemImage[], activeIndex: number) {
    this.dialog.open<any, ImageGalleryViewDialogData, ImageLeafletGalleryViewDialogComponent>(
      ImageLeafletGalleryViewDialogComponent,
      {
        ...defaultImageGalleryDialogConfig,
        data: {
          images,
          activeIndex,
          multipleMode: true,
        },
        viewContainerRef: this.vcr,
      },
    );
  }

  openPdfViewerDialog() {
    this.dialog.open<any, PdfViewerDialogData, PdfViewerDialogComponent>(PdfViewerDialogComponent, {
      ...defaultImageGalleryDialogConfig,
      data: {
        attachment: this.attachment,
      },
      viewContainerRef: this.vcr,
    });
  }

  deleteImage(attachment: FileInfo, $event: any) {
    this.removeFile.emit(attachment);
    $event.stopPropagation();
    $event.preventDefault();
  }
}
