import { AsyncPipe } from '@angular/common';
import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { map } from 'rxjs';

import { ComparisonDataResolved } from '../comparison-data-resolver';
import { WhatIfComparisonComponent } from '../what-if-comparison/what-if-comparison.component';

@Component({
  selector: 'app-what-if-comparison-wrap',
  templateUrl: './what-if-comparison-wrap.component.html',
  standalone: true,
  imports: [AsyncPipe, WhatIfComparisonComponent],
})
export class WhatIfComparisonWrapComponent {
  constructor(private readonly route: ActivatedRoute) {}
  resolvedData$ = this.route.data.pipe<ComparisonDataResolved>(map((data) => data?.resolvedData));
  moduleId$ = this.resolvedData$.pipe<string>(map((resolvedData) => resolvedData?.moduleId));
  refNos$ = this.resolvedData$.pipe<string[]>(map((resolvedData) => resolvedData?.refNos));
}
