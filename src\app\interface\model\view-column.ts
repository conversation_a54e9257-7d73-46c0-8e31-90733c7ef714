import { ColDef } from 'ag-grid-community';

export interface ViewColumn {
  action?: string;
  actionParams?: ViewColumnActionParams;
  alignment?: string | 'left' | 'right';
  allowSimpleSearch?: boolean;
  dataFormat?: string;
  fieldId?: string;
  frozenType?: 'left' | 'right';
  id?: string;
  label?: string;
  labelId?: string;
  mapping?: string;
  size?: string;
  sortable?: boolean;
  type?: ColumnType;
  valueType?: string;
  visible?: boolean;
  backgroundColorMap?: { [value: string]: string };
  format?: string;
  codelist?: string;
  resizable?: boolean;
  symbol?: string;
  symbolPosition?: string;
  isDetailViewColumn?: boolean;
}

export interface ViewColumnActionParams {
  naviModule?: string;
  moduleId: string;
  fieldId?: string;
  format?: string;
  refNo?: string;
  version?: string;
  actionLabel?: string;
  dmrNames?: string;
  vendorDomainReadonly?: string;
}

export type ColumnType =
  | 'RefNo'
  | 'Text'
  | 'Label'
  | 'Date'
  | 'Hyperlink'
  | 'HyperlinkBySupplier'
  | 'HyperlinkMultiple'
  | 'HyperlinkOrNew'
  | 'Number'
  | 'Decimal'
  | 'Datetime'
  | 'DateTime'
  | 'CpmTask'
  | 'Codelist'
  | 'CodeList'
  | 'checkbox'
  | 'toolbox'
  | 'image'
  | 'button'
  | 'frozenLine'
  | 'Thumbnail'
  | 'ColorChip'
  | 'ColorImage'
  | 'CostSheetNo'
  | 'ReferencesCellRenderer'
  | 'Dropdown'
  | 'CodelistHyperlink'
  | 'CodelistPopup'
  | 'NotificationSubjectLink'
  | 'NotificationReferencesLink'
  | 'Percentage'
  | 'VpoShipmentStatus'
  | 'MultipleDate'
  | 'Tags'
  | 'RichText';

export type ColumnTypeMap<T> = Partial<{ [type in ColumnType]: T }>;

export type ModeType = 'list' | 'detail' | 'grid' | 'markup-tool';

export type FilterType = 'text' | 'number' | 'date' | 'percentage' | 'tags';

export interface LabelSelectedColumn {
  label: string;
  selected: boolean;
  dataColumn: ColDef;
  subFields?: any[];
}
