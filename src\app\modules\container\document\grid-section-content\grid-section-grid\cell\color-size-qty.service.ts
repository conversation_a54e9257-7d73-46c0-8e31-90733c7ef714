import { DialogConfig } from '@angular/cdk/dialog';
import { Injectable } from '@angular/core';

import { combineLatest, Observable, of } from 'rxjs';

import { FormDefine_DynamicallyCombined } from '../../../../../../entities/api';
import { ColorAndSizeHandler } from './quantity-dialog/color-size/color-and-size-handler';
import { ColorHandler } from './quantity-dialog/color-size/color-handler';
import { ColorSizeCodeMap, NoneHandler, TypeHandler } from './quantity-dialog/color-size/handler';
import { SizeHandler } from './quantity-dialog/color-size/size-handler';
import { CodelistItem } from 'src/app/entities/codelist';
import { ItemColorList, ItemSizeList } from 'src/app/entities/item-color-size';
import { AnyObject } from 'src/app/interface/model';
import { DocumentDataQuery } from 'src/app/modules/container/document/state';
import { isEmptyOrNil } from 'src/app/utils';

@Injectable()
export class ColorSizeQtyService {
  private readonly factoryClass = new Map<ColorSizeCodeMap, TypeHandler>();
  private typeHandler: TypeHandler;

  constructor(private readonly documentDataQuery: DocumentDataQuery) {
    this.factoryClass.set(ColorSizeCodeMap.None, new NoneHandler());
    this.factoryClass.set(ColorSizeCodeMap.Color, new ColorHandler());
    this.factoryClass.set(ColorSizeCodeMap.Size, new SizeHandler());
    this.factoryClass.set(ColorSizeCodeMap.ColorAndSize, new ColorAndSizeHandler());
  }

  setActiveTypeHandler(selectedCodeList: CodelistItem) {
    if (!selectedCodeList) {
      this.typeHandler = new NoneHandler();
      return;
    }

    this.typeHandler = this.factoryClass.get(selectedCodeList.code as ColorSizeCodeMap);
  }

  getActiveTypeHandler(): TypeHandler {
    return this.typeHandler;
  }

  resetItemData$(itemColorList: ItemColorList[], itemSizeList: ItemSizeList[]) {
    const colorDynamicallyCombined = {
      displayKey: 'shortName',
      uniqueKey: 'id',
      conditions: [
        {
          id: 'isPrimary',
          expList: [true],
        },
        {
          id: 'isInactive',
          expList: [false],
        },
      ],
    } as FormDefine_DynamicallyCombined;

    const sizeDynamicallyCombined = {
      displayKey: 'sizeDisplayName',
      uniqueKey: 'dimension.code',
      conditions: [
        {
          id: 'dimension.code',
          dataKey: 'code',
          codelistName: 'DIMENSION_NAME',
          codelistField: 'customFieldCodeList2',
          expList: ['HEI-GHT', 'SIZE'],
        },
        {
          id: 'isInactive',
          expList: [false],
        },
      ],
      groupByKey: 'dimension.code',
      sortByAfterGroupByValues: ['HEI-GHT', 'SIZE'],
    } as FormDefine_DynamicallyCombined;

    const itemList: { itemColorList: ItemColorList; itemSizeList: ItemSizeList } = this.typeHandler.getItemList(
      itemColorList,
      itemSizeList,
    );

    const colorCombine = this.documentDataQuery.selectCombinedDataByDynamicallyCombinedDefine(
      colorDynamicallyCombined,
      of(itemList.itemColorList),
    );

    const sizeCombine = this.documentDataQuery.selectCombinedDataByDynamicallyCombinedDefine(
      sizeDynamicallyCombined,
      of(itemList.itemSizeList),
    );

    return combineLatest([colorCombine, sizeCombine]);
  }

  getDialogConfig(data: AnyObject<any>): DialogConfig {
    return {
      minWidth: '510px',
      width: '39.8vw',
      data,
      autoFocus: 'dialog',
    };
  }

  planQtyByFilter(
    options: CodelistItem[],
    itemColorList: ItemColorList[],
    itemSizeList: ItemSizeList[],
  ): Observable<CodelistItem[]> {
    const filterOption = options.filter((option) => {
      if (isEmptyOrNil(itemSizeList) && isEmptyOrNil(itemColorList)) {
        return false;
      }

      if (isEmptyOrNil(itemSizeList)) {
        return option.code === ColorSizeCodeMap.Color;
      }

      if (isEmptyOrNil(itemColorList)) {
        return option.code === ColorSizeCodeMap.Size;
      }

      return true;
    });

    return of(filterOption);
  }
}
