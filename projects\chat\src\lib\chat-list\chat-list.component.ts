import { CdkMenuModule } from '@angular/cdk/menu';
import { CdkConnectedOverlay, CdkOverlayOrigin, Overlay, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { Async<PERSON>ip<PERSON>, NgFor, NgIf, NgTemplateOutlet } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
} from '@angular/core';

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject, combineLatest, interval, NEVER, ReplaySubject, Subject } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  map,
  shareReplay,
  skip,
  startWith,
  switchMap,
  take,
  takeUntil,
} from 'rxjs/operators';

import { ChatCreateRoomComponent } from '../chat-create-room/chat-create-room.component';
import { ChatListMessageComponent } from '../chat-list-message/chat-list-message.component';
import { ChatRoomAvatarComponent } from '../chat-room-avatar/chat-room-avatar.component';
import { ChatRoomComponent } from '../chat-room/chat-room.component';
import { ChatService } from '../chat.service';
import { InViewportDirective } from '../directive/in-viewport.directive';
import { CloseIconComponent } from '../icon/close-icon/close-icon.component';
import { CreateIconComponent } from '../icon/create-icon/create-icon.component';
import { GroupIconComponent } from '../icon/group-icon/group-icon.component';
import { LockIconComponent } from '../icon/lock-icon/lock-icon.component';
import { SearchIconComponent } from '../icon/search-icon/search-icon.component';
import { ChatClassify, ChatGroupAccount } from '../model';
import { ChatRoom } from '../model/chat-room';
import { DateLabelFormatPipe } from '../pipe/date-label-format.pipe';
import { DateTimeFormatPipe, DATE_TYPE } from '../pipe/date-time-format.pipe';
import { RoomTopicPipe } from '../pipe/room-topic.pipe';
import { ChatOverlayService } from '../service/chat-overlay.service';
import { getChatParticipantsWithoutUser } from '../util/chat-util';
import { IconComponent } from 'src/app/component/icon/icon.component';
import { AuthService } from 'src/app/services/auth.service';
import { ChatRoomsService } from 'src/app/services/chat/chat-rooms.service';
import { deepEqual, isEmptyOrNil } from 'src/app/utils';

@UntilDestroy()
@Component({
  selector: 'cbx-chat-list',
  templateUrl: './chat-list.component.html',
  styleUrls: ['./chat-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    NgIf,
    NgFor,
    NgTemplateOutlet,
    AsyncPipe,
    CreateIconComponent,
    SearchIconComponent,
    CloseIconComponent,
    IconComponent,
    ChatListMessageComponent,
    LockIconComponent,
    ScrollingModule,
    RoomTopicPipe,
    DateLabelFormatPipe,
    InViewportDirective,
    CdkMenuModule,
    GroupIconComponent,
    IconComponent,
    CdkOverlayOrigin,
    CdkConnectedOverlay,
    ChatRoomAvatarComponent,
  ],
  providers: [DateTimeFormatPipe],
})
export class ChatListComponent implements AfterViewInit, OnDestroy {
  readonly listTitleLabel$ = this.chatService.selectLabel('listTitle');
  readonly newRoomLabel$ = this.chatService.selectLabel('newRoom');

  readonly searchInput$ = new BehaviorSubject('');
  readonly searchInputFocused$ = new BehaviorSubject(false);
  readonly displaySearchInput$ = combineLatest([this.searchInput$, this.searchInputFocused$]).pipe(
    map(([input, focused]) => focused || input !== ''),
    distinctUntilChanged(),
  );

  readonly showUnreadOnly$ = new BehaviorSubject(false);

  readonly chatRooms$ = this.chatService.chatRooms$;

  readonly chatClassifies$ = this.chatService.chatClassifies$;

  readonly selectedChatClassify$ = this.chatService.selectedChatClassify$;

  readonly selectedChatClassifyValue$ = this.selectedChatClassify$.pipe(map((chatClassify) => chatClassify.label));

  userId: string;

  chatListTitleIsOpen = false;

  private readonly today = new Date();
  private readonly todayTimestamp = Date.parse(this.today.toLocaleDateString());
  private readonly currentYearTimestamp = Date.parse(this.today.getFullYear().toString());

  readonly selectableChatRoom$ = combineLatest([
    this.chatRooms$,
    this.selectedChatClassify$,
    this.showUnreadOnly$,
  ]).pipe(
    distinctUntilChanged(deepEqual),
    map(([chatRooms, classify, showUnreadOnly]) => {
      chatRooms?.forEach((element) => {
        const totalOtherParticipants = getChatParticipantsWithoutUser(element, this.userId);

        if (totalOtherParticipants.length === 1) {
          element.isSenderNameHidden = true;
        }
        if (element.favoriteUserIds) {
          const index = element.favoriteUserIds.indexOf(this.userId);
          if (index > -1) {
            element.userFavorite = true;
          } else {
            element.userFavorite = false;
          }
        } else {
          element.userFavorite = false;
        }
      });

      const flagFilterRooms = chatRooms?.filter((chatRoom) => {
        if (chatRoom.isAI) {
          return true;
        }
        if (chatRoom.favoriteUserIds) {
          if (chatRoom.userFavorite) {
            return true;
          }
        }
        return false;
      });

      if (flagFilterRooms) {
        flagFilterRooms.sort((a, b) => {
          if (a.isAI !== b.isAI) {
            return a.isAI ? -1 : 1;
          }
          return new Date(b.lastMessage?.created).getTime() - new Date(a.lastMessage?.created).getTime();
        });
      }

      const unflagFilterRooms = chatRooms?.filter((chatRoom) => {
        if (chatRoom.isAI) {
          return false;
        }
        if (chatRoom.favoriteUserIds) {
          if (!chatRoom.userFavorite) {
            return true;
          }
        }
        return false;
      });

      if (unflagFilterRooms) {
        unflagFilterRooms.sort(
          (a, b) => new Date(b.lastMessage?.created).getTime() - new Date(a.lastMessage?.created).getTime(),
        );
      }

      let sortingRooms: ChatRoom[];
      if (flagFilterRooms) {
        sortingRooms = flagFilterRooms.concat(unflagFilterRooms);
      } else {
        sortingRooms = unflagFilterRooms;
      }

      let newChatRooms: ChatRoom[];
      if (sortingRooms) {
        newChatRooms = sortingRooms;
      } else {
        newChatRooms = chatRooms;
      }

      const readFilterRooms = newChatRooms?.filter((chatRoom) => {
        if (showUnreadOnly) {
          return chatRoom.lastReadMessageId !== chatRoom.lastMessage?.id || chatRoom.unread > 0;
        }
        return true;
      });
      const classifiedChatRooms = readFilterRooms?.filter(classify.classify)?.filter((room) => !room?.privateUserLeft);
      this.currentClass$.next(classify);

      return classifiedChatRooms;
    }),
    shareReplay(1),
  );

  filterByQuery(chatRoom: ChatRoom, queryString: string): boolean {
    const { topic, participants, groupIds, partyIds } = chatRoom;

    const participantList = participants?.map(({ username }) => username?.toLowerCase());
    const groupNameAndUserList = this.transformGroupIds(groupIds);
    const partyIdList = partyIds?.map(({ name }) => name?.toLowerCase());

    return [topic.toLowerCase(), ...participantList, ...groupNameAndUserList, ...partyIdList]
      .join(', ')
      .includes(queryString);
  }

  private transformGroupIds(groupIds: ChatGroupAccount[]) {
    return groupIds?.flatMap((group) => {
      const groupUsers = isEmptyOrNil(group.users) ? [] : group.users.map((user) => user?.username?.toLowerCase());
      return [group?.name.toLowerCase(), ...groupUsers];
    });
  }

  readonly noRooms$ = this.selectableChatRoom$.pipe(
    skip(1),
    map((chatRooms) => isEmptyOrNil(chatRooms)),
  );

  readonly loadingInViewport$ = new ReplaySubject<boolean>(1);

  readonly hasMoreRooms$ = this.chatService.hasMoreRooms$;

  readonly targetRoom$ = this.chatService.targetRoom$;

  readonly currentClass$ = new BehaviorSubject<ChatClassify>({
    label: 'All',
    classify: (room) => room.isParticipant,
  });

  private readonly destroy$ = new Subject<void>();

  dateFormat$ = this.authService.dateFormat$;
  dateType = DATE_TYPE.datetime;

  chatRoomSettingList = [
    {
      label: 'Mark as Unread',
      action: 'markAsUnread',
    },
    {
      label: 'Mark as Read',
      action: 'markAsRead',
    },
    {
      label: 'Pin Chat',
      action: 'pinChat',
    },
    {
      label: 'Unpin Chat',
      action: 'unpinChat',
    },
    {
      label: 'Leave Group',
      action: 'leaveGroup',
    },
  ];

  roomTrackBy = (index: number, room: ChatRoom) => room?.id;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly overlay: Overlay,
    private readonly host: ElementRef,
    private readonly chatService: ChatService,
    private readonly chatRoomsService: ChatRoomsService,
    private readonly chatOverlayService: ChatOverlayService,
    private readonly authService: AuthService,
    private readonly dateTimeFormatPipe: DateTimeFormatPipe,
  ) {
    // eslint-disable-next-line no-return-assign
    this.authService.userId$.pipe(untilDestroyed(this)).subscribe((userId) => (this.userId = userId));
  }

  ngAfterViewInit() {
    this.chatService.chatListComponent = this;

    setTimeout(() => {
      this.inLoadingViewport(null, true);
    }, 10);

    this.loadingInViewport$
      .pipe(
        distinctUntilChanged(),
        switchMap((inViewport) => (inViewport ? interval(3000).pipe(startWith(-1)) : NEVER)),
        takeUntil(this.destroy$),
      )
      .subscribe(() => this.inLoadingViewport(null, false));

    this.searchInput$
      .pipe(skip(1), debounceTime(1000), takeUntil(this.destroy$))
      .subscribe((searchInput) => this.inLoadingViewport(searchInput, true));
  }

  private inLoadingViewport(q: string, searchInputChange: boolean) {
    this.chatService.actionBus$.next({
      type: 'inLoadingViewport',
      value: this.currentClass$.getValue(),
      tag: 'rooms',
      q,
      searchInputChange,
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  close() {
    this.chatOverlayService.closeAllOverlays();
    this.chatOverlayService.isOpen = false;
  }

  changeChatClassify(chatClassify: ChatClassify) {
    this.selectedChatClassify$.pipe(take(1)).subscribe((selectedChatClassify) => {
      if (selectedChatClassify === chatClassify) {
        return;
      }
      this.chatService.actionBus$.next({ type: 'chatClassifyChange', value: chatClassify, tag: 'rooms' });
    });
  }

  createRoom() {
    const createRoomPortal = new ComponentPortal(ChatCreateRoomComponent);
    const overlayRef = this.attachOverlay(createRoomPortal);
    this.chatOverlayService.updateCreateRoomOverlay(overlayRef);
  }

  showUnreadOnly() {
    const currentValue = this.showUnreadOnly$.value;
    this.showUnreadOnly$.next(!currentValue);
  }

  openRoom(room: ChatRoom) {
    const chatRoomPortal = new ComponentPortal(ChatRoomComponent);
    const overlayRef = this.attachOverlay(chatRoomPortal);
    this.chatOverlayService.updateRoomOverlay(overlayRef, room);
  }

  private attachOverlay(componentPortal: ComponentPortal<ChatRoomComponent | ChatCreateRoomComponent>): OverlayRef {
    const overlayRef = this.overlay.create({
      width: '352px',
      height: 'calc(100vh - 130px)',
      positionStrategy: this.overlay
        .position()
        .flexibleConnectedTo(this.host)
        .withPositions([
          {
            originX: 'start',
            originY: 'top',
            overlayX: 'end',
            overlayY: 'top',
            offsetX: -8,
          },
          {
            originX: 'start',
            originY: 'top',
            overlayX: 'start',
            overlayY: 'top',
          },
        ]),
    });

    overlayRef.attach(componentPortal);
    return overlayRef;
  }

  toggleFavorite(room: ChatRoom) {
    this.chatRoomsService.toggleFavorite(room);
  }

  handleSettingActionExistCondition(action: string, room: ChatRoom): boolean {
    return this.chatRoomsService.handleSettingActionExistCondition(action, room);
  }

  handleSettingAction(action: string, room: ChatRoom) {
    return this.chatRoomsService.handleSettingAction(action, room);
  }
  formatCreatedDate(chatRoom: ChatRoom) {
    const createdDateTimestamp = Date.parse((chatRoom.lastMessage.created ?? chatRoom.created).toLocaleString());

    if (createdDateTimestamp > this.todayTimestamp) {
      return this.dateTimeFormatPipe.transform(createdDateTimestamp, 'datetime', 'HH:mm');
    }

    if (createdDateTimestamp > this.currentYearTimestamp) {
      return this.dateTimeFormatPipe.transform(createdDateTimestamp, 'datetime', 'MM/dd');
    }

    return this.dateTimeFormatPipe.transform(createdDateTimestamp, 'datetime', 'yyyy/MM');
  }

  overlayKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      this.chatListTitleIsOpen = false;
    }
  }

  clickOutsideMenu(event: MouseEvent, menuTrigger: CdkOverlayOrigin) {
    const element = menuTrigger.elementRef.nativeElement as HTMLElement;
    if (element.contains(event.target as Node)) {
      return;
    }
    this.chatListTitleIsOpen = false;
    this.cdr.markForCheck();
  }
}
