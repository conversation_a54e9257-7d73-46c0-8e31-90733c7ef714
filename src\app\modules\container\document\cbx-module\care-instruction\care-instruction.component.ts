import { ChangeDetectionStrategy, Component } from '@angular/core';

import { DocumentComponentStrategy } from '../../../document-switch/document-component-strategy';
import { GridActionDispatcherService } from '../../grid-section-content/service/grid-action-dispatcher.service';
import { CellMapperModuleToken } from '../../service/cell-mapper-constant';
import { CellMapperService } from '../../service/cell-mapper.service';
import { DocumentDropdownService } from '../../service/document-dropdown.service';
import { DocumentDynamicDataService } from '../../service/document-dynamic-data.service';
import { DocumentDynamicFieldService } from '../../service/document-dynamic-field.service';
import { DocumentFacadeService } from '../../service/document-facade.service';
import { DocumentFieldReadonlyService } from '../../service/document-field-readonly.service';
import { FieldMapperModuleToken } from '../../service/field-mapper-constant';
import { FieldMapperService } from '../../service/field-mapper.service';
import { CareInstructionCellMapperService } from './service/care-instruction-cell-mapper.service';
import { CareInstructionDropdownService } from './service/care-instruction-dropdown.service';
import { CareInstructionDynamicDataService } from './service/care-instruction-dynamic-data.service';
import { CareInstructionDynamicFieldService } from './service/care-instruction-dynamic-field.service';
import { CareInstructionFieldMapperService } from './service/care-instruction-field-mapper.service';
import { CareInstructionFieldReadonlyService } from './service/care-instruction-field-readonly.service';
import { CareInstructionGridActionDispatcherService } from './service/care-instruction-grid-action-dispatcher.service';

@Component({
  selector: 'app-communication-doc',
  template: '',
  standalone: true,

  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    DocumentFacadeService,
    {
      provide: DocumentDynamicDataService,
      useClass: CareInstructionDynamicDataService,
    },
    {
      provide: DocumentDynamicFieldService,
      useClass: CareInstructionDynamicFieldService,
    },
    {
      provide: DocumentDropdownService,
      useClass: CareInstructionDropdownService,
    },
    {
      provide: DocumentFieldReadonlyService,
      useClass: CareInstructionFieldReadonlyService,
    },
    CellMapperService,
    {
      provide: CellMapperModuleToken,
      useClass: CareInstructionCellMapperService,
    },
    FieldMapperService,
    {
      provide: FieldMapperModuleToken,
      useClass: CareInstructionFieldMapperService,
    },
    {
      provide: GridActionDispatcherService,
      useClass: CareInstructionGridActionDispatcherService,
    },
  ],
})
export default class CareInstructionComponent extends DocumentComponentStrategy {
  constructor(private readonly documentFacadeService: DocumentFacadeService) {
    super();
    this.documentFacadeService.registerBusinessLogic();
  }
}
