:host ::ng-deep {
  &.ag-cell-wrapper {
    align-items: center !important;
  }

  &.ag-cell-value {
    align-content: center;
    height: 16px;
  }
}

.tab-wrapper {
  height: 30px;
  margin: 16px 16px 0;
  border-bottom: 2px solid transparent;
}

.tab {
  font-size: 14px;
  letter-spacing: 0.19px;
  color: var(--color-primary-gray);
  cursor: pointer;
  user-select: none;
  padding: 0 16px;
  border: 0;
  background-color: transparent;
  border-bottom: 2px solid transparent;
  margin-bottom: -2px;
  border-radius: 3px 3px 0 0;

  &:focus,
  &:active {
    background-color: rgba($color: var(--legacy-primary-blue-01), $alpha: 0.1);
  }

  &:hover {
    background-color: rgba($color: var(--legacy-primary-blue-01), $alpha: 0.08);
  }

  &.tab-active {
    font-weight: 500;
    border-bottom-color: var(--legacy-primary-blue-01);
  }
}

.table-wrapper {
  height: calc(100% - 110px);
}
