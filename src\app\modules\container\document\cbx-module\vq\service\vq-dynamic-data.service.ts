import { Injectable } from '@angular/core';

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import * as R from 'ramda';
import { isNil } from 'ramda';
import { combineLatest, merge, Observable, of } from 'rxjs';
import { auditTime, distinctUntilChanged, filter, map, shareReplay, startWith, switchMap } from 'rxjs/operators';

import { AnyObject } from '../../../../../../interface/model';
import {
  allHasValue,
  arrayMultiplyCalculator,
  cartesianProduct,
  deepEqual,
  divideCalculator,
  dotPathF,
  generateUUID,
  isEmptyOrNil,
  isNotEmptyOrNil,
  isNotNil,
  missingValue,
  multiplyCalculator,
} from '../../../../../../utils';
import { DataTransformService } from '../../../service/data-transform.service';
import { DocumentDynamicDataService } from '../../../service/document-dynamic-data.service';
import { DocumentDefineStore, DocumentStatus, DocumentStatusService } from '../../../state';
import {
  ItemColorList,
  ItemSizeList,
  UnitCostBySubItemList,
  VqAdditionalCostList,
  VqCartonList,
  VqComponentCostList,
  VqCostSummaryList,
  VqOtherChargeList,
  vqSpecFormulationList,
  VqUnitCostDetailList,
} from '../model/vq-dto';
import { Vq_VqShareFileDto } from 'src/app/entities/api';
import { CodelistItem } from 'src/app/entities/codelist';
import { FieldDefine } from 'src/app/entities/form-field';
import { FormFieldTypes } from 'src/app/entities/form-field-types';
import { DocumentGridCellValueChange } from 'src/app/interface/model/web-socket-frame';
import { DocumentService } from 'src/app/modules/services/document.service';
import { DomainAttributeService } from 'src/app/workspace/service/domain-attribute.service';

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class VqDynamicDataService extends DocumentDynamicDataService {
  filterDataFunctionMap = {
    vqShareFileList: (data: Vq_VqShareFileDto[]) => this.filterActiveShareFile(data),
  };

  dataInitialized$ = this.documentDataQuery
    .selectDataInitialized$()
    .pipe(filter((dataInitialized) => !!dataInitialized));

  docEditing$ = this.documentStatusService.getDocumentStatus$().pipe(
    filter((status) => status === DocumentStatus.Edit),
    untilDestroyed(this),
    shareReplay(1),
  );

  customizeAfterGirdValueChangeActionMap = {
    vqUnitCostDetailList: {
      unitsPerInner: this.unitsPerInner.bind(this),
      innersPerOuter: this.innersPerOuter.bind(this),
      outerCartonL: this.outerCartonL.bind(this),
      outerCartonW: this.outerCartonW.bind(this),
      outerCartonH: this.outerCartonH.bind(this),
    },
  };

  // unitCostDetailList
  private unitsPerInner(sectionId: string, currentRowData: VqUnitCostDetailList, rowIndex: number) {
    const result = this.calculateUnitCostDetailList(currentRowData);
    const { unitsPerInner, unitsPerOuter, unitsPerCbm, unitsPerCFT, outerCartonCbm, outerCartonCFT } = result;

    this.documentDataService.updateCellValueByRowId(
      sectionId,
      currentRowData,
      { id: 'unitsPerInner', type: FormFieldTypes.number } as FieldDefine,
      unitsPerInner,
    );
    this.updateUnitCostDetailList(
      sectionId,
      currentRowData,
      rowIndex,
      unitsPerOuter,
      unitsPerCbm,
      unitsPerCFT,
      outerCartonCbm,
      outerCartonCFT,
    );
  }

  private innersPerOuter(sectionId: string, currentRowData: VqUnitCostDetailList, rowIndex: number) {
    const result = this.calculateUnitCostDetailList(currentRowData);
    const { innersPerOuter, unitsPerOuter, unitsPerCbm, unitsPerCFT, outerCartonCbm, outerCartonCFT } = result;

    this.documentDataService.updateCellValueByRowId(
      sectionId,
      currentRowData,
      { id: 'innersPerOuter', type: FormFieldTypes.number } as FieldDefine,
      innersPerOuter,
    );
    this.updateUnitCostDetailList(
      sectionId,
      currentRowData,
      rowIndex,
      unitsPerOuter,
      unitsPerCbm,
      unitsPerCFT,
      outerCartonCbm,
      outerCartonCFT,
    );
  }

  private outerCartonL(sectionId: string, currentRowData: VqUnitCostDetailList, rowIndex: number) {
    const result = this.calculateUnitCostDetailList(currentRowData);
    const { outerCartonL, unitsPerOuter, unitsPerCbm, unitsPerCFT, outerCartonCbm, outerCartonCFT } = result;

    this.documentDataService.updateCellValueByRowId(
      sectionId,
      currentRowData,
      { id: 'outerCartonL', type: FormFieldTypes.number } as FieldDefine,
      outerCartonL,
    );
    this.updateUnitCostDetailList(
      sectionId,
      currentRowData,
      rowIndex,
      unitsPerOuter,
      unitsPerCbm,
      unitsPerCFT,
      outerCartonCbm,
      outerCartonCFT,
    );
  }

  private outerCartonW(sectionId: string, currentRowData: VqUnitCostDetailList, rowIndex: number) {
    const result = this.calculateUnitCostDetailList(currentRowData);
    const { outerCartonW, unitsPerOuter, unitsPerCbm, unitsPerCFT, outerCartonCbm, outerCartonCFT } = result;

    this.documentDataService.updateCellValueByRowId(
      sectionId,
      currentRowData,
      { id: 'outerCartonW', type: FormFieldTypes.number } as FieldDefine,
      outerCartonW,
    );
    this.updateUnitCostDetailList(
      sectionId,
      currentRowData,
      rowIndex,
      unitsPerOuter,
      unitsPerCbm,
      unitsPerCFT,
      outerCartonCbm,
      outerCartonCFT,
    );
  }

  private outerCartonH(sectionId: string, currentRowData: VqUnitCostDetailList, rowIndex: number) {
    const result = this.calculateUnitCostDetailList(currentRowData);
    const { outerCartonH, unitsPerOuter, unitsPerCbm, unitsPerCFT, outerCartonCbm, outerCartonCFT } = result;

    this.documentDataService.updateCellValueByRowId(
      sectionId,
      currentRowData,
      { id: 'outerCartonH', type: FormFieldTypes.number } as FieldDefine,
      outerCartonH,
    );
    this.updateUnitCostDetailList(
      sectionId,
      currentRowData,
      rowIndex,
      unitsPerOuter,
      unitsPerCbm,
      unitsPerCFT,
      outerCartonCbm,
      outerCartonCFT,
    );
  }

  private updateUnitCostDetailList(
    sectionId: string,
    currentRowData: VqUnitCostDetailList,
    rowIndex: number,
    unitsPerOuter: number,
    unitsPerCbm: number,
    unitsPerCFT: number,
    outerCartonCbm: number,
    outerCartonCFT: number,
  ) {
    // unitsPerOuter
    this.documentDataService.dynamicRowCellChanges$.next({
      changes: [
        {
          sectionId,
          rowId: currentRowData.id,
          path: 'unitsPerOuter',
          content: unitsPerOuter,
          fieldId: 'unitsPerOuter',
          rowIndex,
        },
      ],
    });
    // unitsPerCbm
    this.documentDataService.dynamicRowCellChanges$.next({
      changes: [
        {
          sectionId,
          rowId: currentRowData.id,
          path: 'unitsPerCbm',
          content: unitsPerCbm,
          fieldId: 'unitsPerCbm',
          rowIndex,
        },
      ],
    });
    // unitsPerCFT
    this.documentDataService.dynamicRowCellChanges$.next({
      changes: [
        {
          sectionId,
          rowId: currentRowData.id,
          path: 'unitsPerCFT',
          content: unitsPerCFT,
          fieldId: 'unitsPerCFT',
          rowIndex,
        },
      ],
    });
    // outerCartonCFT
    this.documentDataService.dynamicRowCellChanges$.next({
      changes: [
        {
          sectionId,
          rowId: currentRowData.id,
          path: 'outerCartonCFT',
          content: outerCartonCFT,
          fieldId: 'outerCartonCFT',
          rowIndex,
        },
      ],
    });
    // outerCartonCbm
    this.documentDataService.dynamicRowCellChanges$.next({
      changes: [
        {
          sectionId,
          rowId: currentRowData.id,
          path: 'outerCartonCbm',
          content: outerCartonCbm,
          fieldId: 'outerCartonCbm',
          rowIndex,
        },
      ],
    });
    this.documentDataService.dynamicRowCellChanges$.next({
      changes: [
        {
          sectionId,
          rowId: currentRowData.id,
          path: 'unitsPerCbm',
          content: unitsPerCbm,
          fieldId: 'unitsPerCbm',
          rowIndex,
        },
      ],
    });
  }

  private calculateUnitCostDetailList(currentRowData: VqUnitCostDetailList) {
    const { unitsPerInner, innersPerOuter, outerCartonL, outerCartonW, outerCartonH } = currentRowData;
    const { breakdownDimensionUom } = this.documentDataQuery.getValue();

    const toMRate = this.dataTransformService.getUOMConversionRateValue(breakdownDimensionUom, 'M');
    const updatedOuterCartonCbm = allHasValue([outerCartonL, outerCartonW, outerCartonH])
      ? arrayMultiplyCalculator([outerCartonL, outerCartonW, outerCartonH], toMRate)
      : null;
    const toINCHRate = this.dataTransformService.getUOMConversionRateValue(breakdownDimensionUom, 'INCH');
    const updatedOuterCartonCFT = allHasValue([outerCartonL, outerCartonW, outerCartonH])
      ? divideCalculator(arrayMultiplyCalculator([outerCartonL, outerCartonW, outerCartonH], toINCHRate), 1728)
      : null;
    const unitsPerOuter = allHasValue([unitsPerInner, innersPerOuter])
      ? multiplyCalculator(unitsPerInner, innersPerOuter)
      : null;

    const unitsPerCbm =
      allHasValue([unitsPerOuter, updatedOuterCartonCbm]) && updatedOuterCartonCbm !== 0
        ? divideCalculator(unitsPerOuter, updatedOuterCartonCbm)
        : null;

    const unitsPerCFT =
      allHasValue([unitsPerOuter, updatedOuterCartonCFT]) && updatedOuterCartonCFT !== 0
        ? divideCalculator(unitsPerOuter, updatedOuterCartonCFT)
        : null;

    return {
      unitsPerInner,
      innersPerOuter,
      outerCartonL,
      outerCartonW,
      outerCartonH,
      unitsPerOuter,
      unitsPerCbm,
      unitsPerCFT,
      outerCartonCbm: updatedOuterCartonCbm,
      outerCartonCFT: updatedOuterCartonCFT,
    };
  }

  vqUnitCostDetailList$ = this.documentDataQuery
    .select('breakdownDimensionUom')
    .pipe(distinctUntilChanged(deepEqual))
    .pipe(
      auditTime(0),
      map((breakdownDimensionUom) => {
        const { vqUnitCostDetailList } = this.documentDataQuery.getValue();
        if (isNotEmptyOrNil(vqUnitCostDetailList)) {
          vqUnitCostDetailList?.map((unitCostDetailRow) => {
            const { unitsPerInner, innersPerOuter, outerCartonL, outerCartonW, outerCartonH } = unitCostDetailRow;

            const toMRate = this.dataTransformService.getUOMConversionRateValue(breakdownDimensionUom, 'M');
            const updatedOuterCartonCbm = allHasValue([outerCartonL, outerCartonW, outerCartonH])
              ? arrayMultiplyCalculator([outerCartonL, outerCartonW, outerCartonH], toMRate)
              : null;
            const toINCHRate = this.dataTransformService.getUOMConversionRateValue(breakdownDimensionUom, 'INCH');
            const updatedOuterCartonCFT = allHasValue([outerCartonL, outerCartonW, outerCartonH])
              ? divideCalculator(arrayMultiplyCalculator([outerCartonL, outerCartonW, outerCartonH], toINCHRate), 1728)
              : null;
            const unitsPerOuter = allHasValue([unitsPerInner, innersPerOuter])
              ? multiplyCalculator(unitsPerInner, innersPerOuter)
              : null;

            const unitsPerCbm =
              allHasValue([unitsPerOuter, updatedOuterCartonCbm]) && updatedOuterCartonCbm !== 0
                ? divideCalculator(unitsPerOuter, updatedOuterCartonCbm)
                : null;

            const unitsPerCFT =
              allHasValue([unitsPerOuter, updatedOuterCartonCFT]) && updatedOuterCartonCFT !== 0
                ? divideCalculator(unitsPerOuter, updatedOuterCartonCFT)
                : null;

            return {
              ...unitCostDetailRow,
              unitsPerOuter,
              unitsPerCbm,
              unitsPerCFT,
              outerCartonCbm: updatedOuterCartonCbm,
              outerCartonCFT: updatedOuterCartonCFT,
            };
          });
        }
      }),
    );

  componentCostList$ = this.documentDataQuery.select('vqComponentCostList') as Observable<VqComponentCostList[]>;
  additionalCostList$ = this.documentDataQuery.select('vqAdditionalCostList') as Observable<VqAdditionalCostList[]>;
  specFormulationList$ = this.documentDataQuery.select('vqSpecFormulationList') as Observable<vqSpecFormulationList[]>;
  unitCostBySubItemList$ = this.documentDataQuery.select('unitCostBySubItemList') as Observable<
    UnitCostBySubItemList[]
  >;
  cartonList$ = this.documentDataQuery.select('vqCartonList') as Observable<VqCartonList[]>;
  otherChargeList$ = this.documentDataQuery.select('vqOtherChargeList') as Observable<VqOtherChargeList[]>;
  currency$ = this.documentDataQuery.select('currency') as Observable<CodelistItem>;

  // currency
  vqCurrency$ = this.currency$.pipe(
    filter(() => this.documentDataService.getDataChangeByUser(['currency'])),
    filter(isNotEmptyOrNil),
    distinctUntilChanged(deepEqual),
    map((currency) => currency || null),
  );

  // cartonSection
  unitsPerOuter$ = this.documentDataQuery.select(['unitsPerInner', 'innersPerOuter']).pipe(
    filter(() => this.documentDataService.getDataChangesByUser([['unitsPerInner'], ['innersPerOuter']])),
    auditTime(0),
    distinctUntilChanged(deepEqual),
    filter(({ unitsPerInner, innersPerOuter }) => allHasValue([unitsPerInner, innersPerOuter])),
    map(({ unitsPerInner, innersPerOuter }) => multiplyCalculator(unitsPerInner, innersPerOuter)),
    untilDestroyed(this),
    shareReplay(1),
  );

  outerCartonCbm$ = this.cartonList$.pipe(
    filter(() => this.documentDataService.getDataChangeByUser(['vqCartonList'])),
    auditTime(0),
    distinctUntilChanged(deepEqual),
    map((cartonList: VqCartonList[]) =>
      isEmptyOrNil(cartonList) ? [] : cartonList.filter((carton: VqCartonList) => carton?.cartonType?.code === 'OUTER'),
    ),
    map((cartonList: VqCartonList[]) => this.dataTransformService.sumInGrid(cartonList, 'cartonCbm') || 0),
    untilDestroyed(this),
    shareReplay(1),
  );

  outerCartonCFT$ = this.cartonList$.pipe(
    filter(() => this.documentDataService.getDataChangeByUser(['vqCartonList'])),
    auditTime(0),
    distinctUntilChanged(deepEqual),
    map((cartonList: VqCartonList[]) =>
      cartonList?.filter((carton: VqCartonList) => carton?.cartonType?.code === 'OUTER'),
    ),
    map((cartonList) => this.dataTransformService.sumInGrid(cartonList, 'cartonCFT') || 0),
    untilDestroyed(this),
    shareReplay(1),
  );

  unitsPerCbm$ = combineLatest([this.unitsPerOuter$, this.outerCartonCbm$]).pipe(
    filter(() => this.documentDataService.getDataChangesByUser([['unitsPerOuter'], ['outerCartonCbm']])),
    auditTime(0),
    distinctUntilChanged(deepEqual),
    filter(([unitsPerOuter, outerCartonCbm]) => allHasValue([unitsPerOuter, outerCartonCbm]) && outerCartonCbm !== 0),
    map(([unitsPerOuter, outerCartonCbm]) => divideCalculator(unitsPerOuter, outerCartonCbm)),
  );

  unitsPerCFT$ = combineLatest([this.unitsPerOuter$, this.outerCartonCFT$]).pipe(
    filter(() => this.documentDataService.getDataChangesByUser([['unitsPerOuter'], ['outerCartonCFT']])),
    auditTime(0),
    distinctUntilChanged(deepEqual),
    filter(([unitsPerOuter, outerCartonCFT]) => allHasValue([unitsPerOuter, outerCartonCFT]) && outerCartonCFT !== 0),
    map(([unitsPerOuter, outerCartonCFT]) => divideCalculator(unitsPerOuter, outerCartonCFT)),
  );

  // costBreakdownSummary
  totalComponentCosts$ = this.componentCostList$.pipe(
    filter(() => this.documentDataService.getDataChangeByUser(['vqComponentCostList'])),
    auditTime(0),
    distinctUntilChanged(deepEqual),
    map((componentCostList) => this.dataTransformService.sumInGrid(componentCostList, 'calculatedCost') || 0),
    untilDestroyed(this),
    shareReplay(1),
  );

  totalAdditionalCosts$ = this.additionalCostList$.pipe(
    filter(() => this.documentDataService.getDataChangeByUser(['vqAdditionalCostList'])),
    auditTime(0),
    distinctUntilChanged(deepEqual),
    map((additionalCostList) => this.dataTransformService.sumInGrid(additionalCostList, 'calculatedCost') || 0),
    untilDestroyed(this),
    shareReplay(1),
  );

  totalUsed$ = this.specFormulationList$.pipe(
    filter(() => this.documentDataService.getDataChangeByUser(['vqSpecFormulationList'])),
    auditTime(0),
    distinctUntilChanged(deepEqual),
    map((formulationList) => this.dataTransformService.sumInGrid(formulationList, 'used') || 0),
  );

  totalUnitCost$ = this.specFormulationList$.pipe(
    filter(() => this.documentDataService.getDataChangeByUser(['vqSpecFormulationList'])),
    auditTime(0),
    distinctUntilChanged(deepEqual),
    map((specFormulationList) => {
      if (isEmptyOrNil(specFormulationList)) {
        return 0;
      }

      let total = 0;
      Object.keys(specFormulationList).forEach((i) => {
        const { treePath } = specFormulationList[i];
        let hasChild = false;
        Object.keys(specFormulationList).forEach((j) => {
          const newPath = specFormulationList[j].treePath;
          if (newPath !== treePath && newPath?.indexOf(treePath) !== -1 && treePath !== '') {
            hasChild = true;
          }
          return null;
        });
        if (!hasChild) {
          total += specFormulationList[i].formulaCosts;
        }
        return null;
      });

      return total;
    }),
    untilDestroyed(this),
    shareReplay(1),
  );

  costBreakdownTotalCost$: Observable<number> = combineLatest([
    this.totalComponentCosts$,
    this.totalAdditionalCosts$,
    this.totalUnitCost$,
  ]).pipe(
    filter(() =>
      this.documentDataService.getDataChangesByUser([
        ['totalComponentCosts'],
        ['totalAdditionalCosts'],
        ['vqSpecFormulationList'],
      ]),
    ),
    auditTime(0),
    distinctUntilChanged(deepEqual),
    map(
      ([totalComponentCosts, totalAdditionalCosts, totalUnitCost]) =>
        (totalComponentCosts ?? 0) + (totalAdditionalCosts ?? 0) + (totalUnitCost ?? 0),
    ),
    untilDestroyed(this),
    shareReplay(1),
  );

  // costingSection
  unitCost$ = this.documentDataQuery.select(['unitCost', 'itemType', 'openCosting']).pipe(
    auditTime(0),
    switchMap((data) => {
      const unitCost: number = data?.unitCost ?? 0;
      const isSet: boolean = data?.itemType?.code === 'SET';
      const openCosting: boolean = data?.openCosting;

      if (!openCosting && isSet) {
        return this.unitCostBySubItemList$.pipe(
          startWith<UnitCostBySubItemList[]>([]),
          map((unitCostBySubItemList) => {
            const sum = this.dataTransformService.sumInGrid(unitCostBySubItemList, 'unitCostSubTotal');
            return unitCostBySubItemList?.some((record) => isNotEmptyOrNil(record?.unitCostSubTotal)) ? sum : unitCost;
          }),
          filter((value) => value !== undefined),
        );
      }

      if (openCosting) {
        return this.costBreakdownTotalCost$.pipe(
          startWith<number>(null as number),
          map((costBreakdownTotalCost) => (isNotNil(costBreakdownTotalCost) ? costBreakdownTotalCost : unitCost)),
          filter((value) => value !== undefined),
        );
      }

      return of(unitCost);
    }),
    distinctUntilChanged(deepEqual),
    untilDestroyed(this),
    shareReplay(1),
  );

  totalOtherCharges$ = this.otherChargeList$.pipe(
    filter(() => this.documentDataService.getDataChangeByUser(['vqOtherChargeList'])),
    auditTime(0),
    distinctUntilChanged(deepEqual),
    map((otherChargeList) => this.dataTransformService.sumInGrid(otherChargeList, 'calculatedCost') || 0),
    untilDestroyed(this),
    shareReplay(1),
  );

  totalFormulaCost$ = this.totalUnitCost$;

  totalCost$ = combineLatest([this.documentDataQuery.select('unitCost'), this.totalOtherCharges$]).pipe(
    auditTime(0),
    distinctUntilChanged(deepEqual),
    map(([unitCost, totalOtherCharges]) => unitCost + totalOtherCharges),
  );

  // vqComponentCostList
  vqComponentCostList$ = this.componentCostList$.pipe(
    filter(() => this.documentDataService.getDataChangeByUser(['vqComponentCostList'])),
    auditTime(0),
    filter(isNotEmptyOrNil),
    distinctUntilChanged(deepEqual),
    map((componentCostList: VqComponentCostList[]) =>
      componentCostList?.map((componentCostRow) => {
        const { consumption, wastage, unitCost, currency, quotationCurrency } = componentCostRow;

        const consumptionCost = allHasValue([consumption, wastage, unitCost])
          ? multiplyCalculator(multiplyCalculator(consumption, 1 + divideCalculator(wastage, 100)), unitCost)
          : null;

        const calculatedCost = allHasValue([consumptionCost, currency, quotationCurrency])
          ? multiplyCalculator(
              consumptionCost,
              this.dataTransformService.getExchangeRates(currency?.code, quotationCurrency?.code),
            )
          : null;

        return { ...componentCostRow, consumptionCost, calculatedCost };
      }),
    ),
  );

  componentCostQuotationCurrency$ = this.currency$.pipe(
    filter(() => this.documentDataService.getDataChangesByUser([['currency'], ['vqComponentCostList']])),
    distinctUntilChanged(deepEqual),
    switchMap((currency) =>
      this.componentCostList$.pipe(
        filter(isNotEmptyOrNil),
        distinctUntilChanged(deepEqual),
        map(
          (componentCostList: VqComponentCostList[]) =>
            componentCostList?.map((componentCostRow) => ({
              ...componentCostRow,
              quotationCurrency: currency,
            })) || null,
        ),
      ),
    ),
  );

  // vqAdditionalCostList
  vqAdditionalCostList$ = this.additionalCostList$.pipe(
    filter(() => this.documentDataService.getDataChangeByUser(['vqAdditionalCostList'])),
    auditTime(0),
    filter(isNotEmptyOrNil),
    distinctUntilChanged(deepEqual),
    map((additionalCostList: VqAdditionalCostList[]) =>
      additionalCostList?.map((additionalCostRow) => {
        const { consumption, wastage, unitCost } = additionalCostRow;

        const consumptionCost = allHasValue([consumption, wastage, unitCost])
          ? multiplyCalculator(multiplyCalculator(consumption, 1 + divideCalculator(wastage, 100)), unitCost)
          : null;

        // const calculatedCost = allHasValue([consumptionCost, currency, quotationCurrency])
        //   ? multiplyCalculator(
        //       consumptionCost,
        //       this.dataTransformService.getExchangeRates(currency.code, quotationCurrency.code),
        //     )
        //   : null;

        return { ...additionalCostRow, consumptionCost };
      }),
    ),
  );

  // vqSpecFormulationList
  vqSpecFormulationList$ = this.specFormulationList$.pipe(
    filter(() => this.documentDataService.getDataChangeByUser(['vqSpecFormulationList'])),
    auditTime(0),
    filter(isNotEmptyOrNil),
    distinctUntilChanged(deepEqual),
    map((specFormulationList: vqSpecFormulationList[]) =>
      specFormulationList?.map((specFormulationRow) => {
        const { used, unitCost } = specFormulationRow;

        const formulaCosts = allHasValue([used, unitCost])
          ? multiplyCalculator(divideCalculator(used, 100), unitCost)
          : null;

        return { ...specFormulationRow, formulaCosts };
      }),
    ),
  );

  additionalCostQuotationCurrency$ = this.currency$.pipe(
    filter(() => this.documentDataService.getDataChangesByUser([['currency'], ['vqAdditionalCostList']])),
    distinctUntilChanged(deepEqual),
    switchMap((currency) =>
      this.additionalCostList$.pipe(
        filter(isNotEmptyOrNil),
        distinctUntilChanged(deepEqual),
        map((additionalCostList: VqAdditionalCostList[]) =>
          additionalCostList?.map((vqAdditionalCostRow) => ({
            ...vqAdditionalCostRow,
            quotationCurrency: currency,
          })),
        ) || null,
      ),
    ),
  );

  // vqOtherChargeList
  calculatedCost$: Observable<number> = combineLatest([
    this.otherChargeList$,
    this.documentDataQuery.select('unitCost'),
  ]).pipe(
    auditTime(0),
    filter(isNotEmptyOrNil),
    distinctUntilChanged(deepEqual),
    map(([otherChargeList, unitCost]) =>
      otherChargeList?.map((otherChargeRow) => {
        const { basis, rate, currency, quotationCurrency } = otherChargeRow;
        if (missingValue([basis, quotationCurrency])) {
          return otherChargeRow;
        }

        let updateUtherChargeRow: { [prop: string]: number };
        if (basis.code === 'FIXED') {
          updateUtherChargeRow = {
            calculatedCost:
              currency && isNotEmptyOrNil(rate)
                ? multiplyCalculator(
                    rate,
                    this.dataTransformService.getExchangeRates(currency.code, quotationCurrency.code),
                  )
                : null,
          };
        } else {
          updateUtherChargeRow = {
            calculatedCost: isNotEmptyOrNil(rate) ? divideCalculator(multiplyCalculator(unitCost, rate), 100) : null,
            currency: null,
          };
        }
        return { ...otherChargeRow, ...updateUtherChargeRow };
      }),
    ),
  );

  // unitCostBySubItemList
  quotationCurrency$ = combineLatest([this.currency$, this.otherChargeList$]).pipe(
    filter(isNotEmptyOrNil),
    distinctUntilChanged(deepEqual),
    map(([currency, otherChargeList]) =>
      otherChargeList?.map((otherChargeRow) => ({ ...otherChargeRow, quotationCurrency: currency } || null)),
    ),
  );

  // unitCostBySubItemList
  unitCostSubTotal$ = this.unitCostBySubItemList$.pipe(
    filter(() => this.documentDataService.getDataChangeByUser(['unitCostBySubItemList'])),
    auditTime(0),
    filter(isNotEmptyOrNil),
    distinctUntilChanged(deepEqual),
    map((unitCostBySubItemList: UnitCostBySubItemList[]) =>
      unitCostBySubItemList?.map((unitCostBySubItemRow) => {
        const { subItemQuantity, unitCostBreakdown } = unitCostBySubItemRow;
        const unitCostSubTotal = allHasValue([subItemQuantity, unitCostBreakdown])
          ? multiplyCalculator(subItemQuantity, unitCostBreakdown)
          : null;

        return { ...unitCostBySubItemRow, unitCostSubTotal };
      }),
    ),
  );

  // vqCartonList
  vqCartonList$ = this.cartonList$.pipe(
    filter(() => this.documentDataService.getDataChangeByUser(['vqCartonList'])),
    auditTime(0),
    filter(isNotEmptyOrNil),
    distinctUntilChanged(deepEqual),
    map((cartonList: VqCartonList[]) =>
      cartonList?.map((cartonRow) => {
        const { length, widht, height, dimensionUOM } = cartonRow;

        const toMRate = this.dataTransformService.getUOMConversionRateValue(dimensionUOM as CodelistItem, 'M');
        const cartonCbm = allHasValue([length, widht, height, dimensionUOM])
          ? arrayMultiplyCalculator([length, widht, height], toMRate)
          : null;

        const toINCHRate = this.dataTransformService.getUOMConversionRateValue(dimensionUOM as CodelistItem, 'INCH');
        const cartonCFT = allHasValue([length, widht, height, dimensionUOM])
          ? divideCalculator(arrayMultiplyCalculator([length, widht, height], toINCHRate), 1728)
          : null;

        return {
          id: cartonRow.id,
          cartonCbm,
          cartonCFT,
        };
      }),
    ),
  );

  constructor(
    private readonly documentService: DocumentService,
    private readonly dataTransformService: DataTransformService,
    private readonly documentStatusService: DocumentStatusService,
    private readonly documentDefineStore: DocumentDefineStore,
    private readonly domainAttributeService: DomainAttributeService,
  ) {
    super();
  }

  isValueChange(originValue: any, newValue: any) {
    if (originValue === undefined) {
      return false;
    }
    if (originValue === newValue) {
      return false;
    }
    if (originValue == null || newValue == null) {
      return originValue !== newValue;
    }
    // if type change
    if (typeof originValue !== typeof newValue) {
      return true;
    }
    if (Array.isArray(originValue)) {
      return (
        !Array.isArray(newValue) ||
        originValue.length !== newValue.length ||
        originValue.some((child, i) => this.isValueChange(child, newValue[i]))
      );
    }
    if (typeof originValue === 'object') {
      const originKeys = Object.keys(originValue);
      const newKeys = Object.keys(newValue);
      return (
        originKeys.length !== newKeys.length ||
        originKeys.some((key) => this.isValueChange(originValue[key], newValue[key]))
      );
    }
    return originValue !== newValue;
  }

  isCEMode: boolean = this.domainAttributeService.enabledConcurrentModules.includes('vq');

  registerBusinessLogic() {
    const dataMap: AnyObject<Observable<any>> = {
      unitsPerOuter: this.unitsPerOuter$,
      unitsPerCbm: this.unitsPerCbm$,
      unitsPerCFT: this.unitsPerCFT$,
      outerCartonCbm: this.outerCartonCbm$,
      outerCartonCFT: this.outerCartonCFT$,
      totalComponentCosts: this.totalComponentCosts$,
      totalAdditionalCosts: this.totalAdditionalCosts$,
      costBreakdownTotalCost: this.costBreakdownTotalCost$,
      unitCost: this.unitCost$,
      totalOtherCharges: this.totalOtherCharges$,
      totalUsed: this.totalUsed$,
      totalUnitCost: this.totalUnitCost$,
      totalCost: this.totalCost$,
      totalFormulaCost: this.totalFormulaCost$,
      // currency: this.vqCurrency$,
      vqComponentCostList: merge(this.vqComponentCostList$, this.componentCostQuotationCurrency$),
      vqAdditionalCostList: merge(this.vqAdditionalCostList$, this.additionalCostQuotationCurrency$),
      vqOtherChargeList: merge(this.calculatedCost$, this.quotationCurrency$),
      unitCostBySubItemList: this.unitCostSubTotal$,
      vqSpecFormulationList: this.vqSpecFormulationList$,
    };
    Object.entries(dataMap).forEach(([fieldId, data$]) => {
      combineLatest([this.docEditing$, this.dataInitialized$])
        .pipe(
          switchMap(() => data$),
          auditTime(0),
          untilDestroyed(this),
        )
        .subscribe((data) => {
          if (this.isCEMode) {
            const vq = this.documentDataQuery.getValue();
            const originValue = vq[fieldId];
            if (this.isValueChange(originValue, data)) {
              this.documentDataService.updateData({ [fieldId]: data });
              this.documentDataService.dynamicValueChange$.next({
                fieldId,
                content: data,
              });
            }
          } else {
            this.documentDataService.updateData({ [fieldId]: data });
            this.documentDataService.dynamicValueChange$.next({
              fieldId,
              content: data,
            });
          }
        });
    });

    const rowDataMap: AnyObject<Observable<any>> = {
      vqUnitCostDetailList: this.vqUnitCostDetailList$,
      vqCartonList: this.vqCartonList$,
    };

    Object.entries(rowDataMap).forEach(([fieldId, data$]) => {
      combineLatest([this.docEditing$, this.dataInitialized$])
        .pipe(
          switchMap(() => data$),
          untilDestroyed(this),
        )
        .subscribe((data) => {
          if (Array.isArray(data)) {
            this.documentDataService.updateGridRows(fieldId, data);
            const changes: DocumentGridCellValueChange[] = [];
            if (this.isCEMode) {
              const vq = this.documentDataQuery.getValue();
              const originValue = vq[fieldId];
              data.forEach((value) => {
                Object.entries(value)
                  .filter(([key, content]) => key !== 'id' && content !== null)
                  .forEach(([key, content]) => {
                    if (isNotEmptyOrNil(originValue) && originValue.length > 0) {
                      originValue.forEach((origin) => {
                        if (origin.id === value.id && this.isValueChange(origin[key], content)) {
                          changes.push({
                            rowId: value.id,
                            content,
                            path: key,
                            fieldId: key,
                            rowIndex: this.documentDataQuery
                              .getValue()
                              [fieldId].findIndex((r: { id: string }) => r.id === value.id),
                            sectionId: fieldId,
                          });
                        }
                      });
                    }
                  });
              });
            } else {
              data.forEach((value) => {
                Object.entries(value)
                  .filter(([key, content]) => key !== 'id' && content !== null)
                  .forEach(([key, content]) => {
                    changes.push({
                      rowId: value.id,
                      content,
                      path: key,
                      fieldId: key,
                      rowIndex: this.documentDataQuery
                        .getValue()
                        [fieldId].findIndex((r: { id: string }) => r.id === value.id),
                      sectionId: fieldId,
                    });
                  });
              });
            }

            this.documentDataService.dynamicRowCellChanges$.next({ changes });
          }
        });
    });

    this.handleUnitCostBy();
    this.handleOtherRequirement();
    this.resetCostSheetSeq();
    this.handleShareFileList('vqShareFileList');
    this.handleHeaderVendorFact();
  }

  handleHeaderVendorFact() {
    combineLatest([this.documentDataQuery.select('vendor'), this.documentDataQuery.select('factory')])
      .pipe(
        filter(() => this.documentDataService.getDataChangesByUser([['vendor'], ['factory']])),
        auditTime(0),
        distinctUntilChanged(deepEqual),
        untilDestroyed(this),
      )
      .subscribe(([vendor]) => {
        if (isEmptyOrNil(vendor)) {
          const fieldId = 'factory';
          const content = null;
          this.documentDataService.updateData({ [fieldId]: content });
          this.documentDataService.dynamicValueChange$.next({ fieldId, content });
        }
      });
  }

  resetCostSheetSeq() {
    this.documentDataQuery
      .select('vqCostSummaryList')
      .pipe(
        filter(() => this.documentDataService.getDataChangeByUser(['vqCostSummaryList'])),
        auditTime(0),
        filter(() => this.documentStatusService.getDocumentStatus() === DocumentStatus.Edit),
        distinctUntilChanged(
          (before: VqCostSummaryList[], after: VqCostSummaryList[]) =>
            JSON.stringify(before?.map((a) => a.refNo)) === JSON.stringify(after?.map((b) => b.refNo)),
        ),
        untilDestroyed(this),
      )
      .subscribe((costSheetList: VqCostSummaryList[]) => {
        const vqCostSummaryList = costSheetList?.map((costSheet, index) => ({ ...costSheet, seqNo: index + 1 }));
        this.documentDataService.updateData({ vqCostSummaryList });
        this.documentDataService.dynamicRowChanges$.next({
          changes: vqCostSummaryList.map((row, rowIndex) => ({
            sectionId: 'vqCostSummaryList',
            changeType: 'update',
            rowId: row.id.toString(),
            rowIndex,
            content: row,
          })),
        });
      });
  }

  private transformItemSizeData(
    data$: Observable<any>,
    displayKey: string,
    subDisplayKey: string,
    uniqueKey: string,
    groupByKey: string,
  ) {
    const displayF = dotPathF(displayKey);
    const subDisplayKeyF = dotPathF(subDisplayKey);
    const uniqueKeyF = dotPathF(uniqueKey);
    const groupByKeyF = dotPathF(groupByKey);

    return data$.pipe(
      auditTime(0),
      map((data) => data || []),
      map((data) => data.map((row, index) => ({ index, lineNumber: index + 1, sizeSeq: row.sizeSeq, ...row }))),
      // Group by `groupByKey` and sort by sortByAfterGroupByValues seq.
      map((data: any) => R.groupBy(groupByKeyF)(data)),
      map(R.values),
      map((group: any) => {
        if (!group || group.length < 1) {
          return [];
        }

        // If data only one group return data in `B(b)` style.
        if (group.length === 1) {
          return R.map(({ index, lineNumber, sizeSeq, ...row }) => ({
            id: uniqueKey ? `${uniqueKeyF(row)}${lineNumber}` : `${lineNumber}`,
            sizeSeq,
            sizeDisplayName: subDisplayKeyF(row) ? `${displayF(row)}(${subDisplayKeyF(row)})` : `${displayF(row)}`,
          }))(group[0]);
        }

        // Otherwise return combined data in `22|B|in, 11|B|in` style, with id in `SIZE2-CUP1-INSEAM5` style.
        return R.pipe(
          R.map(
            R.map(({ index, lineNumber, ...row }) => ({
              sizeSeq: index,
              lineNumber,
              uniqueValue: uniqueKeyF(row),
              sizeDisplayName: displayF(row),
            })),
          ),
          cartesianProduct,
          R.map(
            R.pipe(
              R.reduce(
                ({ ids, sizeDisplayNames }, { lineNumber, uniqueValue, sizeDisplayName }) => ({
                  ids: [...ids, `${uniqueValue}${lineNumber}`],
                  sizeDisplayNames: [...sizeDisplayNames, sizeDisplayName],
                }),
                { ids: [], sizeDisplayNames: [] },
              ),
              ({ ids, sizeDisplayNames }) => ({ id: ids.join('-'), sizeDisplayName: sizeDisplayNames.join('|') }),
            ),
          ),
        )(group);
      }),
    );
  }

  private checkUnitCostBy(): Observable<boolean> {
    return this.documentDataQuery.select(['itemSizeList', 'itemColorList', 'isPlanQtyBySet']).pipe(
      filter(() =>
        this.documentDataService.getDataChangesByUser([['itemSizeList'], ['itemColorList'], ['isPlanQtyBySet']]),
      ),
      map(
        ({ itemSizeList, itemColorList, isPlanQtyBySet }) =>
          !(isNotEmptyOrNil(itemSizeList) && isNotEmptyOrNil(itemColorList) && !isPlanQtyBySet),
      ),
    );
  }

  private handleUnitCostBy() {
    this.checkUnitCostBy()
      .pipe(
        filter((shouldHandle) => !shouldHandle), // Only proceed if checkUnitCostBy() returns false
        switchMap(() =>
          this.documentDataQuery.select('unitCostBy').pipe(
            filter(() =>
              this.documentDataService.getDataChangesByUser([['unitCostBy'], ['itemColorList'], ['itemSizeList']]),
            ),
            auditTime(0),
            filter(() => this.documentStatusService.getDocumentStatus() === DocumentStatus.Edit),
            distinctUntilChanged((a, b) => deepEqual(a?.code, b?.code)),
            map((unitCostBy: CodelistItem) => {
              const itemColorList: ItemColorList[] = this.documentDataQuery.getValue()?.itemColorList ?? [];
              const itemSizeList: ItemSizeList[] = this.documentDataQuery.getValue()?.itemSizeList ?? [];

              const filterItemColorList = itemColorList
                .filter((itemColor) => itemColor.isPrimary && !itemColor.isInactive)
                .map((itemColor) => ({ ...itemColor, colorShortName: itemColor.shortName, itemColor }));

              const filterItemSizeList = itemSizeList.filter((itemSize) => !itemSize.isInactive);

              const groupItemSizeList$ = this.transformItemSizeData(
                of(filterItemSizeList),
                'sizeDisplayName',
                'sizeName',
                'dimension.code',
                'dimension.code',
              );
              return { unitCostBy, filterItemColorList, groupItemSizeList$ };
            }),
            switchMap(({ unitCostBy, filterItemColorList, groupItemSizeList$ }) => {
              if (isEmptyOrNil(unitCostBy)) {
                return of([]);
              }

              if (unitCostBy.code === 'COLOR') {
                return of(filterItemColorList.map((record) => ({ ...record, id: generateUUID() })));
              }

              return groupItemSizeList$.pipe(
                map((groupItemSizeList) => {
                  if (unitCostBy.code === 'COLOR_SIZE') {
                    return cartesianProduct([filterItemColorList, groupItemSizeList]).map(R.mergeAll);
                  }
                  return groupItemSizeList;
                }),
                map((result) => result.map((record) => ({ ...record, id: generateUUID(), sizeUniqueKey: record.id }))),
              );
            }),
            untilDestroyed(this),
            shareReplay(1),
          ),
        ),
      )
      .subscribe((vqUnitCostDetailList) => {
        this.documentDataService.updateData({ vqUnitCostDetailList });
        this.documentDataService.dynamicRowChanges$.next({
          changes: vqUnitCostDetailList.map((row, rowIndex) => ({
            sectionId: 'vqUnitCostDetailList',
            changeType: 'update',
            rowId: row.id.toString(),
            rowIndex,
            content: row,
          })),
        });
      });
  }

  private handleOtherRequirement() {
    this.documentDataQuery
      .select('otherRequirementTempId')
      .pipe(auditTime(0), untilDestroyed(this))
      .subscribe(() => {
        this.removeValidation();
      });
  }

  private filterActiveShareFile(sectionData: Vq_VqShareFileDto[]) {
    return of(
      sectionData?.filter(
        (row) =>
          row.shareFile.docStatus !== 'inactive' &&
          row.shareFile.docStatus !== 'canceled' &&
          !row.shareFile?.dataInVisible,
      ) ?? [],
    );
  }

  private removeValidation() {
    const asyncError: any = JSON.parse(JSON.stringify(this.documentDefineStore.getValue().ui.asyncError));
    if (isNotEmptyOrNil(asyncError.grids.vqOtherRequirementList)) {
      asyncError.grids.vqOtherRequirementList = undefined;
    }
    const invalid: any = JSON.parse(JSON.stringify(this.documentDefineStore.getValue().ui.invalid));
    const { tabs, sections } = invalid;
    sections.vqOtherRequirementList = undefined;
    tabs.tabOtherRequirement = undefined;
    this.documentDefineStore.update((state) => ({ ui: { ...state.ui, asyncError, invalid } }));
  }

  handleShareFileList(shareFileListFieldId: string) {
    this.documentDataQuery
      .select(shareFileListFieldId)
      .pipe(
        filter(isNotEmptyOrNil),
        map((shareFileList) =>
          shareFileList?.filter(
            (shareFileRefLine: any) => shareFileRefLine.shareFile.refNo && isNil(shareFileRefLine.dataAccessiable),
          ),
        ),
        untilDestroyed(this),
      )
      .subscribe((shareFileList: any[]) => {
        shareFileList?.forEach((shareFileRefLine) => {
          const { refNo } = shareFileRefLine.shareFile;
          const { version } = shareFileRefLine.shareFile;
          this.documentService
            .getDataAccessiableIgnoreError$('shareFile', refNo, version)
            .subscribe((dataAccessiable) => {
              this.documentDataService.updateCellValueByFieldId(
                dataAccessiable,
                shareFileListFieldId,
                null,
                'dataAccessiable',
                shareFileRefLine.id,
              );
            });
        });
      });
  }
}
