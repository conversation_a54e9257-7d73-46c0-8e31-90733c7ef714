import { Dialog, DialogConfig } from '@angular/cdk/dialog';
import { CdkMenuModule } from '@angular/cdk/menu';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import { CommonModule, DatePipe } from '@angular/common';
import { HttpStatusCode } from '@angular/common/http';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  HostBinding,
  Inject,
  OnInit,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { ActivatedRoute, NavigationEnd, Router, RouterLink } from '@angular/router';

import { TranslocoModule } from '@ngneat/transloco';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject, combineLatest, Observable, Subject } from 'rxjs';
import { filter, map } from 'rxjs/operators';

import { ChatRoom } from '../../../../projects/chat/src/lib/model';
import { defaultDialogConfig } from '../../config/dialog';
import { ConfigurationService } from '../../services/configuration.service';
import { UserService } from '../../services/user.service';
import { WINDOW } from '../../services/window.service';
import { immutableInsertTo, isEmptyOrNil, resolveFormat } from '../../utils';
import { BookmarkMenuComponent } from '../bookmark-menu/bookmark-menu.component';
import LINKS from './links.json';
import { ChatComponent } from 'src/app/chat/chat.component';
import { IconComponent } from 'src/app/component/icon/icon.component';
import { CBX_URL } from 'src/app/config/constant';
import { TooltipDirective } from 'src/app/directives/tooltip.directive';
import { SearchCondition } from 'src/app/entities';
import { BookmarkItem, ChatRoomsParameter } from 'src/app/interface/model';
import { AuthenticatorData } from 'src/app/interface/model/dialog';
import { UserDto } from 'src/app/modules/container/document/cbx-module/user/model/user-dto';
import { UserPreferenceService } from 'src/app/modules/container/user-preference/service/user-preference.service';
import { QrCodeScannerComponent } from 'src/app/modules/shared/common/qr-code-scanner/qr-code-scanner.component';
import { TwoFactorDialogComponent } from 'src/app/modules/shared/common/two-factor-dialog/two-factor-dialog/two-factor-dialog.component';
import { CbxLabelTranslatePipe } from 'src/app/modules/shared/pipe/cbx-label-translate.pipe';
import { ApiService } from 'src/app/services/api.service';
import { AuthService } from 'src/app/services/auth.service';
import { ChatApiService } from 'src/app/services/chat/chat-api.service';
import { MessageDialogService } from 'src/app/services/message-dialog.service';
import { NavigationService } from 'src/app/services/navigation.service';
import { NotificationCheckService } from 'src/app/services/notification-check.service';
import { NotificationService } from 'src/app/services/notification.service';
import { DomainAttributeService } from 'src/app/workspace/service/domain-attribute.service';

export interface TopMenuItem {
  labelId?: string;
  labelKey?: string;
  icon?: string;
  useCbxIcon?: boolean;
  type?: 'separator' | 'two-Factor' | 'workingFor';
  url?: string;
  externalUrl?: string;
  items?: TopMenuItem[];
}

@UntilDestroy()
@Component({
  selector: 'app-topmenu',
  templateUrl: './topmenu.component.html',
  styleUrls: ['./topmenu.component.scss'],
  providers: [UserPreferenceService],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CommonModule,
    IconComponent,
    TooltipDirective,
    RouterLink,
    BookmarkMenuComponent,
    ChatComponent,
    CdkMenuModule,
    TranslocoModule,
    CbxLabelTranslatePipe,
  ],
})
export class TopmenuComponent implements OnInit {
  @HostBinding('class') class = 'flex items-center';

  @ViewChild('QRCode', { static: false }) QRCodeTemplate: TemplateRef<any>;
  notification = 11;

  showTwoFAManageButton$ = new BehaviorSubject(false);
  twoFAManageButtonLabel$ = new BehaviorSubject('Manage Two-Factor Auth');

  userId = this.authService.state.userInfo.userId;
  userEmail = this.authService.state.userInfo.email;
  isTbyMigration = this.authService.state.userInfo.isTbyMigration;
  workingVendorDomainId = this.authService.state.userInfo.domain_id;

  productReleaseVersion: string = '';

  currentDate: string = '';

  userAbbreviation$ = this.usersService.user$.pipe(
    map((user) => {
      const firstChar = [...(user.firstName ?? '')][0] ?? '';
      const secondChar = [...(user.lastName ?? '')][0] ?? '';
      return `${firstChar.toUpperCase()}${secondChar.toUpperCase()}`;
    }),
  );

  scanQRCode$ = this.domainAttributeService.selectHomePagePanelSetting('scanQRCode');
  flagShowTwoFactor = this.configurationService.CONFIG.FLAG_SHOW_TWO_FACTOR;

  naviView$ = this.navigationService.view$;
  moduleLabel$ = this.navigationService.module$.pipe(map((module) => module?.label ?? ''));
  naviTabId$ = this.navigationService.tab$.pipe(
    filter((tab) => !!tab),
    map(({ id }) => id),
  );

  qrCodeScannerConfig: DialogConfig = {
    hasBackdrop: true,
    disableClose: true,
    maxWidth: '80vw',
    maxHeight: '80vh',
    width: '500px',
    autoFocus: 'dialog',
  };

  searchCondition$ = this.route.queryParamMap.pipe<SearchCondition>(
    map((queryParamMap) =>
      Object.keys(new SearchCondition())
        .map((key) => [key, queryParamMap.get(key)])
        .filter(([, value]) => value)
        .reduce((prev, [key, value]) => ({ ...prev, [key]: value }), {}),
    ),
  );

  workingForUsers$ = this.usersService.workingForUsers$;
  switchForUsers$ = this.usersService.switchForUsers$;
  currentWorkingForUser$ = this.usersService.currentWorkingForUser$;

  userProfileImage$ = this.usersService.user$.pipe(map((user) => user.photoId?.thumbnailUrl));
  noUserProfileImage$ = this.userProfileImage$.pipe(map(isEmptyOrNil));
  userProfileBackgroundUrl$ = this.userProfileImage$.pipe(
    filter((url) => !!url),
    map((url) => `url("${url}")`),
  );

  hasWorkingForUsers$ = this.usersService.hasWorkingForUsers$;
  hasSwitchForUsers$ = this.usersService.hasSwitchForUsers$;
  links$: Observable<TopMenuItem[]> = combineLatest([this.hasWorkingForUsers$, this.hasSwitchForUsers$]).pipe(
    map(([hasWorkingForUsers, hasSwitchForUsers]) => this.handleLinks(hasWorkingForUsers, hasSwitchForUsers)),
  );

  rooms = this.getRooms();
  isAllRead$ = new BehaviorSubject<boolean>(false);

  getRooms() {
    const params: ChatRoomsParameter = {
      page: 0,
      isExternalIncluded: false,
      q: '',
    };
    this.chatApiService.getRooms(params).subscribe((res) => {
      if (this.isParticipant(res) && (res?.lastReadMessageId !== res.lastMessage?.id || res.unread)) {
        this.isAllRead$.next(false);
      }
    });
  }

  uiExternalChatRoomEnable: boolean;
  uiExternalChatRoomEnable$ = this.domainAttributeService.uiExternalChatRoomEnable$;

  hiddenExternalDomain() {
    return !this.uiExternalChatRoomEnable$ && this.authService.state.userType === 'vendor';
  }

  isParticipant(room: ChatRoom) {
    return (
      room.participants?.some((participant) => participant.id === this.userId) ||
      room.groupIds?.some((groups) => groups.users?.some((user) => user.id === this.userId))
    );
  }

  userColor$ = combineLatest([this.authService.userColor$, this.userProfileImage$]).pipe(
    map(([color, image]) => (image ? 'white' : color)),
  );

  QRCodePopup: OverlayRef;
  stopInterval$ = new Subject<boolean>();
  imageSrc: SafeUrl;
  showQrCode: boolean;

  constructor(
    @Inject(WINDOW) private readonly window: Window,
    readonly configurationService: ConfigurationService,
    private readonly authService: AuthService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly dialog: Dialog,
    private readonly navigationService: NavigationService,
    private readonly apiService: ApiService,
    private readonly usersService: UserService,
    private readonly chatApiService: ChatApiService,
    private readonly cdr: ChangeDetectorRef,
    private readonly domainAttributeService: DomainAttributeService,
    private readonly notificationCheckService: NotificationCheckService,
    private readonly overlay: Overlay,
    private readonly viewContainerRef: ViewContainerRef,
    private readonly sanitizer: DomSanitizer,
    private readonly userPreferenceService: UserPreferenceService,
    private readonly messageDialogService: MessageDialogService,
    private readonly notificationService: NotificationService,
    private readonly datePipe: DatePipe,
  ) {
    this.notificationCheckService.getNotificationStatus().subscribe((status) => {
      this.showNotificationRedDot$.next(status);
    });
    this.getQRCode();

    this.showTwoFAManageButton$.next(this.flagShowTwoFactor);
    this.authService.userInfo$.subscribe((userInfo) => {
      const { requireTwoFA, mfaEnabled } = userInfo;
      if (mfaEnabled) {
        if (requireTwoFA) {
          this.twoFAManageButtonLabel$.next('Reset Two-Factor Auth');
        } else {
          this.twoFAManageButtonLabel$.next('Disable Two-Factor Auth');
        }
      } else {
        this.twoFAManageButtonLabel$.next('Enable Two-Factor Auth');
      }
    });
    this.userPreferenceService.fetchUserPreference().subscribe((userPreference) => {
      this.showTwoFAManageButton$.next(this.flagShowTwoFactor && !userPreference?.isSystemSSOUser);
    });
  }

  showNotificationRedDot$ = new BehaviorSubject(false);
  isShowingAnnouncement$ = new BehaviorSubject(false);
  announcementData: string;
  isShowMore: boolean;

  ngOnInit() {
    this.getUnread();
    this.showSystemAnnouncement();
  }

  currentUser() {
    this.authService.getCurrentUser().subscribe((user) => {
      const { refNo } = user;
      if (refNo) {
        this.router.navigateByUrl(`/document/setup/user/${refNo}`);
      } else {
        this.router.navigateByUrl(`/document/setup/user/${this.authService.state.userInfo.userLoginId}`);
      }
    });
  }

  getProductReleaseVersion() {
    this.apiService.get(`${CBX_URL.moduleAlias({ module: 'system' })}/releaseLog`).subscribe((data: any) => {
      const { version, appliedOn } = data;
      this.productReleaseVersion = version;
      this.currentDate = this.datePipe.transform(new Date(appliedOn), 'yyyy-MM-dd HH:mm:ss', 'UTC');
      this.currentDate = this.currentDate ? `${this.currentDate}Z` : '';
    });
  }

  logout(isExpired?: boolean) {
    this.authService.addRedirectUrl('');
    this.authService.logout(isExpired);
  }

  naviDocumentTo(event: Event) {
    if (this.router.url.replace(/\((.*?)\)/, '$1').includes('RHS:rightHandSide/inbox/showall')) {
      this.router.navigate([{ outlets: { RHS: null } }], { skipLocationChange: true, queryParamsHandling: 'preserve' });
    } else {
      this.router.navigate([{ outlets: { RHS: ['rightHandSide', 'inbox', 'showall', 'Business'] } }], {
        skipLocationChange: true,
        queryParamsHandling: 'preserve',
      });
    }

    event.stopPropagation();
  }

  openQrCodeDialog() {
    this.dialog.open(QrCodeScannerComponent, {
      ...this.qrCodeScannerConfig,
    });
  }

  directToBookmark(bookmark: BookmarkItem) {
    const bookmarkQueryParam = bookmark?.bookmarkQueryParam;
    if (bookmarkQueryParam !== null && bookmarkQueryParam !== undefined) {
      this.router.navigate([bookmark.url], {
        queryParams: {
          cbxql: bookmarkQueryParam.get('cbxql'),
          q: bookmarkQueryParam.get('q'),
          dateRange: bookmarkQueryParam.get('dateRange'),
        },
      });
    } else {
      this.router.navigate([bookmark.url]);
    }
  }

  onClickMenuItem(link: TopMenuItem) {
    if (link.url) {
      this.router.navigate([link.url]);

      return;
    }

    if (link.externalUrl) {
      this.window.location.href = link.externalUrl;
    }
  }

  navigateToTby() {
    const casUrl = this.configurationService.CONFIG.CAS_URL;
    const tbyUrl = this.configurationService.CONFIG.TBY_URL;
    const serviceUrl = encodeURIComponent(`${tbyUrl}/redirect?username=${this.userEmail}`);

    window.location.href = `${casUrl}/login?service=${serviceUrl}`;
  }

  cloneWindow() {
    const { location } = this.window;
    if (!location) {
      return;
    }
    const newWindow = this.window.open(location.href, '_blank');
    newWindow?.addEventListener('load', () => {
      newWindow.history.pushState({ ...this.window.history.state }, '');
    });
  }

  onClickSwitchWorkingFor(userId: string) {
    this.authService.switchWorkingForUser(userId).subscribe(() => {
      this.router.navigateByUrl('/home');
      window.location.reload();
    });
  }

  manageTwoFactor() {
    this.apiService.get<AuthenticatorData>('api/users/authenticationCode').subscribe({
      next: (authenticatorData) => {
        this.openWarningDialog(authenticatorData);
      },
      error: (error) => {
        console.error(error);
      },
    });
  }

  private handleLinks(hasWorkingForUsers: boolean, hasSwitchForUsers: boolean) {
    let links = (LINKS as TopMenuItem[]).map((link) => {
      if (link.labelId === 'newCbx') {
        return { ...link, externalUrl: this.configurationService.CONFIG.ORIGINAL_CBX };
      }

      return link;
    });

    // LULU-327
    if (this.authService.state.userType === 'vendor') {
      links = links.filter(({ labelId }) => labelId !== 'newCbx');
    }

    if (hasWorkingForUsers || !!hasSwitchForUsers) {
      const workingForLinks: TopMenuItem[] = [
        {
          type: 'separator',
        },
        {
          type: 'workingFor',
          labelKey: 'workspace.workingFor',
          labelId: 'workingFor',
          icon: 'account_box',
          url: '/working-for',
        },
      ];

      links = immutableInsertTo(links, workingForLinks, 1);
    }

    return links;
  }

  private openWarningDialog(authenticatorData: AuthenticatorData) {
    this.dialog
      .open<any>(TwoFactorDialogComponent, {
        ...defaultDialogConfig,
        disableClose: true,
        height: 'auto',
        minHeight: '190px',
        width: 'auto',
        minWidth: '404px',
        data: authenticatorData,
      })
      .closed.pipe(
        filter((result) => result?.type === 'done'),
        map((result) => result?.payload),
        untilDestroyed(this),
      )
      .subscribe((data) => {
        if (data?.status === 'reset') {
          this.logout();
        }
      });
  }

  allRead(allRead: boolean) {
    this.isAllRead$.next(allRead);
    this.cdr.detectChanges();
  }

  getUnread() {
    const params: ChatRoomsParameter = {
      page: 0,
      isExternalIncluded: false,
      q: '',
    };
    this.chatApiService.getUnread(params).subscribe((res) => {
      if (res) {
        if (this.userId === res.userId?.id && res.unread) {
          this.isAllRead$.next(false);
        }
      }
    });
  }

  onClickSwitchUser(user: UserDto) {
    let isInactiveVendor = false;
    this.apiService.get<boolean>(CBX_URL.checkSwitchVendor(user.domainId)).subscribe((result: any) => {
      isInactiveVendor = result;
      if (isInactiveVendor) {
        this.messageDialogService.openOkayDialog(
          'Vendor is already deactivated. Please contact buyer. ',
          'information',
        );
        return;
      }
      this.apiService
        .post<any>(CBX_URL.switchUser(user.domainId), {})
        .pipe(untilDestroyed(this))
        .subscribe(() =>
          this.authService.switchWorkingForUser(user.id).subscribe(() => window.location.replace('/home')),
        );
      this.router.events.subscribe((event) => {
        if (event instanceof NavigationEnd) {
          window.location.reload();
        }
      });
    });
  }

  dumbChangeDetect() {}

  closeOverlay() {
    this.stopInterval$.next(true);
    if (this.QRCodePopup) {
      this.QRCodePopup.detach();
      this.QRCodePopup.dispose();
      return true;
    }
    return false;
  }
  openQRCodePopup() {
    this.QRCodePopup = this.overlay.create({
      maxWidth: '100%',
      maxHeight: '100%',
      hasBackdrop: true,
      positionStrategy: this.overlay.position().global().centerHorizontally('0').centerVertically('0'),
    });
    this.QRCodePopup.attach(new TemplatePortal(this.QRCodeTemplate, this.viewContainerRef));
  }
  getQRCode() {
    this.apiService
      .getWithBinaryResponse(CBX_URL.getCustQRCode, { responseType: 'blob', observe: 'response' })
      .pipe()
      .subscribe((data) => {
        if (HttpStatusCode.Ok === data.status) {
          this.imageSrc = this.sanitizer.bypassSecurityTrustUrl(window.URL.createObjectURL(data.body));
          this.showQrCode = true;
        }
      });
  }

  showSystemAnnouncement() {
    this.apiService.get<any>(CBX_URL.lookupSystemAnnouncement).subscribe((announcement) => {
      if (announcement) {
        let duration = (announcement.endTime - Math.floor(Date.now() / 1000)) / 1000;
        duration = duration >= 30 ? 30 : duration;
        this.isShowMore = announcement.content.length > 100;
        this.announcementData = announcement.content;
        const countdownTimer = setInterval(() => {
          duration -= 1;
          if (duration === 0) {
            clearInterval(countdownTimer);
            this.closeBar();
          }
        }, 1000);
        this.isShowingAnnouncement$.next(true);
      }
    });
  }

  toggleShowAll() {
    this.closeBar();
    this.notificationService.open({
      message: resolveFormat(this.announcementData, null, null),
      duration: 0,
      type: 'announcement',
      position: 'center',
      vPosition: 'top',
    });
  }

  closeBar() {
    this.isShowingAnnouncement$.next(false);
  }
}
