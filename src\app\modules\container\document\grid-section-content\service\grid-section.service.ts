/* eslint-disable @typescript-eslint/no-unused-vars */
import { Injectable } from '@angular/core';

import { isNil } from '@datorama/akita';
import { Column, GridApi, IRowNode } from 'ag-grid-community';
import * as R from 'ramda';
import { BehaviorSubject, debounceTime, distinctUntilChanged, filter, Subject } from 'rxjs';

import { DocumentViewData } from '../../../../../entities';
import { FieldDefine, PopupMeta } from '../../../../../entities/form-field';
import { FormFieldTypes } from '../../../../../entities/form-field-types';
import { AnyObject, ColumnDefs, Record } from '../../../../../interface/model';
import {
  autoSizeById,
  deepClone,
  deepEqual,
  generateUUID,
  immutableSplice,
  isEmptyOrNil,
  isNotEmptyOrNil,
  isNotNil,
  mutableSet,
} from '../../../../../utils';
import { DocumentService } from '../../../../services/document.service';
import { DocumentDataQuery, DocumentDataState, DocumentDefineQuery, Section } from '../../state';
import { CBX_URL } from 'src/app/config/constant';
import { FormDefine_Field } from 'src/app/entities/api';
import { DocumentGridCellValueChange } from 'src/app/interface/model/web-socket-frame';
import { CellRendererAngularComp } from 'src/app/modules/shared/common/cbx-table/cell-renderer-params';
import { ApiService } from 'src/app/services/api.service';
import { AuthService } from 'src/app/services/auth.service';

@Injectable()
export class GridSectionService {
  sectionId: string;
  gridApi: GridApi;

  autoSizeTask$ = new BehaviorSubject<any>(null);

  isVendorDomain = this.authService.state.userType === 'vendor';

  private readonly loadingStateChanged$ = new BehaviorSubject<boolean>(false);
  gridLoading$ = this.loadingStateChanged$.asObservable();

  private readonly actionLoadingStateChanged$ = new BehaviorSubject<boolean>(false);
  actionLoading$ = this.actionLoadingStateChanged$.asObservable();

  readonly focusFirstCellSubject$ = new BehaviorSubject<{
    focus: boolean;
    rowIndex?: number;
    autoFocusRowId?: string;
    autoSelectRowIds?: string[];
  }>({
    focus: false,
  });
  focusFirstCell$ = this.focusFirstCellSubject$.asObservable();

  readonly columnSizeStable$ = new BehaviorSubject(false);

  readonly refreshGrid$ = new Subject<void>();

  refreshGrid() {
    this.refreshGrid$.next();
  }

  constructor(
    private readonly documentDefineQuery: DocumentDefineQuery,
    private readonly documentDataQuery: DocumentDataQuery,
    private readonly documentService: DocumentService,
    private readonly apiService: ApiService,
    private readonly authService: AuthService,
  ) {
    this.autoSizeTask$
      .pipe(
        debounceTime(100),
        distinctUntilChanged(deepEqual),
        filter((autoSizeTask) => autoSizeTask?.isStartAutoSize),
      )
      .subscribe((autoSizeTask) => {
        autoSizeById(this.gridApi, autoSizeTask.fieldId);
        this.autoSizeTask$.next({
          fieldId: autoSizeTask.fieldId,
          isStartAutoSize: false,
        });
      });
  }

  setGridLoading(state: boolean) {
    this.loadingStateChanged$.next(state);
  }

  setActionLoading(state: boolean) {
    this.actionLoadingStateChanged$.next(state);
  }

  isSystemField(field: FieldDefine): boolean {
    return field?.type === FormFieldTypes.hidden || field?.isSystemField;
  }

  generateRows(
    fields: FieldDefine[],
    originalRowList: AnyObject<any>[],
    records: AnyObject<any>[],
    seqId: string,
    removeOriginalRecord?: boolean,
    defaultValue?: AnyObject<any>,
    moduleId?: string,
    sectionId?: string,
  ) {
    const rowData = removeOriginalRecord ? [] : originalRowList ?? [];

    const sequences = rowData.map((row) => parseInt(row[seqId], 10)).filter((seq) => seq);
    const maxBaseSeq = Math.max(...sequences, 0);

    const internalSeqNos = rowData.map((row) => parseInt(row.internalSeqNo, 10)).filter((seq) => seq);
    const maxInternalSeqNo = Math.max(...internalSeqNos, 0);

    const newRows = records.map((record, index) =>
      this.initialRowData(
        fields,
        { ...record, isNewRow: true },
        sectionId === 'evaluationTemplateDtlList' ? maxBaseSeq + 10 : maxBaseSeq + index + 1,
        maxInternalSeqNo + index + 1,
        defaultValue,
      ),
    );
    const newRowData = [...rowData, ...(newRows ?? [])];
    // newRowData.sort((record1, record2) => record1[seqId] - record2[seqId]);
    return newRowData;
  }

  generateRowsAfterSelected(
    fields: FieldDefine[],
    originalRowList: AnyObject<any>[],
    records: AnyObject<any>[],
    seqId: string,
    removeOriginalRecord?: boolean,
    defaultValue?: AnyObject<any>,
  ) {
    const rowData = removeOriginalRecord ? [] : originalRowList ?? [];

    const sequences = rowData.map((row) => parseInt(row[seqId], 10)).filter((seq) => seq);
    const maxBaseSeq = Math.max(...sequences, 0);

    const internalSeqNos = rowData.map((row) => parseInt(row.internalSeqNo, 10)).filter((seq) => seq);
    const maxInternalSeqNo = Math.max(...internalSeqNos, 0);

    let moveRowData = [...rowData];
    records.forEach((record, index) => {
      const initNewRow = this.initialRowData(
        fields,
        { ...record, isNewRow: true },
        maxBaseSeq + index + 1,
        maxInternalSeqNo + index + 1,
        defaultValue,
      );
      const rowDataIndex = moveRowData.findIndex((row) => row.id === record.id);
      if (isEmptyOrNil(sequences)) {
        moveRowData = immutableSplice(moveRowData, rowDataIndex + 1, 0, initNewRow);
      } else {
        moveRowData.push(initNewRow);
      }
    });
    return moveRowData;
  }

  generateGroupSectionRows(fields: FieldDefine[], rowIndex: number, defaultValue?: AnyObject<any>) {
    return this.initialRowData(fields, [], rowIndex, rowIndex, defaultValue);
  }

  mergeWithViewData<T = DocumentViewData>(
    fields: FieldDefine[],
    popupMeta: PopupMeta,
    selectionViewData: T,
    currentSectionId: string,
    sections: Section[],
    removeOriginalRecord?: boolean,
  ) {
    const patchedViewData = { ...selectionViewData };
    const documentData = this.documentDataQuery.getValue();

    Object.keys(selectionViewData)
      .map((sectionId) => sections.find((section) => section.id === sectionId))
      .filter((section) => section?.type === 'grid')
      .forEach((section) => {
        const sectionId = section.id;
        const orgRecords = documentData[sectionId] || [];
        let appendRecords = selectionViewData[sectionId] || [];
        const seqId = this.getSeqIdFromFields(this.documentDefineQuery.getFieldsBySectionId(sectionId));
        const gridDataKeys = sectionId === currentSectionId ? popupMeta?.dataKeys : section.gridMeta?.dataKeys;

        if (gridDataKeys?.length) {
          appendRecords = this.filterDuplicatedRecord(orgRecords, appendRecords, gridDataKeys);
        }

        patchedViewData[sectionId] = this.generateRows(
          currentSectionId === sectionId ? fields : this.documentDefineQuery.getFieldsBySectionId(sectionId),
          orgRecords,
          appendRecords,
          seqId,
          removeOriginalRecord,
        );
      });

    return patchedViewData as Partial<DocumentDataState>;
  }

  initialRowData(
    fields: FieldDefine[],
    row: any,
    baseSeq: number,
    nextInternalSeqNo: number,
    defaultValue?: AnyObject<any>,
  ) {
    row = Number.isNaN(nextInternalSeqNo) ? { ...row } : { ...row, internalSeqNo: nextInternalSeqNo };

    const needInitialType = {
      [FormFieldTypes.seq]: { value: baseSeq },
      [FormFieldTypes.radio]: { value: false },
    };

    const rowPlaceholder = fields
      .filter((field) => isNotNil(field.id))
      .reduce((acc, field) => {
        acc[field.id] = null;
        if (field.mapping) {
          const [id] = field.mapping.split('.');
          acc[id] = null;
        }
        return acc;
      }, {} as AnyObject<any>);

    const newRow = {
      ...rowPlaceholder,
      ...deepClone(row),
      id: generateUUID(),
      ...defaultValue,
      uniqueKey: null,
      isVendorDomain: this.isVendorDomain,
    };

    // row.customFields is a Object
    if (isNotEmptyOrNil(defaultValue?.customFields)) {
      newRow.customFields = { ...row?.customFields, ...defaultValue?.customFields };
    }

    fields.forEach((fieldDefine: FormDefine_Field) => {
      if (fieldDefine.composite) {
        // TODO: no-parameter-reassignment
        fieldDefine = R.isEmpty(row)
          ? fieldDefine.composite.defaultField
          : this.documentService.getRealDefineInCompositeDefine(row, fieldDefine);
      }

      let realData = this.documentService.getFieldDataByDefine(newRow, fieldDefine);
      if (defaultValue) {
        const value = defaultValue[fieldDefine.id];
        if (value) {
          realData = value;
        }
      }
      // internalSeqNo is system field, should not sync with the business field
      const cellValue =
        needInitialType[fieldDefine.type] && fieldDefine.id !== 'internalSeqNo'
          ? needInitialType[fieldDefine.type].value
          : realData;

      const paths = fieldDefine.mapping ? fieldDefine.mapping.split('.') : [fieldDefine.id];

      mutableSet(newRow, paths, cellValue);

      // Factory Audit special logic
      if (isNotEmptyOrNil(fieldDefine?.cellAvailableDynamically)) {
        newRow[fieldDefine.cellAvailableDynamically] = true;
      }
    });

    return newRow;
  }

  filterDuplicatedRecord(sourceRecord: Record[], compareRecord: Record[], dataKeys: string[]) {
    return R.differenceWith(
      (recordA, recordB) => this.generateRowID(recordA, dataKeys) === this.generateRowID(recordB, dataKeys),
      compareRecord,
      sourceRecord,
    );
  }

  private generateRowID(record: Record, dataKeys: string[]): string {
    const identifiers = [];

    dataKeys.forEach((key) => {
      const keyGroup = key.split('.');
      const identifier = keyGroup.reduce((value, idxKey) => value?.[idxKey] ?? undefined, record);
      identifiers.push(identifier);
    });

    if (identifiers.every(isNil)) {
      return generateUUID();
    }

    return identifiers.join(' / ');
  }

  getSeqIdFromFields(fields: FieldDefine[]) {
    const seqField = fields.find((field) => field.type === FormFieldTypes.seq);
    return seqField?.id || 'seq';
  }

  getAgCellsCompInstance(gridApi: GridApi, rowNodes: IRowNode[] = null, columns: (string | Column)[] = null) {
    return gridApi
      .getCellRendererInstances({ rowNodes, columns })
      .map((component) => component)
      .filter((component) => !!component);
  }

  getAgCellsComp(gridApi: GridApi, rowNodes: IRowNode[] = null, columns: (string | Column)[] = null) {
    return gridApi
      .getCellRendererInstances({ rowNodes, columns })
      .map((component: CellRendererAngularComp) => component.gui)
      .filter((component) => !!component);
  }

  getAgCellsParent(gridApi: GridApi, rowNodes: IRowNode[] = null, columns: (string | Column)[] = null): Element[] {
    return gridApi
      .getCellRendererInstances({ rowNodes: rowNodes?.filter((rowNode) => !!rowNode), columns })
      .map((component: CellRendererAngularComp & { eGui: HTMLElement }) => {
        const correctGui = component?.eGui ?? component?.gui;
        return correctGui?.closest('.ag-cell');
      })
      .filter((component) => !!component);
  }

  updateGridColumnDefs(
    entityName: string,
    uiId: string,
    formId: string,
    frozenIndex: number,
    isAddRemoveColumn: boolean,
    columnDefsList: ColumnDefs[],
    gridCacheExtraParam?: string,
  ) {
    const gridColVisibleOrderDto = {
      entityName,
      uiId,
      formId,
      frozenIndex,
      isAddRemoveColumn,
      columnDefsList,
      gridCacheExtraParam,
    };
    return this.apiService.post(CBX_URL.gridColVisibleOrderView, gridColVisibleOrderDto);
  }

  generateNewRow(
    fields: FieldDefine[],
    originalRowList: AnyObject<any>[],
    selectedRow: any,
    seqId: string,
    defaultValue?: AnyObject<any>,
    moduleId?: string,
    sectionId?: string,
  ) {
    const rowData = !originalRowList ? [] : deepClone(originalRowList);
    const sequences = rowData.map((row) => +row[seqId] || 0);
    const maxBaseSeq = Math.max(...sequences, 0);
    const internalSeqNos = rowData.map((row) => parseInt(row.internalSeqNo, 10)).filter((seq) => seq);
    const maxInternalSeqNo = Math.max(...internalSeqNos, 0);
    const baseSeq =
      moduleId === 'evaluationTemplate' && sectionId === 'evaluationTemplateDtlList' ? maxBaseSeq + 10 : maxBaseSeq + 1;
    const newMaxInternalSeqNo = maxInternalSeqNo + 1;
    const newRow = this.initialRowData(fields, selectedRow, baseSeq, newMaxInternalSeqNo, defaultValue);
    rowData.push({ ...newRow, isNewRow: true });
    return rowData;
  }

  // TODO:(ce) check where use this method
  generateNewSeqNos(
    rowData: any[],
    fields: FieldDefine[],
    moduleId?: string,
    sectionId?: string,
    rowGroupField?: string,
    currentRow?: any,
  ) {
    const seqFieldIdList = [];
    fields
      ?.filter((field) => field.type === FormFieldTypes.seq && field.id !== 'internalSeqNo')
      .forEach((field: FieldDefine) => {
        seqFieldIdList.push(field.id);
      });
    if (rowGroupField) {
      const moveGroupRow = rowData?.filter((row) => row[rowGroupField] === currentRow[rowGroupField]);
      const rowGroupChanges: DocumentGridCellValueChange[] = [];
      const rowGroupNewRows = rowData.map((row, rowIndex) => {
        let newRow = row;
        moveGroupRow.forEach((moveRow, index) => {
          if (moveRow.id === row.id) {
            seqFieldIdList?.forEach((fieldId) => {
              newRow = { ...newRow, [fieldId]: index + 1 };
              rowGroupChanges.push({
                sectionId,
                rowId: newRow.id,
                rowIndex,
                content: index + 1,
                path: fieldId,
                fieldId,
              });
            });
          }
        });
        return newRow;
      });
      return { result: rowGroupNewRows, changes: rowGroupChanges };
    }
    const changes: DocumentGridCellValueChange[] = [];
    const newRows = rowData.map((row, index) => {
      let newRow = row;
      const baseSeq =
        moduleId === 'evaluationTemplate' && sectionId === 'evaluationTemplateDtlList' ? (index + 1) * 10 : index + 1;
      seqFieldIdList?.forEach((fieldId) => {
        newRow = { ...newRow, [fieldId]: baseSeq };
        changes.push({
          sectionId,
          rowId: newRow.id,
          rowIndex: index,
          content: baseSeq,
          path: fieldId,
          fieldId,
        });
      });
      return newRow;
    });
    return { result: newRows, changes };
  }

  generateRow(fields: FieldDefine[], originalRowList: AnyObject<any>[], seqId: string, defaultValue?: AnyObject<any>) {
    const rowData = !originalRowList ? [] : deepClone(originalRowList);
    const sequences = rowData.map((row) => +row[seqId] || 0);
    const maxBaseSeq = Math.max(...sequences, 0);
    const internalSeqNos = rowData.map((row) => parseInt(row.internalSeqNo, 10)).filter((seq) => seq);
    const maxInternalSeqNo = Math.max(...internalSeqNos, 0);
    const newMaxInternalSeqNo = maxInternalSeqNo + 1;
    const newRow = this.initialRowData(fields, [], maxBaseSeq + 1, newMaxInternalSeqNo, defaultValue);
    return { ...newRow, isNewRow: true };
  }

  getNestedValue<T>(obj: T, path: string): string {
    if (!obj || !path) return '';
    return path.split('.').reduce((acc, key) => acc?.[key], obj);
  }
}
