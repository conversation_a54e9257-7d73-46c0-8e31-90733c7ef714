.record-section {
  font-size: 13px;
  font-weight: 400;
  height: calc(100% - 52px);
  overflow: hidden;
  margin-top: 12px;
  padding: 0 12px;
}

.wrapper {
  height: 60px;
  overflow: hidden;

  &:hover {
    opacity: 0.75;
    background-color: rgba(169, 202, 235, 0.15);
  }
}

.hint-wrapper {
  flex: 0 0 24px;
  align-self: flex-start;
}

.unread-hint {
  border-radius: 50%;
  width: 8px;
  height: 8px;
  margin: 4px auto;
  background-color: var(--color-main-secondary);
}

.content {
  width: calc(100% - 24px);
}

.header {
  font-weight: 500;
  color: var(--color-text-default);
  line-height: 18px;
  height: 18px;
}

.description {
  color: var(--color-text-subtlest);
  line-height: 18px;
  font-size: 13px;
  padding-top: 4px;
}

.date {
  color: var(--color-text-subtlest);
}

.attach-file-icon {
  color: var(--color-text-light);
  margin-left: 8px;
  margin-top: 6px;
  font-size: 20px;
  height: 20px;
  width: 20px;
}

.divider {
  margin: 4px 0 8px 0;
}

.view-all-button {
  color: var(--color-main-secondary);
  height: 34px;
  font-size: 13px;
  font-weight: 400;
  width: calc(100% - 24px);
  border-top: 1px solid #0000001f;
}
