import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, HostBinding, Input, Output } from '@angular/core';

import { trim } from 'ramda';
import { filter, map, ReplaySubject } from 'rxjs';

import { FieldBaseDirective } from '../../field-base.directive';
import { arrowKeys } from 'src/app/config/keyboard-key';
import { FieldDefine } from 'src/app/entities/form-field';
import { BaseTextareaComponent } from 'src/app/modules/shared/common/base-component/base-textarea/base-textarea.component';

@Component({
  selector: 'app-textarea-edit',
  templateUrl: './textarea-edit.component.html',
  styleUrls: ['./textarea-edit.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [AsyncPipe, BaseTextareaComponent],
})
export class TextareaEditComponent extends FieldBaseDirective {
  @HostBinding('class') textareaEditClass = 'block position-relative';

  field$ = new ReplaySubject<FieldDefine>(1);
  @Input() get field(): FieldDefine {
    return this._field;
  }
  set field(field: FieldDefine) {
    this._field = field;
    this.field$.next(field);
  }
  private _field: FieldDefine;

  textareaHeight$ = this.field$.pipe(
    filter((field) => !!field.rowspan && !this.isAutoHeight),
    map((field) => `${field.rowspan}rem`),
  );

  @Input() isAutoHeight: boolean = false;
  @Input() size: string;
  @Input() maxHeight: string;
  @Input() readonly: boolean;

  @Output() textareaKeydown = new EventEmitter<KeyboardEvent>();

  @Output() panelOpen = new EventEmitter<boolean>();

  newValue: string = null;

  onFocusChanged(focus: boolean) {
    if (focus) {
      this.newValue = this.value;
    } else {
      const hasChanged = this.newValue !== this.value;
      if (hasChanged) {
        this.valueChange.emit({ value: trim(this.newValue) });
      }
    }

    this.focusChange.emit(focus);
  }

  onTextareaKeydown(event: KeyboardEvent) {
    if (arrowKeys[event.key]) {
      event.stopPropagation();
    }
    this.textareaKeydown.emit(event);
  }
}
