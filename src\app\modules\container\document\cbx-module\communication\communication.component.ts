import { ChangeDetectionStrategy, Component } from '@angular/core';

import { DocumentComponentStrategy } from '../../../document-switch/document-component-strategy';
import { GridActionDispatcherService } from '../../grid-section-content/service/grid-action-dispatcher.service';
import { CellMapperModuleToken } from '../../service/cell-mapper-constant';
import { CellMapperService } from '../../service/cell-mapper.service';
import { DocumentDropdownService } from '../../service/document-dropdown.service';
import { DocumentDynamicDataService } from '../../service/document-dynamic-data.service';
import { DocumentDynamicFieldService } from '../../service/document-dynamic-field.service';
import { DocumentFacadeService } from '../../service/document-facade.service';
import { DocumentFieldReadonlyService } from '../../service/document-field-readonly.service';
import { FieldMapperModuleToken } from '../../service/field-mapper-constant';
import { FieldMapperService } from '../../service/field-mapper.service';
import { CommunicationCellMapperService } from './service/communication-cell-mapper.service';
import { CommunicationDropdownService } from './service/communication-dropdown.service';
import { CommunicationDynamicDataService } from './service/communication-dynamic-data.service';
import { CommunicationDynamicFieldService } from './service/communication-dynamic-field.service';
import { CommunicationFieldMapperService } from './service/communication-field-mapper.service';
import { CommunicationFieldReadonlyService } from './service/communication-field-readonly.service';
import { CommunicationGridActionDispatcherService } from './service/communication-grid-action-dispatcher.service';

@Component({
  selector: 'app-communication',
  template: '',
  standalone: true,

  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    DocumentFacadeService,
    {
      provide: DocumentDynamicDataService,
      useClass: CommunicationDynamicDataService,
    },
    {
      provide: DocumentDynamicFieldService,
      useClass: CommunicationDynamicFieldService,
    },
    {
      provide: DocumentDropdownService,
      useClass: CommunicationDropdownService,
    },
    {
      provide: DocumentFieldReadonlyService,
      useClass: CommunicationFieldReadonlyService,
    },
    CellMapperService,
    {
      provide: CellMapperModuleToken,
      useClass: CommunicationCellMapperService,
    },
    FieldMapperService,
    {
      provide: FieldMapperModuleToken,
      useClass: CommunicationFieldMapperService,
    },
    {
      provide: GridActionDispatcherService,
      useClass: CommunicationGridActionDispatcherService,
    },
  ],
})
export default class CommunicationComponent extends DocumentComponentStrategy {
  constructor(private readonly documentFacadeService: DocumentFacadeService) {
    super();
    this.documentFacadeService.registerBusinessLogic();
  }
}
