<app-cbx-dialog-header>{{ title }}</app-cbx-dialog-header>

<ng-container *transloco="let t; read: 'user-preference'" [formGroup]="passwordForm$ | async">
  <div class="padding-x-16 padding-y-16 section-container">
    <label *ngIf="(isAdminUser$ | async) === false" class="flex label">
      <span class="section-field-label require">{{ t('oldPassword') }}:</span>
      <input type="password" class="text-input bg-white" formControlName="oldPassword" appControlErrorHint />
    </label>

    <label class="flex label">
      <span class="section-field-label require">{{ 'newPassword' | transloco }}:</span>
      <input
        type="password"
        class="text-input bg-white"
        formControlName="newPassword"
        appControlErrorHint
        (focus)="newPasswordOnFocus()"
        (blur)="newPasswordOnBlur()"
      />
      <div class="icon-wrapper flex items-center">
        <app-icon
          svgIcon="icon_info_yellow"
          class="info-icon icon-size-16"
          appPasswordHint
          showOrHide="true"
          [validateType]="validateType"
          [value]="newPassword$ | async"
          [disabledMouse]="true"
          [passwordPolicy]="passwordPolicy$"
          [validateHistory]="validateHistory$ | async"
          [userInfo]="userInfo"
          (validate)="newPasswordValid$.next($event)"
        >
        </app-icon>
      </div>
    </label>

    <label class="flex label">
      <span class="section-field-label require">{{ 'confirmPassword' | transloco }}:</span>
      <input type="password" class="text-input" formControlName="confirmPassword" appControlErrorHint />
    </label>
  </div>
</ng-container>

<div class="flex items-center padding-x-16 padding-y-8 gap-8">
  <div #footerSpacer class="flex-spacer"></div>
  <button class="basic-button min-width-94" (click)="onCancel()">{{ 'cancel' | transloco }}</button>

  <button
    class="flat-button primary-color min-width-94"
    [disabled]="(passwordForm$ | async).invalid"
    [class.disabled-button]="(passwordForm$ | async).invalid"
    (click)="onConfirm()"
  >
    {{ 'document.confirm' | transloco }}
  </button>
</div>
