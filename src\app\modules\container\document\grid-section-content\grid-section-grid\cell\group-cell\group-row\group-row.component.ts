import { <PERSON><PERSON><PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  HostBinding,
  OnDestroy,
  Optional,
  ViewChild,
} from '@angular/core';

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { <PERSON>rid<PERSON>pi, IRowNode } from 'ag-grid-community';
import * as R from 'ramda';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { delay, finalize, map, take, tap } from 'rxjs/operators';

import { IconComponent } from '../../../../../../../../component/icon/icon.component';
import { Condition, FieldDefine, GroupConfig, MenuItem } from '../../../../../../../../entities/form-field';
import { AnyObject } from '../../../../../../../../interface/model';
import { BaseMenuBarComponent } from '../../../../../../../shared/common/base-component/base-menu-bar/base-menu-bar.component';
import { MultilineViewerComponent } from '../../../../../../../shared/common/multiline-viewer/multiline-viewer.component';
import { GridSectionService } from '../../../../service/grid-section.service';
import { GroupRowActionDispatcherService } from '../../../../service/group-row-action-dispacher.service';
import { GroupRowTrigger, RowGroupCellRenderParams } from '../../cell-component';
import { TextCellViewAutoHeightComponent } from '../../view-cell/text-cell-view-auto-height/text-cell-view-auto-height.component';
import {
  DocumentDataQuery,
  DocumentDefineQuery,
  DocumentStatus,
  DocumentStatusService,
} from 'src/app/modules/container/document/state';
import {
  AngularCellComponent,
  CellRendererAngularComp,
} from 'src/app/modules/shared/common/cbx-table/cell-renderer-params';
import { AgGridRowHeightDirective } from 'src/app/modules/shared/directives/ag-grid-row-height.directive';
import { GridhintDirective } from 'src/app/modules/shared/directives/grid-hint/gridhint.directive';
import { LabelOnlyPipe } from 'src/app/modules/shared/pipe/label-only.pipe';
import { checkSingleConditionValidInObject, isNotEmptyOrNil } from 'src/app/utils';
import { notMap } from 'src/app/utils/operators';
import { DomainAttributeService } from 'src/app/workspace/service/domain-attribute.service';
import { DOMAIN_ATTRIBUTES } from 'src/app/workspace/service/domain-attributes';

@UntilDestroy()
@Component({
  selector: 'app-group-row',
  templateUrl: './group-row.component.html',
  styleUrls: ['./group-row.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    NgIf,
    AsyncPipe,
    IconComponent,
    MultilineViewerComponent,
    TextCellViewAutoHeightComponent,
    BaseMenuBarComponent,
    GridhintDirective,
    LabelOnlyPipe,
  ],
})
export class GroupRowComponent
  extends AngularCellComponent
  implements CellRendererAngularComp, AfterViewInit, OnDestroy
{
  @HostBinding('class') class = 'flex height-100';
  @ViewChild('section') section: ElementRef;

  groupConfig: GroupConfig;
  params: RowGroupCellRenderParams;
  node: IRowNode;
  treeActionButtons: MenuItem[];
  moduleId: string;
  sectionId: string;
  isExpanded: boolean;
  fields: FieldDefine[];
  gridApi: GridApi;

  isExpanded$ = new BehaviorSubject<boolean>(false);
  collapsed$ = this.isExpanded$.pipe(notMap);

  groupRowData: AnyObject<any>;
  sectionHeader: string;
  sectionLabel: string;
  sectionHint: string;
  showAddDelete = true;
  aggData$ = new Subject<string>();
  percentOfScore$ = new Subject<string>();
  statusScore$ = new Subject<string>();
  maxScore$ = new Subject<string>();
  percentOfScoreLeft$ = new Subject<string>();
  statusScoreLeft$ = new Subject<string>();
  maxScoreLeft$ = new Subject<string>();
  isShowPercentOfScore$ = new BehaviorSubject<boolean>(true);
  isShowStatusScore$ = new BehaviorSubject<boolean>(true);
  isShowMaxScore$ = new BehaviorSubject<boolean>(true);

  hideCondition: Condition;

  statusChanged$ = this.documentStatusService.getDocumentStatus$();

  defineLoading$ = this.documentDefineQuery.selectLoading();

  dataLoading$ = this.documentDataQuery.selectLoading();

  onlyDataLoading$ = combineLatest([this.defineLoading$, this.dataLoading$]).pipe(
    map(([defineLoading, dataLoading]) => !defineLoading && dataLoading),
  );

  editable$ = combineLatest([this.statusChanged$, this.onlyDataLoading$]).pipe(
    map(([status, dataLoading]) => status === DocumentStatus.Edit && !dataLoading),
  );

  hideActionButton$ = new BehaviorSubject<boolean>(false);

  canShowMenuBar$ = combineLatest([this.editable$, this.hideActionButton$]).pipe(
    map(([editable, hideActionButton]) => editable && !hideActionButton && this.showAddDelete),
  );

  actionLoading$ = new BehaviorSubject(false);

  isVpoCocItemLevel: boolean = this.domainAttributeService.state[DOMAIN_ATTRIBUTES.VPO_COC_LEVEL] === 'vpoItem';

  constructor(
    private readonly actionDispatcherService: GroupRowActionDispatcherService,
    private readonly documentStatusService: DocumentStatusService,
    private readonly documentDataQuery: DocumentDataQuery,
    private readonly documentDefineQuery: DocumentDefineQuery,
    private readonly gridSectionService: GridSectionService,
    private readonly domainAttributeService: DomainAttributeService,
    host: ElementRef,
    @Optional() private readonly agGridRowHeightDirective: AgGridRowHeightDirective,
  ) {
    super(host);
  }

  ngAfterViewInit(): void {
    this.buildSummary();
    this.calculateScorData(null);
    this.calculateScrollLeft(0);
  }

  agInit(params: RowGroupCellRenderParams) {
    const { api, groupRowActionBus$ } = params;
    this.params = params;
    this.node = params.node;
    this.groupConfig = params.groupConfig;
    this.treeActionButtons = params.treeActionButtons;
    this.moduleId = params.moduleId;
    this.sectionId = params.sectionId;
    this.fields = params.fields;
    this.gridApi = api;

    groupRowActionBus$.pipe(delay(100), untilDestroyed(this)).subscribe((groupRowTrigger: GroupRowTrigger) => {
      if (groupRowTrigger.gridId === this.sectionId && this.node.id === groupRowTrigger.id) {
        this.buildSummary();
      }
      if (groupRowTrigger.gridId === this.sectionId && groupRowTrigger?.eventType === 'scrollEnd') {
        this.calculateScrollLeft(groupRowTrigger.left);
      }
      this.params.data$?.pipe(untilDestroyed(this)).subscribe((data) => {
        this.calculateScorData(data);
      });
    });

    this.sectionHeader = `Section ${this.node.childIndex + 1}`;
    this.sectionLabel = this.groupConfig?.sectionLabel;
    this.sectionHint = this.node?.allLeafChildren[0]?.data[this.groupConfig?.sectionHint];
    if (!this.isVpoCocItemLevel) {
      this.treeActionButtons = this.treeActionButtons.filter(
        (treeActionButton) =>
          !(treeActionButton.id === 'refreshVpoChainOfCustody' || treeActionButton.id === 'selectVpoChainOfCustody'),
      );
    }

    if (R.startsWith('parentSection', this.node.field) || R.startsWith('groupKey', this.node.field)) {
      this.sectionLabel = `{${this.node.field}}`;
      this.sectionHint = this.node?.allLeafChildren[0]?.data[this.groupConfig?.parentSectionHit];

      if (R.equals('groupKey1', this.node.field)) {
        this.treeActionButtons = this.treeActionButtons.filter(
          (treeActionButton) =>
            treeActionButton.id === 'refreshVpoChainOfCustody' || treeActionButton.id === 'selectVpoChainOfCustody',
        );
      } else {
        this.treeActionButtons = this.treeActionButtons.filter(
          (treeActionButton) =>
            (treeActionButton.id !== 'refreshVpoChainOfCustody' &&
              treeActionButton.id !== 'selectVpoChainOfCustody' &&
              R.equals('groupKey2', this.node.field)) ||
            (treeActionButton.id !== 'refreshVpoChainOfCustody' &&
              treeActionButton.id !== 'selectVpoChainOfCustody' &&
              treeActionButton.id !== 'addDetailItem'),
        );
      }

      // this.showAddDelete = false;
    }

    if (this.node.parent?.parent?.level === -1) {
      this.treeActionButtons = this.treeActionButtons?.filter(
        (treeActionButton) => treeActionButton.id !== 'addSubSection',
      );
    }

    this.groupRowData = params.node.allLeafChildren.map((node) => node.data);

    this.hideCondition = this.groupConfig?.hideCondition;

    if (isNotEmptyOrNil(this.hideCondition)) {
      if (isNotEmptyOrNil(this.hideCondition.mapping)) {
        this.hideCondition = {
          ...this.hideCondition,
          mapping: this.hideCondition.mapping.replaceAll(/{i}/g, this.sectionId.match(/\d+/g)?.at(0) ?? `{i}`),
        };
      }

      this.documentDataQuery
        .select()
        .pipe(
          map((data) => checkSingleConditionValidInObject(this.hideCondition, data)),
          tap((hideActionButton: boolean) => this.hideActionButton$.next(hideActionButton)),
          untilDestroyed(this),
        )
        .subscribe();
    }
    this.handleExpandToggle();

    const maxHeight =
      params?.data?.checklistField?.type === 'CHECKBOX' || params?.data?.checklistField?.type === 'RADIO_BUTTON'
        ? 350
        : undefined;
    this.agGridRowHeightDirective.registerResizeCell(params.node, this.host.nativeElement, maxHeight);
  }

  refresh() {
    return false;
  }

  ngOnDestroy(): void {
    this.agGridRowHeightDirective.removeResizeCell(this.params.node, this.host.nativeElement);
  }

  onMenuClick(menu: MenuItem) {
    const params = {
      gridSectionService: this.gridSectionService,
      moduleId: this.moduleId,
      groupRowData: this.groupRowData,
      groupConfig: this.groupConfig,
      sectionId: this.sectionId,
      node: this.node,
      fields: this.fields,
    };

    this.actionLoading$.next(true);
    this.actionDispatcherService
      .execute(menu, params)
      .pipe(
        take(1),
        finalize(() => this.actionLoading$.next(false)),
        untilDestroyed(this),
      )
      .subscribe({
        next: () => this.params.api.clearFocusedCell(),
        error: (error: string | Error) => console.error(error),
      });
  }

  showSectionRows() {
    this.isExpanded$.next(!this.isExpanded$.getValue());
  }

  private handleExpandToggle() {
    this.isExpanded$.next(this.node.expanded);

    this.isExpanded$.pipe(untilDestroyed(this)).subscribe((isExpanded) => this.node.setExpanded(isExpanded));
  }

  private buildSummary() {
    const columns = this.gridApi.getAllDisplayedColumns();
    const aggKeys = R.keys(this.node.aggData);
    if (aggKeys.length > 0) {
      const aggDataStr = [];

      columns
        .filter((column) => R.includes(column.getColId(), aggKeys))
        .forEach((column) => {
          const headerName = column.getColDef()?.headerName;
          const text = this.node.aggData[column.getColId()];

          aggDataStr.push(`${headerName}: ${text}`);
        });
      this.aggData$.next(`(${aggDataStr.join('; ')})`);
    }
  }

  private calculateScrollLeft(scrollLeft: number) {
    const columns = this.gridApi.getAllDisplayedColumns();
    const colIds = ['percentOfScore', 'statusScore', 'maxScore'];
    let frozenLength = 0;
    columns
      .filter((column) => R.includes(column.getColId(), colIds) || column.isLastLeftPinned())
      .forEach((column) => {
        if (column.isLastLeftPinned()) {
          frozenLength = column.getLeft() + column.getActualWidth();
        }
        const left = column.getLeft() + frozenLength - scrollLeft;
        if (column.getColId() === 'percentOfScore') {
          this.isShowPercentOfScore$.next(left + 8 - frozenLength < 0);
          this.percentOfScoreLeft$.next(`${left.toString()}px`);
        } else if (column.getColId() === 'statusScore') {
          this.statusScoreLeft$.next(`${left.toString()}px`);
          this.isShowStatusScore$.next(left + 8 - frozenLength < 0);
        } else if (column.getColId() === 'maxScore') {
          this.maxScoreLeft$.next(`${left.toString()}px`);
          this.isShowMaxScore$.next(left + 8 - frozenLength < 0);
        }
      });
  }

  private calculateScorData(datas: AnyObject<any>[]) {
    const columns = this.gridApi.getAllDisplayedColumns();
    const colIds = ['percentOfScore', 'statusScore', 'maxScore'];
    let statusScore = 0;
    let maxScore = 0;
    this.groupRowData.forEach((rowData) => {
      let filterData = null;
      if (datas) {
        filterData = datas.find((data) => data?.refNo === rowData?.refNo);
      }
      statusScore += filterData?.statusScore ? filterData.statusScore : 0;
      maxScore += filterData?.maxScore ? filterData.maxScore : 0;
    });
    columns
      .filter((column) => R.includes(column.getColId(), colIds))
      .forEach((column) => {
        if (column.getColId() === 'percentOfScore') {
          this.percentOfScore$.next(maxScore === 0 ? '0.00' : ((statusScore / maxScore) * 100).toFixed(2));
        } else if (column.getColId() === 'statusScore') {
          this.statusScore$.next(statusScore.toFixed(2));
        } else if (column.getColId() === 'maxScore') {
          this.maxScore$.next(maxScore.toFixed(2));
        }
      });
  }
}
