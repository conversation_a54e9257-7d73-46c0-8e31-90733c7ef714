import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn, Router } from '@angular/router';

import * as R from 'ramda';
import { Observable, forkJoin, of } from 'rxjs';
import { catchError, filter, map, shareReplay, switchMap, tap } from 'rxjs/operators';

import { DocumentViewData } from '../../../../entities';
import { DocumentViewDefine } from '../../../../entities/form-field';
import { NewDocResult } from '../../../../interface/model/new-doc-result';
import { MyRecentItemService } from '../../../../services/my-recent-item.service';
import { customURLEstimate, isNotEmptyOrNil } from '../../../../utils';
import { DomainAttributeService } from '../../../../workspace/service/domain-attribute.service';
import { DocumentService } from '../../../services/document.service';
import { GeneralTabService } from '../right-hand-panel/general-tab/general-tab.service';
import { DocumentViewEditService } from '../service/document-view-edit.service';
import { DocumentDataResolved } from './document-data-resolved';
import { CBX_URL } from 'src/app/config/constant';
import { ApiService } from 'src/app/services/api.service';
import { NavigationService } from 'src/app/services/navigation.service';
import { NotificationService } from 'src/app/services/notification.service';
import { RouterStateService } from 'src/app/services/router-state.service';

export function resolveDocumentData(
  route: ActivatedRouteSnapshot,
  filterDefine?: (define: DocumentViewDefine, data: DocumentViewData) => DocumentViewDefine,
): Observable<DocumentDataResolved> {
  const domainAttributeService = inject(DomainAttributeService);
  const documentService = inject(DocumentService);
  const navigationService = inject(NavigationService);
  const generalTabService = inject(GeneralTabService);
  const myRecentItemService = inject(MyRecentItemService);
  const { inSecondaryRoute } = inject(RouterStateService);
  const notificationService = inject(NotificationService);
  const documentViewEditService = inject(DocumentViewEditService);
  const router = inject(Router);
  const apiService = inject(ApiService);

  generalTabService.resetPartiesIsValid();

  const tabId = route.paramMap.get('tabId');
  const moduleId = route.paramMap.get('moduleId');
  const refNo = route.paramMap.get('refNo');
  const refNos = route.queryParamMap.get('refNos');
  // The version means "Snapshot Version" in the concurrent-edit mode
  const version = route.paramMap.get('version') ?? '';
  const domainId = route.paramMap.get('domainId') ?? '';
  const defaultModule = !!moduleId;

  const domainAttributeReady$ = domainAttributeService.loading$.pipe(filter((loading) => !loading));
  const getEnabledConcurrent = () => domainAttributeService.enabledConcurrentModules.includes(moduleId);

  const openApprovePopup = coerceBooleanProperty(route.queryParamMap.get('openApprovePopup'));
  const openRejectPopup = coerceBooleanProperty(route.queryParamMap.get('openRejectPopup'));
  const openCpmDialog = coerceBooleanProperty(route.queryParamMap.get('openCpmDialog'));

  const documentViewData$ = documentViewEditService.docData;

  if (documentViewData$) {
    const extraState = (router.getCurrentNavigation().extras.state ?? {}) as DocumentViewData;
    const newDocResultObservable = documentService
      .getViewDefineByData(moduleId, documentViewData$)
      .pipe(shareReplay(1));
    const formdefine$ = newDocResultObservable.pipe(map((result: NewDocResult) => result.formDefine));
    const viewData$ = newDocResultObservable.pipe(
      map((result: NewDocResult) => result.doc),
      map((defaultData) => R.mergeDeepLeft(extraState, defaultData) as DocumentViewData),
    );
    navigationService.updateCurrentNavi(tabId, moduleId, '', refNo, defaultModule);
    return of({
      version,
      tabId,
      moduleId,
      refNo,
      action: 'create',
      define$: formdefine$,
      data$: viewData$,
      openApprovePopup,
      openRejectPopup,
    });
  }

  const getDefine$ = (enabledConcurrent: boolean) => {
    if (enabledConcurrent) {
      return documentService.getViewDefine(moduleId, refNo);
    }

    return documentService.getViewDefine(moduleId, refNo, version);
  };

  const getData$ = (enabledConcurrent: boolean) => {
    if (enabledConcurrent && isNotEmptyOrNil(version)) {
      return documentService.getConcurrentSnapshotData(moduleId, refNo, version);
    }
    if (customURLEstimate(moduleId, domainId)) {
      return documentService.getViewData(moduleId, refNo, version, domainId);
    }
    if (moduleId === 'vqce') {
      // refNos=QTE2407-000002,QTE2407-000001
      return documentService.getVqceViewData(moduleId, refNos);
    }
    return documentService.getViewData(moduleId, refNo, version);
  };

  return domainAttributeReady$.pipe(
    map(getEnabledConcurrent),
    switchMap((enabledConcurrent) => {
      const data$ = getData$(enabledConcurrent);
      const define$ = getDefine$(enabledConcurrent);
      return forkJoin([define$, data$]).pipe(
        tap(([, data]) => {
          if (!inSecondaryRoute) {
            // TODO: should re-consider this hardcode solution (THD-721)
            if (data?.partnerType?.code === 'SP' && moduleId === 'vendor') {
              navigationService.updateCurrentNavi(tabId, 'serviceProvider', '', refNo, defaultModule);
            } else {
              navigationService.updateCurrentNavi(tabId, moduleId, '', refNo, defaultModule);
            }
          }
          myRecentItemService.insertMyRecentItem(moduleId, refNo, version).subscribe();
        }),
        switchMap(([define, data]) => {
          if (data.readStatus === false && data.status === 'published') {
            return apiService.patch(CBX_URL.markDocAsRead(moduleId, refNo), {}).pipe(
              switchMap(() => getData$(enabledConcurrent)),
              map((newData) => [define, newData]),
            );
          }
          return of([define, data]);
        }),
        map(([define, data]: [DocumentViewDefine, DocumentViewData]) => {
          const filteredDefine = filterDefine ? filterDefine(define, data) : define;
          return {
            version,
            tabId,
            moduleId,
            refNo,
            action: enabledConcurrent && isNotEmptyOrNil(version) ? 'snapshot' : 'view',
            define$: of(filteredDefine),
            data$: of(data),
            openApprovePopup,
            openRejectPopup,
            openCpmDialog,
          } as DocumentDataResolved;
        }),
        catchError((error) => {
          console.error(error);
          notificationService.errorNotify(error.errorMessage);
          return of(null);
        }),
      );
    }),
  );
}

export const DocumentResolver: ResolveFn<DocumentDataResolved> = (
  route: ActivatedRouteSnapshot,
): Observable<DocumentDataResolved> => resolveDocumentData(route);
