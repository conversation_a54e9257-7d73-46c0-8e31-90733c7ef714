import { Injectable } from '@angular/core';

import {
  DocumentModuleActionService,
  HandleActionParams,
  HandleActionResult,
  HandleGridActionParams,
} from '../../tokens';

@Injectable()
export class CommunicationActionService extends DocumentModuleActionService {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  handleAction(params: HandleActionParams): HandleActionResult {
    return false;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  handleGridAction(params: HandleGridActionParams): HandleActionResult {
    return false;
  }
}
