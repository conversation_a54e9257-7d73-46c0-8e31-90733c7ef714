import { Pipe, PipeTransform } from '@angular/core';

import { differenceInCalendarDays, format } from 'date-fns';

import { DATE_TYPE } from './date-time-format.pipe';
import { DateTimeFormatService } from 'src/app/services/date-time-format.service';

@Pipe({
  name: 'dateLabelFormat',
  standalone: true,
})
export class DateLabelFormatPipe implements PipeTransform {
  constructor(private readonly dateTimeFormatService: DateTimeFormatService) {}

  transform(value: string | Date, dateType: DATE_TYPE, dateFormat: string, showToday?: boolean): string {
    const localDatetime = this.dateTimeFormatService.transform(value, dateType, dateFormat);
    const days = this.getDaysDifference(new Date(localDatetime));

    switch (days) {
      case 0:
        return showToday ? 'Today' : format(new Date(localDatetime), 'HH:mm');
      case 1:
        return 'Yesterday';
      case 2:
      case 3:
      case 4:
      case 5:
      case 6:
        return format(new Date(localDatetime), 'EEEE');
      default:
        return localDatetime;
    }
  }

  private getDaysDifference(date: Date): number {
    return differenceInCalendarDays(new Date(), date);
  }
}
