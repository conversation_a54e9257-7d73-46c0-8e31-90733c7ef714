import { Injectable, Type } from '@angular/core';

import { defer, Observable } from 'rxjs';

import { ActionDispatcherService } from '../../../services/action-dispatcher.service';
import { AbstractUIAction } from 'src/app/modules/cbx-action/model/abstract-ui-action';

@Injectable()
export class DocumentActionDispatcherService extends ActionDispatcherService {
  actionMap: { [action: string]: Observable<Type<AbstractUIAction>> } = {
    ...this.actionMap,

    // document
    AmendDoc: defer(() => import('../cbx-action/action').then((a) => a.AmendDocAction)),
    EditDoc: defer(() => import('../cbx-action/action').then((a) => a.AmendDocAction)),
    DiscardDoc: defer(() => import('../cbx-action/action').then((a) => a.DiscardDocAction)),
    UpdateDoc: defer(() => import('../cbx-action/action').then((a) => a.UpdateDocAction)),
    CopyFromExistingDoc: defer(() => import('../cbx-action/action').then((a) => a.CopyFromExistingDocAction)),
    ActivateCopyMode: defer(() => import('../cbx-action/action').then((a) => a.ActivateCopyModeAction)),
    DeactivateCopyMode: defer(() => import('../cbx-action/action').then((a) => a.DeActivateCopyModeAction)),
    CopyFromSelectedDocData: defer(() => import('../cbx-action/action').then((a) => a.CopyFromSelectedDocDataAction)),
    CreateSrWithStData: defer(() => import('../cbx-action/action').then((a) => a.CreateSrWithStDataAction)),
    CreateWithData: defer(() => import('../cbx-action/action').then((a) => a.CreateWithDataAction)),
    RequestNewDnaRevisionAction: defer(() => import('../cbx-action/action').then((a) => a.RequestNewDnaRevisionAction)),
    PrintEss: defer(() => import('../cbx-action/action').then((a) => a.PrintEssAction)),
    GenerateTbsc: defer(() => import('../cbx-action/action').then((a) => a.GenerateTbscAction)),
    UploadToFlexPLM: defer(() =>
      import('../cbx-action/action/upload-to-flex-plm-action').then((a) => a.UploadToFlexPLMAction),
    ),
    CreateWithSelectedData: defer(() => import('../cbx-action/action').then((a) => a.CreateWithSelectedDataAction)),
    ExportInDoc: defer(() => import('../cbx-action/action').then((a) => a.ExportInDocAction)),
    PrintInDoc: defer(() => import('../cbx-action/action').then((a) => a.PrintInDocAction)),
    PrintTag: defer(() => import('../cbx-action/action').then((a) => a.PrintTagAction)),
    SetTo: defer(() => import('../cbx-action/action').then((a) => a.SetToAction)),
    MarkAs: defer(() => import('../cbx-action/action').then((a) => a.MarkAsAction)),
    MarkAsDocWithLinkedDoc: defer(() => import('../cbx-action/action').then((a) => a.MarkAsDocWithLinkedDocAction)),
    OpenListingView: defer(() => import('../cbx-action/action').then((a) => a.OpenListingViewAction)),
    ResendInvitationRequest: defer(() =>
      import('../cbx-module/invitationRequest/resend-invitation-request-action').then(
        (a) => a.ResendInvitationRequestAction,
      ),
    ),
    CustomAction: defer(() => import('../cbx-action/action').then((a) => a.CustomAction)),
    RefreshItems: defer(() => import('../cbx-action/action').then((a) => a.RefreshItemsAction)),
    //
    FollowDoc: defer(() => import('../cbx-action/action').then((a) => a.FollowDocAction)),
    UnfollowDoc: defer(() => import('../cbx-action/action').then((a) => a.UnFollowDocAction)),
    OpenCommentDoc: defer(() => import('../cbx-action/action').then((a) => a.OpenCommentDocAction)),
    OpenRelatedDoc: defer(() => import('../cbx-action/action').then((a) => a.OpenRelatedDocAction)),
    crossUpdate: defer(() => import('../cbx-action/action').then((a) => a.CrossUpdateAction)),
    CopyToClipboard: defer(() =>
      import('../cbx-action/action/copy-to-clipboard-action').then((a) => a.CopyToClipboardAction),
    ),
    PasteFromClipboard: defer(() =>
      import('../cbx-action/action/paste-from-clipboard-action').then((a) => a.PasteFromClipboardAction),
    ),
    // approval
    OpenApprovalByDoc: defer(() => import('../cbx-action/action').then((a) => a.OpenApprovalAction)),
    approve: defer(() => import('../cbx-action/action').then((a) => a.ApproveAction)),
    withdraw: defer(() => import('../cbx-action/action').then((a) => a.WithdrawAction)),
    reject: defer(() => import('../cbx-action/action').then((a) => a.RejectAction)),
    // regenerateCostSheets
    RegenerateCostSheets: defer(() => import('../cbx-action/action').then((a) => a.RegenerateCostSheetsAction)),
    AcceptAndRejectVendorChanges: defer(() =>
      import('../cbx-action/action/vendor-change-proposed/accept-reject-vendor-change-action').then(
        (a) => a.AcceptRejectVendorChangeAction,
      ),
    ),
    AcceptAndRejectComparison: defer(() =>
      import('../cbx-action/action/inline-comparison/accept-reject-comparison-action').then(
        (a) => a.AcceptRejectComparisonAction,
      ),
    ),
    SubmitAcceptOrRejectChanges: defer(() =>
      import('../cbx-action/action/submit-accept-reject-action').then((a) => a.SubmitAcceptOrRejectAction),
    ),
    RequestForESignature: defer(() =>
      import('../cbx-action/action/request-for-e-signature-action').then((a) => a.RequestForESignatureAction),
    ),
    ModuleRequestForESignature: defer(() =>
      import('src/app/components/request-for-signature/module-request-for-e-signature-action').then(
        (a) => a.ModuleRequestForESignatureAction,
      ),
    ),
    OpenPopupWin: defer(() => import('../cbx-action/action').then((a) => a.OpenPopupWinAction)),
    OpenLookupPopupWin: defer(() => import('../cbx-action/action').then((a) => a.OpenLookupPopupWinAction)),
    ExportExcel: defer(() => import('../cbx-action/action/export-excel-action').then((a) => a.ExportExcelAction)),
    PopupDocDetail: defer(() =>
      import('../cbx-action/action/popup-doc-detail-action').then((a) => a.PopupDocDetailAction),
    ),
    ViewReadUserDetail: defer(() => import('../cbx-action/action').then((a) => a.ViewReadUserDetailAction)),
    ViewAcknowledgedUserDetail: defer(() =>
      import('../cbx-action/action').then((a) => a.ViewAcknowledgedUserDetailAction),
    ),
  };
}
