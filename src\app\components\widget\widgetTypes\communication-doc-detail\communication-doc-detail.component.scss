.record-section {
  font-size: 13px;
  font-weight: 400;
  height: calc(100% - 48px);
  overflow: hidden;
  margin-top: 10px;
  padding: 0 12px;
}

.wrapper {
  height: 40px;

  &:hover {
    opacity: 0.75;
    background-color: rgba(169, 202, 235, 0.15);
  }
}

.hint-wrapper {
  flex: 0 0 24px;
  align-self: flex-start;
}

.unread-hint {
  border-radius: 50%;
  width: 8px;
  height: 8px;
  margin: 6px auto;
  background-color: var(--color-main-secondary);
}

.content {
  width: calc(100% - 24px);
}

.header {
  font-weight: 500;
  color: var(--color-text-default);
  line-height: 18px;
  height: 18px;
  margin-right: 6px;
}

.label {
  bottom: 8px;
  height: 16px;
  line-height: 16px;
  border-radius: 4px;
  background-color: var(--color-function-alert-contrast);
  color: var(--color-function-on-alert-contrast);
  font-size: 11px;
  padding: 0 6px;
  min-width: 90px;
  margin-right: 6px;
}

.description {
  color: var(--color-text-subtlest);
  line-height: 18px;
  height: 18px;
}

.date {
  color: var(--color-text-subtlest);
}

.divider {
  margin: 5px 0;
}

.view-all-button {
  color: var(--color-main-secondary);
  height: 32px;
  font-size: 13px;
  font-weight: 400;
}
