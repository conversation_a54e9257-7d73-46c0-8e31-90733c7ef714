import { DialogRef, DIALOG_DATA } from '@angular/cdk/dialog';
import { AsyncPipe, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormGroup,
  ReactiveFormsModule,
  UntypedFormBuilder,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';

import { TranslocoModule } from '@ngneat/transloco';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject } from 'rxjs';
import { distinctUntilChanged, map } from 'rxjs/operators';

import { IconComponent } from 'src/app/component/icon/icon.component';
import { CbxDialogHeaderComponent } from 'src/app/modules/shared/common/cbx-dialog-header/cbx-dialog-header.component';
import { PasswordValidateType } from 'src/app/modules/shared/directives/passwordhint/password-validate-type';
import { PasswordHintDirective } from 'src/app/modules/shared/directives/passwordhint/passwordhint.directive';
import { AuthService } from 'src/app/services/auth.service';

@UntilDestroy()
@Component({
  selector: 'password-setting-dialog',
  templateUrl: './password-setting-dialog.component.html',
  styleUrls: ['./password-setting-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    NgIf,
    AsyncPipe,
    ReactiveFormsModule,
    TranslocoModule,
    IconComponent,
    CbxDialogHeaderComponent,
    PasswordHintDirective,
  ],
})
export class PasswordSettingDialogComponent implements OnInit {
  title = 'Set / Reset Password';

  validateType = PasswordValidateType.Auth;

  passwordPolicy$ = new BehaviorSubject(undefined);

  userInfo = {};

  isAdminUser$ = new BehaviorSubject<boolean>(false);

  newPasswordValid$ = new BehaviorSubject<boolean>(true);
  correctOldPassword$ = new BehaviorSubject<boolean>(true);

  oldPasswordControl = this.formBuilder.control('', [Validators.required, this.isCorrectOldPassword()]);
  oldPassword$ = this.oldPasswordControl.valueChanges;

  newPasswordControl = this.formBuilder.control('', [Validators.required, this.isValidPassword()]);
  newPassword$ = this.newPasswordControl.valueChanges;

  passwordForm$ = new BehaviorSubject<FormGroup>(
    this.formBuilder.group({
      oldPassword: this.oldPasswordControl,
      newPassword: this.newPasswordControl,
      confirmPassword: ['', [Validators.required, this.equalTo(this.newPasswordControl)]],
    }),
  );

  passwordAdminForm = this.formBuilder.group({
    newPassword: this.newPasswordControl,
    confirmPassword: ['', [Validators.required, this.equalTo(this.newPasswordControl)]],
  });

  validateHistory$ = new BehaviorSubject(false);

  constructor(
    private readonly authService: AuthService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private readonly dialogRef: DialogRef<any, PasswordSettingDialogComponent>,
    @Inject(DIALOG_DATA)
    readonly data: { loginId: string; isNewDoc: boolean; isAdminUser: boolean; firstName: string; lastName: string },
  ) {}

  ngOnInit(): void {
    this.oldPassword$.pipe(distinctUntilChanged(), untilDestroyed(this)).subscribe({
      next: () => this.correctOldPassword$.next(true),
    });
    this.authService
      .getPasswordPolicyByLoginId(this.data.loginId)
      .pipe(
        map((result) => {
          this.passwordPolicy$ = result;
        }),
        untilDestroyed(this),
      )
      .subscribe();

    const passwordValidateType = this.data.isNewDoc ? 'PASSWORD_REGISTER' : 'PASSWORD_CHANGE';
    if (this.data.isAdminUser || this.data.isNewDoc) {
      this.isAdminUser$.next(true);
      this.passwordForm$.next(this.passwordAdminForm);
    }

    this.userInfo = {
      firstName: this.data.firstName,
      lastName: this.data.lastName,
      loginId: this.data.loginId,
      requestBy: passwordValidateType,
    };
  }

  onCancel() {
    this.dialogRef.close({ type: 'cancel', payload: false });
  }

  onConfirm() {
    const obj = this.passwordForm$.value;
    if (obj instanceof FormGroup) {
      const fg = obj as FormGroup;
      if (fg.invalid) {
        fg.markAllAsTouched();
        return;
      }
      const { loginId, isNewDoc } = this.data;
      const { oldPassword, newPassword } = fg.value;
      const userPreference = {
        loginId,
        newPassword,
        oldPassword,
        isNewDoc,
      };
      this.callUpdateApi(userPreference);
    }
  }

  callUpdateApi(userPreference: { loginId: string; newPassword: string; oldPassword: string; isNewDoc: boolean }) {
    this.dialogRef.close({ type: 'done', payload: userPreference });
  }

  private isValidPassword(): ValidatorFn {
    const error: ValidationErrors = { invalidPassword: true };
    let subscribe = false;
    return (control: AbstractControl): ValidationErrors => {
      if (!subscribe) {
        subscribe = true;
        this.newPasswordValid$.pipe(distinctUntilChanged(), untilDestroyed(this)).subscribe({
          next: () => {
            control.updateValueAndValidity();
          },
        });
      }
      return this.newPasswordValid$.value ? null : error;
    };
  }

  private equalTo(equalControl: AbstractControl): ValidatorFn {
    let subscribe = false;

    return (control: AbstractControl): ValidationErrors => {
      if (!subscribe) {
        subscribe = true;
        equalControl.valueChanges.pipe(untilDestroyed(this)).subscribe(() => {
          control.updateValueAndValidity();
        });
      }

      const v: string = control.value;

      return equalControl.value === v ? null : { equalTo: { control: equalControl, value: equalControl.value } };
    };
  }

  private isCorrectOldPassword(): ValidatorFn {
    const error: ValidationErrors = { oldPasswordIncorrect: true };
    let subscribe = false;
    return (control: AbstractControl): ValidationErrors | null => {
      if (!subscribe) {
        subscribe = true;
        this.correctOldPassword$.pipe(untilDestroyed(this)).subscribe({
          next: () => {
            control.updateValueAndValidity();
            this.cdr.markForCheck();
          },
        });
      }

      return this.correctOldPassword$.value ? null : error;
    };
  }

  newPasswordOnFocus() {
    this.validateHistory$.next(false);
  }

  newPasswordOnBlur() {
    this.validateHistory$.next(true);
  }
}
