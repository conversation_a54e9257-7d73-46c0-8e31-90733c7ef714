import { CommonModule } from '@angular/common';
import {
  ApplicationRef,
  ChangeDetectionStrategy,
  Component,
  ComponentRef,
  createComponent,
  EnvironmentInjector,
  inject,
  Injector,
  Input,
  OnInit,
  Type,
  ViewContainerRef,
} from '@angular/core';

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Observable, defer, map, distinctUntilChanged, switchMap, tap, ReplaySubject, finalize, filter } from 'rxjs';

import { DocumentComponentStrategy } from '../document-switch/document-component-strategy';
import { DocumentPreviewComponent } from '../document/document-preview/document-preview.component';
import { DocumentComponent } from '../document/document.component';
import { FormTemplateContainerComponent } from '../document/form-template-container/form-template-container.component';
import { ColorSizeQtyService } from '../document/grid-section-content/grid-section-grid/cell/color-size-qty.service';
import { GridActionDispatcherService } from '../document/grid-section-content/service/grid-action-dispatcher.service';
import { GroupRowActionDispatcherService } from '../document/grid-section-content/service/group-row-action-dispacher.service';
import { GroupGridActionDispatcherService } from '../document/group-grid-container/service/group-grid-action-dispatcher.service';
import { DocumentDataResolved } from '../document/resolver/document-data-resolved';
import { RightHandPanelStateService } from '../document/right-hand-panel/right-hand-panel-state.service';
import { CascadeDropdownService } from '../document/service/cascade-dropdown.service';
import { DocumentActionDispatcherService } from '../document/service/document-action-dispatcher.service';
import { DocumentActionService } from '../document/service/document-action.service';
import { DocumentChapterService } from '../document/service/document-chapter.service';
import { DocumentDynamicDataService } from '../document/service/document-dynamic-data.service';
import { DocumentEventService } from '../document/service/document-event.service';
import { DocumentGridApiService } from '../document/service/document-grid-api.service';
import { DocumentUiService } from '../document/service/document-ui.service';
import { DocumentValidateService } from '../document/service/document-validate.service';
import { DocumentValueChangeService } from '../document/service/document-value-change.service';
import { DocumentConcurrentEditService } from '../document/service/hook/document-concurrent-edit.service';
import { DocumentConcurrentHookService } from '../document/service/hook/document-concurrent-hook.service';
import { concurrentHookProviders, normalHookProviders } from '../document/service/hook/mode-hook-providers';
import { ValidationHandler } from '../document/service/validation-handler.service';
import {
  documentDatabaseProviders,
  DocumentDataQuery,
  DocumentDataService,
  DocumentDefineStore,
  DocumentStatusService,
  SubSectionsQuery,
} from '../document/state';
import { DocumentBehaviorPreferenceService } from 'src/app/services/document-behavior-preference.service';
import { ConcurrentPendingChangesHook } from 'src/app/services/guard/pending-changes.guard';
import { ValidateTooltipService } from 'src/app/services/validate-tooltip.service';
import { InputObservable } from 'src/app/utils/input-subject-observer';
import { DomainAttributeService } from 'src/app/workspace/service/domain-attribute.service';

const deferImport = <T extends Promise<{ default: Type<DocumentComponentStrategy> }>>(
  getImport: () => T,
): Observable<Type<DocumentComponentStrategy>> =>
  defer(() =>
    getImport()
      .then((c) => c.default)
      .catch(() => null),
  );

const componentStrategyMap: { [moduleId: string]: Observable<Type<DocumentComponentStrategy>> } = {
  default: deferImport(() => import(`../document/cbx-module/default/default.component`)),

  artwork: deferImport(() => import('../document/cbx-module/artwork/artwork.component')),
  artworkPalette: deferImport(() => import('../document/cbx-module/artworkPalette/artwork-palette.component')),
  careInstruction: deferImport(() => import('../document/cbx-module/care-instruction/care-instruction.component')),
  color: deferImport(() => import('../document/cbx-module/color/color.component')),
  colorPalette: deferImport(() => import('../document/cbx-module/colorPalette/color-palette.component')),
  component: deferImport(() => import('../document/cbx-module/component/component.component')),
  correctiveActionPlans: deferImport(() => import('../document/cbx-module/cap/cap.component')),
  cost: deferImport(() => import('../document/cbx-module/cost/cost.component')),
  cso: deferImport(() => import('../document/cbx-module/cso/cso.component')),
  cust: deferImport(() => import('../document/cbx-module/cust/cust.component')),
  custInv: deferImport(() => import('../document/cbx-module/custInv/cust-inv.component')),
  documentRequestTemplate: deferImport(
    () => import('../document/cbx-module/document-request-template/doc-template.component'),
  ),
  evaluationTemplate: deferImport(
    () => import('../document/cbx-module/evaluation-template/evaluation-template.component'),
  ),
  fact: deferImport(() => import('../document/cbx-module/fact/fact.component')),
  factAudit: deferImport(() => import('../document/cbx-module/factAudit/fact-audit.component')),
  factoryAuditTemplate: deferImport(
    () => import('../document/cbx-module/factory-audit-template/factory-audit-template.component'),
  ),
  inspectBooking: deferImport(() => import('../document/cbx-module/inspectBooking/inspect-booking.component')),
  inspectReport: deferImport(() => import('../document/cbx-module/inspectReport/inspect-report.component')),
  inspectReportTemplate: deferImport(
    () => import('../document/cbx-module/inspectReportTemplate/inspect-report-template.component'),
  ),
  inspectTemplate: deferImport(() => import('../document/cbx-module/inspectTemplate/inspect-template.component')),
  item: deferImport(() => import('../document/cbx-module/item/item.component')),
  labelProfile: deferImport(() => import('../document/cbx-module/labelProfile/label-profile.component')),
  lineSheet: deferImport(() => import('../document/cbx-module/line-sheet/line-sheet.component')),
  materialPalette: deferImport(() => import('../document/cbx-module/materialPalette/material-palette.component')),
  materialRequestTemplate: deferImport(
    () => import('../document/cbx-module/material-request-template/mr-template.component'),
  ),
  measurementTemplate: deferImport(
    () => import('../document/cbx-module/measurementTemplate/measurement-template.component'),
  ),
  mpo: deferImport(() => import('../document/cbx-module/mpo/mpo.component')),
  offersheet: deferImport(() => import('../document/cbx-module/offersheet/offersheet.component')),
  packaging: deferImport(() => import('../document/cbx-module/packaging/packaging.component')),
  packagingArtwork: deferImport(() => import('../document/cbx-module/packagingArtwork/packaging-artwork.component')),
  packingList: deferImport(() => import('../document/cbx-module/packing-list/packing-list.component')),
  pattern: deferImport(() => import('../document/cbx-module/pattern/pattern.component')),
  patternPalette: deferImport(() => import('../document/cbx-module/patternPalette/pattern-palette.component')),
  project: deferImport(() => import('../document/cbx-module/project/project.component')),
  qq: deferImport(() => import('../document/cbx-module/qq/qq.component')),
  rfi: deferImport(() => import('../document/cbx-module/rfi/rfi.component')),
  rfq: deferImport(() => import('../document/cbx-module/rfq/rfq.component')),
  rfs: deferImport(() => import('../document/cbx-module/rfs/rfs.component')),
  sampleEvaluation: deferImport(() => import('../document/cbx-module/se/se.component')),
  sampleRequest: deferImport(() => import('../document/cbx-module/sr/sr.component')),
  sampleRequestTemplate: deferImport(
    () => import('../document/cbx-module/sample-request-template/sr-template.component'),
  ),
  sampleTracker: deferImport(() => import('../document/cbx-module/st/st.component')),
  shareFile: deferImport(() => import('../document/cbx-module/shareFile/share-file.component')),
  shipmentAdvice: deferImport(() => import('../document/cbx-module/sa/sa.component')),
  shipmentBooking: deferImport(() => import('../document/cbx-module/shipment-booking/shipment-booking.component')),
  sourcingRecord: deferImport(() => import('../document/cbx-module/sourcingRecord/sourcing-record.component')),
  specification: deferImport(() => import('../document/cbx-module/item/item.component')),
  user: deferImport(() => import('../document/cbx-module/user/user.component')),
  vendor: deferImport(() => import('../document/cbx-module/vendor/vendor.component')),
  vendorInvoice: deferImport(() => import('../document/cbx-module/vendor-invoice/vendor-invoice.component')),
  vpo: deferImport(() => import('../document/cbx-module/vpo/vpo.component')),
  vq: deferImport(() => import('../document/cbx-module/vq/vq.component')),
  vqce: deferImport(() => import('../document/cbx-module/vqce/vqce.component')),
  codelist: deferImport(() => import('../document/cbx-module/codelist/codelist.component')),
  hcl: deferImport(() => import('../document/cbx-module/hcl/hcl.component')),
  hclType: deferImport(() => import('../document/cbx-module/hclType/hcl-type.component')),
  dataListType: deferImport(() => import('../document/cbx-module/dataListType/data-list-type.component')),
  condition: deferImport(() => import('../document/cbx-module/condition/condition.component')),
  compTempl: deferImport(() => import('../document/cbx-module/comp-templ/comp-templ.component')),
  defaultProfile: deferImport(() => import('../document/cbx-module/defaultProfile/default-profile.component')),
  custFieldDef: deferImport(() => import('../document/cbx-module/custFieldDef/cust-field-def.component')),
  cpmTaskTempl: deferImport(() => import('../document/cbx-module/cpmTaskTempl/cpm-task-templ.component')),
  systemFile: deferImport(() => import('../document/cbx-module/systemFile/system-file.component')),
  lookup: deferImport(() => import('../document/cbx-module/lookup/lookup.component')),
  role: deferImport(() => import('../document/cbx-module/role/role.component')),
  group: deferImport(() => import('../document/cbx-module/group/group.component')),
  accessObject: deferImport(() => import('../document/cbx-module/accessObject/access-object.component')),
  printFormExportTemplate: deferImport(
    () => import('../document/cbx-module/printFormExportTemplate/print-form-export-template.component'),
  ),
  notificationProfile: deferImport(
    () => import('../document/cbx-module/notificationProfile/notification-profile.component'),
  ),
  trigger: deferImport(() => import('../document/cbx-module/trigger/trigger.component')),
  cpmTempl: deferImport(() => import('../document/cbx-module/cpmTempl/cpm-templ.compontent')),
  partyTemplate: deferImport(() => import('../document/cbx-module/partyTemplate/party-template.component')),
  dataAbstractConfig: deferImport(
    () => import('../document/cbx-module/dataAbstractConfig/data-abstract-config.component'),
  ),
  aprvTempl: deferImport(() => import('../document/cbx-module/aprvTempl/approval-template.component')),
  nutritionTemplate: deferImport(() => import('../document/cbx-module/nutritionTemplate/nutrition-template.component')),
  testProtocolTemplate: deferImport(
    () => import('../document/cbx-module/testProtocolTemplate/test-protocol-template.component'),
  ),
  testAccreditation: deferImport(() => import('../document/cbx-module/testAccreditation/test-accreditation.component')),
  testMethod: deferImport(() => import('../document/cbx-module/testMethod/test-method.component')),
  testReport: deferImport(() => import('../document/cbx-module/test-report/test-report.component')),
  vendorChargesTemplate: deferImport(
    () => import('../document/cbx-module/vendor-charges-template/vendor-charges-template-component'),
  ),
  costTempl: deferImport(() => import('../document/cbx-module/costTempl/cost-templ.component')),
  capaTemplate: deferImport(() => import('../document/cbx-module/capaTemplate/capa-template.component')),
  inlineComparisonTemplate: deferImport(
    () => import('../document/cbx-module/inlineComparisonTemplate/inline-comparison-template.component'),
  ),
  reqTempl: deferImport(() => import('../document/cbx-module/reqTempl/req-templ.component')),
  workflow: deferImport(() => import('../document/cbx-module/workflow/workflow.component')),
  basedNotification: deferImport(() => import('../document/cbx-module/basedNotification/based-notification.component')),
  validationProfile: deferImport(() => import('../document/cbx-module/validationProfile/validation-profile.component')),
  qualityPlanTemplate: deferImport(
    () => import('../document/cbx-module/qualityPlanTemplate/quality-plan-template.component'),
  ),
  import: deferImport(() => import('../document/cbx-module/import/import.component')),
  domain: deferImport(() => import('../document/cbx-module/domain/domain.component')),
  communication: deferImport(() => import('../document/cbx-module/communication/communication.component')),
  communicationDoc: deferImport(() => import('../document/cbx-module/communicationDoc/communication-doc.component')),
};

const concurrentComponentStrategyMap: { [moduleId: string]: Observable<Type<DocumentComponentStrategy>> } = {
  default: deferImport(() => import(`../document/cbx-module/default/default-concurrent.component`)),

  item: deferImport(() => import('../document/cbx-module/item/item-concurrent.component')),
  lineSheet: deferImport(() => import('../document/cbx-module/line-sheet/line-sheet-concurrent.component')),
  factAudit: deferImport(() => import('../document/cbx-module/factAudit/fact-audit-concurrent.component')),
  inspectReport: deferImport(() => import('../document/cbx-module/inspectReport/inspect-report-concurrent.component')),
  import: deferImport(() => import('../document/cbx-module/import/import-concurrent.component')),
  vq: deferImport(() => import('../document/cbx-module/vq/vq-concurrent.component')),
  vqce: deferImport(() => import('../document/cbx-module/vqce/vqce-concurrent.component')),
};

@UntilDestroy()
@Component({
  selector: 'app-document-builder',
  standalone: true,
  imports: [CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: '',
  providers: [
    documentDatabaseProviders,
    DocumentChapterService,
    CascadeDropdownService,
    ValidationHandler,
    DocumentActionService,
    DocumentValueChangeService,
    DocumentDataService,
    GridActionDispatcherService,
    GroupRowActionDispatcherService,
    DocumentActionDispatcherService,
    ColorSizeQtyService,
    DocumentGridApiService,
    DocumentUiService,
    DocumentBehaviorPreferenceService,
    RightHandPanelStateService,
    DocumentDynamicDataService,
    DocumentEventService,
    DocumentValidateService,
    GroupGridActionDispatcherService,
  ],
})
export class DocumentBuilderComponent implements OnInit {
  private readonly parentInject = inject(Injector);
  private readonly vcr = inject(ViewContainerRef);
  private readonly envInjector = inject(EnvironmentInjector);
  private readonly appRef = inject(ApplicationRef);
  private readonly domainAttributeService = inject(DomainAttributeService);
  private readonly concurrentPendingChangesHook = inject(ConcurrentPendingChangesHook);
  private readonly documentStatusService = inject(DocumentStatusService);
  private readonly documentDataQuery = inject(DocumentDataQuery);
  private readonly subSectionsQuery = inject(SubSectionsQuery);
  private readonly documentDefineStore = inject(DocumentDefineStore);
  private readonly validateTooltipService = inject(ValidateTooltipService);

  @Input() isPreview: boolean;

  @Input() isFormBuilder: boolean;

  private readonly resolvedData$ = new ReplaySubject<DocumentDataResolved>(1);
  @InputObservable()
  @Input()
  resolvedData: DocumentDataResolved;

  private readonly moduleId$ = this.resolvedData$.pipe(
    filter((resolvedData) => !!resolvedData),
    map((data) => data.moduleId),
    distinctUntilChanged(),
  );

  private readonly component$ = this.moduleId$.pipe(
    switchMap((moduleId) =>
      this.isFormBuilder
        ? componentStrategyMap.default
        : this.domainAttributeService.enabledConcurrentModules.includes(moduleId)
        ? concurrentComponentStrategyMap[moduleId] ??
          componentStrategyMap[moduleId] ??
          concurrentComponentStrategyMap.default
        : componentStrategyMap[moduleId] ?? componentStrategyMap.default,
    ),
    tap(() => this.vcr.clear()),
  );

  ngOnInit() {
    let cacheComponentRef: ComponentRef<DocumentComponentStrategy>;

    this.handlePendingChangesMode();

    this.component$
      .pipe(
        map((component) =>
          createComponent(component, {
            environmentInjector: this.envInjector,
            elementInjector: this.parentInject,
          }),
        ),
        switchMap((componentRef) => {
          if (cacheComponentRef) {
            this.appRef.detachView(cacheComponentRef.hostView);
            cacheComponentRef.destroy();
          }
          this.documentStatusService.reset();
          cacheComponentRef = componentRef;
          const documentComponent: Type<{ resolvedData: DocumentDataResolved }> = this.isFormBuilder
            ? FormTemplateContainerComponent
            : this.isPreview
            ? DocumentPreviewComponent
            : DocumentComponent;

          this.appRef.attachView(componentRef.hostView);

          const providers = this.concurrentPendingChangesHook.isConcurrentMode
            ? concurrentHookProviders
            : normalHookProviders;

          const injector = Injector.create({
            providers,
            parent: componentRef.injector,
          });

          if (this.concurrentPendingChangesHook.isConcurrentMode) {
            this.initConcurrentService(injector);
          }

          const documentComponentRef = this.vcr.createComponent(documentComponent, {
            injector,
          });

          let moduleId: string;

          return this.resolvedData$.pipe(
            filter((resolvedData) => !!resolvedData),
            filter((resolvedData) => {
              const isSameModule = resolvedData.moduleId === moduleId || !moduleId;
              return isSameModule;
            }),
            tap((resolvedData) => {
              moduleId = resolvedData.moduleId;
              componentRef.injector.get(DocumentDataService).reset();
              this.forwardComponentValues(documentComponentRef, resolvedData);
              documentComponentRef.changeDetectorRef.detectChanges();
            }),
          );
        }),
        finalize(() => {
          if (cacheComponentRef) {
            this.appRef.detachView(cacheComponentRef.hostView);
            cacheComponentRef.destroy();
          }
        }),
        untilDestroyed(this),
      )
      .subscribe();
  }

  private handlePendingChangesMode() {
    this.moduleId$.pipe(untilDestroyed(this)).subscribe((moduleId) => {
      this.concurrentPendingChangesHook.isConcurrentMode =
        this.domainAttributeService.enabledConcurrentModules.includes(moduleId);
    });
  }

  private forwardComponentValues(
    c: ComponentRef<{ resolvedData: DocumentDataResolved }>,
    resolvedData: DocumentDataResolved,
  ) {
    c.setInput('resolvedData', resolvedData);
  }

  private initConcurrentService(injector: Injector) {
    injector.get(DocumentConcurrentHookService);
    injector.get(DocumentConcurrentEditService);
  }

  getDocumentDataQuery(): DocumentDataQuery {
    return this.documentDataQuery;
  }

  getDocumentStatusService(): DocumentStatusService {
    return this.documentStatusService;
  }

  getSubSectionsQuery(): SubSectionsQuery {
    return this.subSectionsQuery;
  }

  getDocumentDefineStore(): DocumentDefineStore {
    return this.documentDefineStore;
  }

  getValidateTooltipService(): ValidateTooltipService {
    return this.validateTooltipService;
  }
}
