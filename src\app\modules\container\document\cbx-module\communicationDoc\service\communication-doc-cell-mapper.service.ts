import { Injectable } from '@angular/core';

import { addSuffix } from '../../../../../../utils';
import { editSuffix } from '../../../service/cell-mapper-constant';
import { OptionalCellMapperProperty } from '../../../service/cell-mapper.property';
import {
  communicationDocEditComponents,
  communicationDocViewComponents,
} from '../cell/communication-doc-cell-component-type';

@Injectable()
export class CommunicationDocCellMapperService implements OptionalCellMapperProperty {
  viewComponents = communicationDocViewComponents;
  editComponents = addSuffix(communicationDocEditComponents, editSuffix);

  cellComponents = {
    ...this.viewComponents,
    ...this.editComponents,
  };
}
