import { inject, Injectable } from '@angular/core';

import { isNil } from '@datorama/akita';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Decimal } from 'decimal.js';
import equal from 'fast-deep-equal';
import * as R from 'ramda';
import { BehaviorSubject, combineLatest, forkJoin, merge, Observable, of } from 'rxjs';
import {
  auditTime,
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  mergeMap,
  shareReplay,
  skip,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs/operators';

import { AnyObject } from '../../../../../../interface/model';
import {
  allHasValue,
  deepClone,
  deepEqual,
  dotPath,
  generateUUID,
  isEmptyOrNil,
  isNotEmptyOrNil,
  isNotNil,
} from '../../../../../../utils';
import { DocumentService } from '../../../../../services/document.service';
import { DataTransformService } from '../../../service/data-transform.service';
import { DocumentDynamicDataService } from '../../../service/document-dynamic-data.service';
import { DocumentGridApiService } from '../../../service/document-grid-api.service';
import { DocumentValueChangeService } from '../../../service/document-value-change.service';
import { DocumentDefineQuery, DocumentStatus, DocumentStatusService, SectionsQuery } from '../../../state';
import {
  CalculateType,
  ChargeType,
  ItemCSPriceAndPlanQty,
  VpoChargeDto,
  VpoChargeOnDocDto,
  VpoOverrideItemCsDto,
  VpoItemDto,
  VpoOverrideShipDtlCsDto,
  VpoOverrideShipDtlDto,
  VpoChainOfCustodyDto,
  VpoDocumentState,
} from '../model/vpo-dto';
import { Common_CustomTableDto, Vpo_VpoShareFileDto, Vpo_VpoShipDtlDto } from 'src/app/entities/api';
import { FieldDefine } from 'src/app/entities/form-field';
import { MessageDialogService } from 'src/app/services/message-dialog.service';
import { DomainAttributeService } from 'src/app/workspace/service/domain-attribute.service';
import { DOMAIN_ATTRIBUTES } from 'src/app/workspace/service/domain-attributes';

@UntilDestroy()
@Injectable()
export class VpoDynamicDataService extends DocumentDynamicDataService {
  VpoItemCsMap = new Map();
  private readonly documentService = inject(DocumentService);
  protected readonly documentDefineQuery = inject(DocumentDefineQuery);
  protected readonly sectionsQuery = inject(SectionsQuery);
  protected readonly documentStatusService = inject(DocumentStatusService);
  private readonly dataTransformService = inject(DataTransformService);
  private readonly domainAttributeService = inject(DomainAttributeService);

  filterDataFunctionMap = {
    vpoShareFileList: (data: Vpo_VpoShareFileDto[]) => this.filterActiveShareFile(data),
  };
  isVpoItemCsDefaultNoRow: boolean =
    this.domainAttributeService.state[DOMAIN_ATTRIBUTES.VPO_ITEM_CS_DEFAULT_NO_ROW]?.toLowerCase() === 'true';
  isVpoCocItemLevel: boolean = this.domainAttributeService.state[DOMAIN_ATTRIBUTES.VPO_COC_LEVEL] === 'vpoItem';

  customizeAfterGirdValueChangeActionMap = {
    vpoItemList: {
      qtyPerExportCarton: this.calculateNoOfCartonsForVpoItemList.bind(this),
      planedQty: this.calculateNoOfCartonsForVpoItemList.bind(this),
      l: this.calculateCbmAndOuterCartonCFTForVpoItemList.bind(this),
      w: this.calculateCbmAndOuterCartonCFTForVpoItemList.bind(this),
      h: this.calculateCbmAndOuterCartonCFTForVpoItemList.bind(this),
      itemColorsList: this.buildVpoItemCsList.bind(this),
      itemSizesList: this.buildVpoItemCsList.bind(this),
    },

    vpoChainOfCustodyList: {
      chainOfCustodySupplier: this.handleChainOfCustodySupplierRemoved.bind(this),
    },
  };

  private readonly headerFactoryRef$ = this.documentDataQuery.select('headerFactory').pipe(
    map((headerFactory: VpoDocumentState['headerFactory']) => headerFactory?.factCode ?? null),
    distinctUntilChanged(deepEqual),
  );

  private readonly dataInitializing$ = this.documentDataQuery
    .selectDataInitialized$()
    .pipe(filter((dataInitialized) => !dataInitialized));

  private readonly statusChanged$ = this.documentStatusService.getDocumentStatus$();

  private readonly docNotEditing$ = this.documentStatusService
    .getDocumentStatus$()
    .pipe(filter((status) => status !== DocumentStatus.Edit));

  private readonly docEditing$ = this.documentStatusService.getDocumentStatus$().pipe(
    filter((status) => status === DocumentStatus.Edit),
    untilDestroyed(this),
    shareReplay(1),
  );

  private readonly vpoItemList$: Observable<VpoDocumentState['vpoItemList']> = this.selectData$('vpoItemList').pipe(
    distinctUntilChanged(deepEqual),
    untilDestroyed(this),
    shareReplay(1),
  );

  private readonly vpoShipDtlDtoGroupList$: Observable<VpoDocumentState['vpoShipDtlDtoGroupList']> = this.selectData$(
    'vpoShipDtlDtoGroupList',
  ).pipe(distinctUntilChanged(deepEqual), untilDestroyed(this), shareReplay(1));

  private readonly vpoItemListLotChanged$ = this.docEditing$.pipe(
    switchMap(() => this.selectData$('vpoItemList')),
    skip(1),
    distinctUntilChanged((previous, current) => {
      const previousLotList = previous.map((vpoItem) => vpoItem.lotNo);
      const currentLotList = current.map((vpoItem) => vpoItem.lotNo);
      return equal(previousLotList, currentLotList);
    }),
    map((vpoItemList) =>
      vpoItemList.map((vpoItem) => {
        const refNo = `${vpoItem.itemNo}-${vpoItem.lotNo}`;
        const vpoItemCsList = this.getVpoItemCsList(vpoItem.refNo);
        return { ...vpoItem, itemLotNo: refNo, vpoItemCsList };
      }),
    ),
  );

  vpoItemDesciption$ = this.selectData$('vpoItemList')
    .pipe(filter(isNotEmptyOrNil))
    .subscribe((vpoItemList: any[]) => {
      const { vpoShipDtlDtoGroupList } = this.documentDataQuery.getValue();
      const vpoItemMap = new Map(vpoItemList.map((item) => [item.treePath, item]));
      const updatedVpoShipDtlDtoGroupList = vpoShipDtlDtoGroupList.map((vposhipDtlGroup) => {
        const updatedVposhipDtls = vposhipDtlGroup.map((vposhipDtl) => {
          const matchedVpoItem = vpoItemMap.get(vposhipDtl.treePath);
          const itemId = vposhipDtl.vpoItemId;
          if (matchedVpoItem) {
            const updatedItemId = { ...itemId, itemDesc: matchedVpoItem.itemDesc };
            return {
              ...vposhipDtl,
              vpoItemId: updatedItemId,
            };
          }
          return vposhipDtl;
        });
        return updatedVposhipDtls;
      });
      this.documentDataService.updateData({
        vpoShipDtlDtoGroupList: updatedVpoShipDtlDtoGroupList,
      });
    });

  private readonly dynamicVPOShipDtlGroupListLotChanged$ = this.vpoItemListLotChanged$.pipe(
    map((vpoItemList) => {
      const documentData = this.documentDataQuery.getValue() as unknown as VpoDocumentState;
      const vpoShipDtlDtoGroupList = deepClone(documentData.vpoShipDtlDtoGroupList);
      vpoShipDtlDtoGroupList?.map((vpoShipDtlDtoGroup) =>
        vpoShipDtlDtoGroup.map((vpoShipDtlDtoRow) => {
          if (!vpoShipDtlDtoRow.vpoItemId) {
            return vpoShipDtlDtoRow;
          }

          const foundVPOItem = vpoItemList.find((vpoItem) => vpoShipDtlDtoRow.vpoItemRef === vpoItem.refNo);
          vpoShipDtlDtoRow.vpoItemId.lotNo = foundVPOItem.lotNo;
          return vpoShipDtlDtoRow;
        }),
      );
      return vpoShipDtlDtoGroupList;
    }),
  );

  // The `vpoShipDtlCsGroupList` value is always generated in frontend since not provide in document data.
  private readonly dynamicVPOShipDtlCsGroupList$ = combineLatest([
    this.statusChanged$,
    this.vpoShipDtlDtoGroupList$,
    this.vpoItemList$,
  ]).pipe(
    auditTime(0),
    map(([status, vpoShipDtlDtoGroupList, vpoItemList]) => {
      const { vpoItemCsList } = this.documentDataQuery.getValue();
      if (isEmptyOrNil(vpoItemCsList)) {
        return [];
      }

      const documentData = this.documentDataQuery.getValue() as unknown as VpoDocumentState;
      const vpoShipDtlCsGroupList = documentData?.vpoShipDtlCsGroupList;

      const vpoShipDtlCsListMap: Map<string, Common_CustomTableDto> = new Map();
      vpoShipDtlCsGroupList?.forEach((vpoShipDtlCsGroup) => {
        vpoShipDtlCsGroup?.forEach((vpoShipDtlCs) => {
          vpoShipDtlCsListMap.set(vpoShipDtlCs.refNo, vpoShipDtlCs.customFields);
        });
      });

      const newVPOCSGroup: VpoOverrideShipDtlCsDto[][] = [];
      const autoFillVpoShipCsColorSizeQtyEnable =
        this.domainAttributeService.state[DOMAIN_ATTRIBUTES.AUTOFILL_VPOSHIPCS_COLORSIZEQTY_ENABLE] === 'TRUE';
      const itemCSQtyMap = new Map();
      if (
        autoFillVpoShipCsColorSizeQtyEnable &&
        vpoShipDtlDtoGroupList?.flat().filter((vpoShipDtlDto) => vpoShipDtlDto?.vpoItemId).length !==
          vpoShipDtlCsGroupList?.length
      ) {
        const { vpoShipDtlCsDto } = this.documentDataQuery.getValue();
        const vicdQtyMap = vpoShipDtlCsDto?.reduce((acc, vicd) => {
          const key = `${vicd.itemLotNo}-${vicd.colorSeq}-${vicd.vpoItemSizeRef}`;
          acc.set(key, (acc.get(key) || 0) + (vicd?.colorSizeQty || 0));
          return acc;
        }, new Map());
        vpoItemCsList.forEach((VpoItemCss) => {
          VpoItemCss.forEach((vpoItemCs) => {
            const key = `${vpoItemCs.itemLotNo}-${vpoItemCs.colorSeq}-${vpoItemCs.vpoItemSizeRef}`;
            const existsPlannedQty = vicdQtyMap.get(key) || 0;
            const plannedQty = vpoItemCs?.plannedQty || 0;
            itemCSQtyMap.set(key, Math.max(0, plannedQty - existsPlannedQty));
          });
        });
      }
      if (vpoShipDtlDtoGroupList?.length === 0) {
        this.vpoShipDtlCsRatioAndQtySubject.next({});
      }

      // vpoShipDtlDtoList
      vpoShipDtlDtoGroupList?.forEach((vpoShipDtlDtoGroup) => {
        // vpoShipDtlDto
        let packDesc;
        vpoShipDtlDtoGroup?.forEach((vpoShipDtlDtoRow) => {
          const newVPOCSRowList: VpoOverrideShipDtlCsDto[] = [];
          if (!vpoShipDtlDtoRow.vpoItemId) {
            return;
          }

          const foundVPOItem = vpoItemList.find((vpoItem: VpoItemDto) => vpoItem.refNo === vpoShipDtlDtoRow.vpoItemRef);
          const foundVpoItemCsbefore =
            vpoItemCsList.find((vpoItemCsGroup) =>
              vpoItemCsGroup.find((vpoItemCs) => vpoItemCs.refNo === foundVPOItem.refNo),
            ) || [];
          const foundVpoItemCsList =
            vpoItemList
              .map((vpoItem) =>
                foundVpoItemCsbefore.filter((itemCs) => {
                  const colorNameList = [];
                  const sizeNameList = [];
                  vpoItem.itemColorsList?.forEach((itemColor) => {
                    colorNameList.push(itemColor.shortName);
                  });
                  vpoItem.itemSizesList?.forEach((itemSize) => {
                    sizeNameList.push(itemSize.sizeDisplayName);
                  });

                  if (colorNameList.length === 0 && sizeNameList.length === 0) {
                    return true;
                  }

                  if (sizeNameList.length === 0) {
                    // colorNameList not empty , display select color + all size
                    if (colorNameList.includes(itemCs.vpoItemColorRef)) {
                      return true;
                    }
                  }

                  if (colorNameList.length === 0) {
                    // sizeNameList not empty, display select size + all color
                    if (sizeNameList.includes(itemCs.vpoItemSizeRef)) {
                      return true;
                    }
                  }

                  // display all select color + all select size
                  return colorNameList.includes(itemCs.vpoItemColorRef) && sizeNameList.includes(itemCs.vpoItemSizeRef);
                }),
              )
              .find((itemCsList) => itemCsList.find((csList) => csList.refNo === foundVPOItem.refNo)) || [];
          if (foundVpoItemCsList.length === 0) {
            packDesc = foundVPOItem.itemDesc;
          }
          const isPlannedqtyOrderqtyControl = this.getIsPlannedqtyOrderqtyControl();

          // vpoItemCsList
          foundVpoItemCsList.forEach((vpoCSData: VpoOverrideItemCsDto, idx: number) => {
            const foundItemCS =
              this.vpoShipDtlCsRatioAndQtySubject.getValue()?.[vpoShipDtlDtoRow.vpoShipId?.refNo]?.[vpoCSData.refNo];
            const foundItemCSPriceAndPlanQty = foundItemCS?.[idx];
            let innerCasePack = foundItemCSPriceAndPlanQty?.innerCasePack;
            const isPackItem = !!vpoShipDtlDtoRow?.treePath.includes('::');
            if (!innerCasePack && isPackItem) {
              innerCasePack = vpoCSData.innerCasePack;
            }
            const refNo = `${vpoShipDtlDtoRow.shipmentNo}/${vpoShipDtlDtoRow.vpoItemRef}/${vpoCSData.vpoItemColorId.shortName}/${vpoCSData.vpoItemSizeRef}`;
            let colorSizeQty = null;
            let ratio = null;
            let shipmentBarcode = null;
            vpoShipDtlCsGroupList?.forEach((vpoShipDtlCsDtoList) => {
              vpoShipDtlCsDtoList.forEach((vpoShipDtlCs) => {
                if (
                  vpoShipDtlCs.itemLotNo === vpoCSData.itemLotNo &&
                  vpoShipDtlCs.colorSeq === vpoCSData.colorSeq &&
                  vpoShipDtlCs.vpoItemColorRef === vpoCSData.vpoItemColorRef &&
                  vpoShipDtlCs.shipmentNo === vpoShipDtlDtoRow.shipmentNo &&
                  vpoShipDtlCs.vpoItemSizeRef === vpoCSData.vpoItemSizeRef
                ) {
                  colorSizeQty = vpoShipDtlCs.colorSizeQty;
                  ratio = vpoShipDtlCs.ratio;
                  shipmentBarcode = vpoShipDtlCs.shipmentBarcode;
                }
              });
            });
            const find = documentData.vpoShipDtlCsDto?.find(
              (vicd) =>
                vicd.itemLotNo === vpoCSData.itemLotNo &&
                vicd.colorSeq === vpoCSData.colorSeq &&
                vicd.vpoItemColorRef === vpoCSData.vpoItemColorRef &&
                vicd.shipmentNo === vpoShipDtlDtoRow.shipmentNo &&
                vicd.vpoItemSizeRef === vpoCSData.vpoItemSizeRef,
            );
            if (vpoShipDtlDtoRow.isPackUpdateField) {
              colorSizeQty = innerCasePack * vpoShipDtlDtoRow.noOfInner;
            }

            const findRow = vpoShipDtlDtoRow?.vpoShipDtlCsDto?.find(
              (vsdr) =>
                vsdr.itemLotNo === vpoCSData.itemLotNo &&
                vsdr.colorSeq === vpoCSData.colorSeq &&
                vsdr.shipmentNo === vpoShipDtlDtoRow.shipmentNo &&
                vsdr.sizeSeq === vpoCSData.sizeSeq,
            );

            if (autoFillVpoShipCsColorSizeQtyEnable && itemCSQtyMap.size !== 0 && isEmptyOrNil(colorSizeQty)) {
              const key = `${vpoCSData?.itemLotNo}-${vpoCSData?.colorSeq}-${vpoCSData?.vpoItemSizeRef}`;
              colorSizeQty = itemCSQtyMap.get(key) || 0;
              itemCSQtyMap.set(key, 0);
            }
            const newVPOCSRow: VpoOverrideShipDtlCsDto = {
              id: foundItemCSPriceAndPlanQty?.shipDtlCsId || generateUUID(),
              internalSeqNo: findRow ? findRow.internalSeqNo : null,
              seqNo: findRow ? findRow.seqNo : null,
              duid: idx.toString(),
              colorSeq: vpoCSData.colorSeq,
              sizeUniqueKey: vpoCSData.sizeUniqueKey,
              sizeSeq: vpoCSData.sizeSeq,
              itemLotNo: vpoCSData.itemLotNo,
              shipmentNo: vpoShipDtlDtoRow.shipmentNo,
              refNo,
              vpoItemRef: vpoShipDtlDtoRow.vpoItemRef,
              itemSizeId: vpoCSData.vpoItemSizeId?.refNo,
              itemColorId: vpoCSData.vpoItemColorId?.refNo,
              itemColor: { hexCode: vpoCSData.vpoItemColorId.hexCode, image: vpoCSData.vpoItemColorId.image },
              vpoItemId: vpoShipDtlDtoRow.vpoItemId,
              vpoShipRef: vpoShipDtlDtoRow.vpoShipId?.refNo,
              vpoShipId: vpoShipDtlDtoRow.vpoShipId,
              vpoItemColorRef: vpoCSData.vpoItemColorRef,
              vpoItemSizeRef: vpoCSData.vpoItemSizeRef,
              skuNo: vpoCSData.skuNo,
              upc: vpoCSData.upc,
              ean: vpoCSData.ean,
              colorSizeQty: isPlannedqtyOrderqtyControl
                ? 0
                : isPackItem
                ? colorSizeQty
                : find
                ? find.colorSizeQty
                : colorSizeQty,
              ratio: isPackItem ? vpoCSData.outerCasePack : find ? find.ratio : ratio,
              innerCasePack: innerCasePack || null,
              packNo: vpoCSData.packNo,
              packDesc,
              customFields: vpoShipDtlCsListMap.get(refNo),
              shipmentBarcode: find ? find.shipmentBarcode : shipmentBarcode,
              isMultiPack: vpoCSData?.isMultiPack,
            } as VpoOverrideShipDtlCsDto;
            if (!autoFillVpoShipCsColorSizeQtyEnable && vpoShipDtlDtoRow.qty === undefined) {
              newVPOCSRow.colorSizeQty = undefined;
              newVPOCSRow.ratio = undefined;
            }
            let isDisplay = true;
            if (
              status === DocumentStatus.View &&
              (newVPOCSRow.colorSizeQty === undefined || isNil(newVPOCSRow.colorSizeQty))
            ) {
              isDisplay = false;
            }
            if (isDisplay) {
              newVPOCSRowList.push(newVPOCSRow);
            }
          });

          if (!R.isEmpty(newVPOCSRowList)) {
            newVPOCSGroup.push(newVPOCSRowList);
          }
        });
      });
      return newVPOCSGroup;
    }),
  );

  private readonly dynamicVPOItemCsList$ = combineLatest([
    this.statusChanged$,
    this.vpoItemList$,
    this.dynamicVPOShipDtlCsGroupList$,
  ]).pipe(
    auditTime(0),
    map(([status, vpoItemList, vpoShipDtlCsGroupList = []]) =>
      vpoItemList
        .map((vpoItem) =>
          this.getVpoItemCsList(vpoItem.refNo)
            .filter((vpoItemCsList: VpoOverrideItemCsDto) => {
              const colorNameList = [];
              const sizeNameList = [];
              vpoItem.itemColorsList?.forEach((itemColor) => {
                colorNameList.push(itemColor.shortName);
              });
              vpoItem.itemSizesList?.forEach((itemSize) => {
                sizeNameList.push(itemSize.sizeDisplayName);
              });

              let isDisplay = false;

              if (status === DocumentStatus.Edit || isNotNil(vpoItemCsList.price)) {
                isDisplay = true;
              }

              if (status === DocumentStatus.View && (vpoItemCsList.price === undefined || isNil(vpoItemCsList.price))) {
                vpoShipDtlCsGroupList?.forEach((vpoShipDtlCsGroup) => {
                  vpoShipDtlCsGroup?.forEach((vpoShipDtlCs) => {
                    if (
                      vpoItemCsList.vpoItemColorRef === vpoShipDtlCs.vpoItemColorRef &&
                      vpoItemCsList.itemLotNo === vpoShipDtlCs.itemLotNo &&
                      vpoItemCsList.sizeUniqueKey === vpoShipDtlCs.sizeUniqueKey &&
                      vpoShipDtlCs.colorSizeQty > 0
                    ) {
                      isDisplay = true;
                    }
                  });
                });
              }

              if (isDisplay) {
                if (!this.isVpoItemCsDefaultNoRow && colorNameList.length === 0 && sizeNameList.length === 0) {
                  return true;
                }

                if (!this.isVpoItemCsDefaultNoRow && sizeNameList.length === 0) {
                  // colorNameList not empty , display select color + all size
                  if (colorNameList.includes(vpoItemCsList.vpoItemColorRef)) {
                    return true;
                  }
                }

                if (!this.isVpoItemCsDefaultNoRow && colorNameList.length === 0) {
                  // sizeNameList not empty, display select size + all color
                  if (sizeNameList.includes(vpoItemCsList.vpoItemSizeRef)) {
                    return true;
                  }
                }

                // display all select color + all select size
                return (
                  colorNameList.includes(vpoItemCsList.vpoItemColorRef) &&
                  sizeNameList.includes(vpoItemCsList.vpoItemSizeRef)
                );
              }
              return false;
            })
            .map((vpoItemCsList: VpoOverrideItemCsDto) => {
              const vpoItemCSPriceAndPlanQty = this.vpoItemCSPriceAndPlanQtySubject.getValue();
              const foundItemCSPriceAndPlanQty = vpoItemCSPriceAndPlanQty[vpoItem.refNo]?.find(
                (vicd) =>
                  vicd.itemLotNo === vpoItemCsList.itemLotNo &&
                  vicd.colorSeq === vpoItemCsList.colorSeq &&
                  vicd.vpoItemColorRef === vpoItemCsList.vpoItemColorRef &&
                  vicd.vpoItemSizeRef === vpoItemCsList.vpoItemSizeRef,
              );
              const isPlannedqtyOrderqtyControl = this.getIsPlannedqtyOrderqtyControl();
              let plannedQty = vpoItemCsList.plannedQty || null;
              let price = vpoItemCsList.price || null;
              let innerCasePack = vpoItemCsList.innerCasePack || null;
              let outerCasePack = vpoItemCsList.outerCasePack || null;
              if (foundItemCSPriceAndPlanQty) {
                price = foundItemCSPriceAndPlanQty.price;
                innerCasePack = foundItemCSPriceAndPlanQty.innerCasePack;
                outerCasePack = foundItemCSPriceAndPlanQty.outerCasePack;
              }
              const documentData = this.documentDataQuery.getValue() as unknown as VpoDocumentState;
              const find = documentData.vpoItemCsDto.find(
                (vicd) =>
                  vicd.itemLotNo === vpoItemCsList.itemLotNo &&
                  vicd.colorSeq === vpoItemCsList.colorSeq &&
                  vicd.vpoItemColorRef === vpoItemCsList.vpoItemColorRef &&
                  vicd.vpoItemSizeRef === vpoItemCsList.vpoItemSizeRef,
              );
              let packDesc;
              if (vpoItemCsList.packNo) {
                packDesc = vpoItemList.find((vpoItemPack) => vpoItemPack.treePath === vpoItemCsList.packNo)?.itemDesc;
              }

              if (vpoItem.isPackUpdateField) {
                plannedQty = innerCasePack * vpoItem.noOfInner;
              }
              return {
                ...vpoItemCsList,
                id: foundItemCSPriceAndPlanQty?.vpoItemCsId || generateUUID(),
                refNo: vpoItem.refNo,
                lotNo: vpoItem.lotNo,
                itemDesc: vpoItem.itemDesc,
                defaultPrice: vpoItem.sellPrice,
                price: price === undefined ? find?.price : price,
                plannedQty: isPlannedqtyOrderqtyControl
                  ? null
                  : vpoItem.isPackUpdateField
                  ? plannedQty
                  : find?.plannedQty ?? plannedQty,
                innerCasePack,
                outerCasePack,
                packDesc,
              } as VpoOverrideItemCsDto;
            }),
        )
        .filter((vpoItem) => !R.isEmpty(vpoItem)),
    ),
  );

  private readonly vpoChargeListOptionsChanged$ = this.vpoItemList$.pipe(
    debounceTime(100),
    skip(1),
    map((vpoItemList) => {
      // handle deleted vpoItemList and update vpoItemCsList
      const documentData = this.documentDataQuery.getValue() as unknown as VpoDocumentState;
      return documentData.vpoChargeList?.map((vpoCharge) => {
        const foundItem = vpoItemList.find((vpoItem) => vpoCharge.vpoItemId?.code === vpoItem.refNo);
        return foundItem ? vpoCharge : { ...vpoCharge, vpoItemId: undefined, chargeValue: 0, chargeAmt: 0 };
      });
    }),
  );

  // Calculation
  private readonly totalAmount$ = new BehaviorSubject<number>(0);
  private readonly vpoItemTotalAmountGroupByItem$ = new BehaviorSubject<AnyObject<{ totalAmt: number }>>({});
  readonly vpoItemCSPriceAndPlanQtySubject = new BehaviorSubject<AnyObject<ItemCSPriceAndPlanQty[]>>({});
  private readonly vpoItemCSPriceAndPlanQty$ = this.vpoItemCSPriceAndPlanQtySubject.pipe(
    distinctUntilChanged(deepEqual),
  );
  readonly shipGroupDtlPercentage$ = new BehaviorSubject<AnyObject<VpoOverrideShipDtlDto>>({});

  private readonly vpoShipDtlCsRatioAndQtySubject = new BehaviorSubject<
    AnyObject<AnyObject<VpoOverrideShipDtlCsDto[]>>
  >({});
  private readonly vpoShipDtlCsRatioAndQty$ = this.vpoShipDtlCsRatioAndQtySubject.pipe(distinctUntilChanged(deepEqual));

  private readonly vpoItemCalculationStream$ = combineLatest([
    this.vpoItemCSPriceAndPlanQty$,
    this.vpoShipDtlCsRatioAndQty$,
    this.vpoShipDtlDtoGroupList$.pipe(startWith([])),
    this.vpoItemList$,
    this.dynamicVPOItemCsList$,
  ]).pipe(
    distinctUntilChanged(deepEqual),
    debounceTime(500),
    filter(([, , , vpoItemList]) => vpoItemList.length > 0),
    map(([vpoItemCSPriceAndPlanQty, vpoShipDtlCsRatioAndQty, vpoShipDtlDtoGroupList, vpoItemList, itemCsLists]) => {
      let newVpoItemList = vpoItemList?.map((vpoItem) => {
        const colorSizeList = vpoItemCSPriceAndPlanQty[vpoItem.refNo];
        const targetIndex = itemCsLists.findIndex((itemCsList) =>
          itemCsList.find((data) => data.refNo === vpoItem.refNo),
        );
        const vpoItemCsList = itemCsLists[targetIndex];
        let allocatedQty = 0;
        let noOfCartons = 0;
        let packUnit;

        // Item does not has CS data
        if (!colorSizeList) {
          if (!vpoItem.itemId) {
            return vpoItem;
          }
          const itemRef = vpoItem.refNo;
          vpoShipDtlDtoGroupList.forEach((vpoShipDtlGroup: VpoOverrideShipDtlDto[]) => {
            const foundElement = vpoShipDtlGroup.find((vpoShipDtlRow) => vpoShipDtlRow.vpoItemRef === itemRef);
            if (!foundElement) {
              allocatedQty += 0;
              noOfCartons += 0;
              return;
            }
            allocatedQty += foundElement.qty;
            noOfCartons += foundElement.noOfCartons;
            packUnit = foundElement.packUnit;
          });

          return {
            ...vpoItem,
            shipQty: allocatedQty,
            noOfCartons,
            qtyPerExportCarton: packUnit,
            averageAmt: vpoItem.sellPrice,
            variance: vpoItem.planedQty - vpoItem.shipQty,
            totalAmt: vpoItem.averageAmt * vpoItem.shipQty,
          };
        }

        const itemCSGroup: VpoOverrideShipDtlCsDto[][] = [];
        Object.keys(vpoShipDtlCsRatioAndQty).forEach((shipRef) => {
          const vpoItemCSGroup = vpoShipDtlCsRatioAndQty[shipRef];
          const vpoItemCSList = vpoItemCSGroup[vpoItem.refNo];
          if (!vpoItemCSList) {
            return;
          }
          itemCSGroup.push(
            vpoItemCSList.filter(
              ({ shipmentStatus }: any) =>
                !this.dataTransformService.getShipmentStatus(shipmentStatus?.code)?.custCheckbox1,
            ),
          );
        });

        let colorSizeQtySum = 0;
        let colorSizeQtySumMap = {};
        itemCSGroup.forEach((itemCs) => {
          itemCs.forEach((element) => {
            const { colorSizeQty } = element;
            colorSizeQtySum += colorSizeQty || 0;
            const colorSizeQtySumMapElement =
              colorSizeQtySumMap[element.itemLotNo + element.colorSeq + element.vpoItemSizeRef];
            colorSizeQtySumMap = {
              ...colorSizeQtySumMap,
              [element.itemLotNo + element.colorSeq + element.vpoItemSizeRef]:
                element.colorSizeQty + (isEmptyOrNil(colorSizeQtySumMapElement) ? 0 : colorSizeQtySumMapElement),
            };
          });
        });

        let vpoItemPlanQtySum = 0;
        let vpoItemOuterCartonSum = 0;
        colorSizeList.forEach((element) => {
          const { plannedQty, outerCasePack } = element;
          vpoItemPlanQtySum += plannedQty || 0;
          vpoItemOuterCartonSum += outerCasePack || 0;
        });

        let price = 0;
        let totalAmount = 0;
        colorSizeList.forEach((calcValue) => {
          // Average Price
          price += calcValue.price || calcValue.defaultPrice;
          const colorSizeQtySumMapElement =
            colorSizeQtySumMap[calcValue.itemLotNo + calcValue.colorSeq + calcValue.vpoItemSizeRef];
          totalAmount += (calcValue.price || calcValue.defaultPrice) * (colorSizeQtySumMapElement || 0);
        });
        totalAmount = totalAmount === 0 ? vpoItem.sellPrice * colorSizeQtySum : totalAmount;

        const itemRef = !vpoItem.refNo ? `${vpoItem.itemId?.itemNo}-Lot${vpoItem.lotNo}` : vpoItem.refNo;
        vpoShipDtlDtoGroupList.forEach((vpoShipDtlGroup: Vpo_VpoShipDtlDto[]) => {
          const foundElement = vpoShipDtlGroup.find((vpoShipDtlRow) => vpoShipDtlRow.vpoItemRef === itemRef);
          if (!foundElement) {
            noOfCartons += 0;
            return;
          }
          noOfCartons += foundElement.noOfCartons;
        });
        return {
          ...vpoItem,
          averageAmt: price / vpoItemCsList.length,
          noOfCartons,
          shipQty: colorSizeQtySum,
          planedQty: this.getIsPlannedqtyOrderqtyControl() ? vpoItem.planedQty : vpoItemPlanQtySum || null,
          qtyPerExportCarton: vpoItemOuterCartonSum,
          variance: vpoItem.planedQty - colorSizeQtySum,
          totalAmt: totalAmount,
        };
      });

      newVpoItemList = newVpoItemList?.map((vpoItem) => {
        if (!vpoItem.itemId) {
          let packShipQty = 0;
          let packVariance = 0;
          let shipDtlNoOfInners = 0;
          vpoShipDtlDtoGroupList.forEach((vpoShipDtlDtoGroup) =>
            vpoShipDtlDtoGroup
              .filter((vpoShipDtlDto) => vpoShipDtlDto.treePath === vpoItem.treePath)
              .forEach((vpoShipDtlDto) => {
                shipDtlNoOfInners += vpoShipDtlDto.noOfInner;
              }),
          );
          newVpoItemList
            .filter((vpoItemPackItem) => vpoItemPackItem.treePath.includes(`${vpoItem.treePath}::`))
            .forEach((vpoItemPackItem) => {
              packShipQty += vpoItemPackItem.shipQty;
              packVariance += vpoItemPackItem.variance;
            });

          return {
            ...vpoItem,
            shipQty: packShipQty,
            variance: packVariance,
            totalAmt: shipDtlNoOfInners * vpoItem.pricePerInner,
          };
        }
        return vpoItem;
      });

      return newVpoItemList;
    }),
  );

  private readonly vpoShipDtlCalculationStream$ = combineLatest([
    this.vpoShipDtlCsRatioAndQty$,
    this.vpoShipDtlDtoGroupList$,
    this.vpoItemList$,
  ]).pipe(
    debounceTime(100),
    skip(1),
    map(([vpoShipDtlCsRatioAndQty, vpoShipDtlDtoGroupList, vpoItemList]) =>
      deepClone(vpoShipDtlDtoGroupList)?.map((vpoShipDtlDtoGroup: VpoOverrideShipDtlDto[]) =>
        vpoShipDtlDtoGroup.map((vpoShipDtlDtoRow) => {
          const cbm = vpoItemList.find((vpoItem) => vpoItem.refNo === vpoShipDtlDtoRow.vpoItemRef)?.cbm;
          const colorSizeList =
            vpoShipDtlCsRatioAndQty?.[vpoShipDtlDtoRow.vpoShipId?.refNo]?.[vpoShipDtlDtoRow.vpoItemRef];

          // When vpoItem does not has any Color/Size data and Pack
          if (!colorSizeList) {
            vpoShipDtlDtoRow.qtyVariance = vpoShipDtlDtoRow.qty - vpoShipDtlDtoRow.originalQty;
            if (vpoShipDtlDtoRow?.vpoItemId?.itemId) {
              vpoShipDtlDtoRow.noOfCartons =
                !!vpoShipDtlDtoRow.packUnit && isNotNil(vpoShipDtlDtoRow.qty)
                  ? Math.round(vpoShipDtlDtoRow.qty / vpoShipDtlDtoRow.packUnit)
                  : null;
              if (vpoShipDtlDtoRow?.treePath?.includes('::')) {
                vpoShipDtlDtoRow.qty = vpoShipDtlDtoRow.innerCasePack * vpoShipDtlDtoRow.noOfInner;
              }
            } else {
              let qty = 0;
              vpoShipDtlDtoGroup
                .filter(
                  (vpoShipDtlDtoRowF) =>
                    vpoShipDtlDtoRowF.treePath &&
                    vpoShipDtlDtoRowF.shipmentNo === vpoShipDtlDtoRow.shipmentNo &&
                    vpoShipDtlDtoRowF.treePath.includes(`${vpoShipDtlDtoRow.treePath}::`),
                )
                .forEach((vpoShipDtlDtoRowF) => {
                  qty += vpoShipDtlDtoRowF.qty || 0;
                });
              vpoShipDtlDtoRow.qty = qty;
            }
            vpoShipDtlDtoRow.totalCbm =
              vpoShipDtlDtoRow.noOfCartons <= 0 || cbm <= 0 ? undefined : vpoShipDtlDtoRow.noOfCartons * cbm;
            return vpoShipDtlDtoRow;
          }

          let packUnit = 0;
          let qty = 0;
          colorSizeList?.forEach((calcValue) => {
            // Pack Unit
            packUnit += calcValue.ratio || 0;
            // Order Qty
            qty += calcValue.colorSizeQty || 0;
          });

          if (this.getIsPlannedqtyOrderqtyControl() && !vpoShipDtlDtoRow.qty) {
            const vpoItem = vpoItemList.filter((vpoItemRow) => vpoItemRow.refNo === vpoShipDtlDtoRow.vpoItemRef);
            if (!vpoItem) {
              qty = 0;
            } else {
              qty = vpoItem[0].planedQty || 0;
            }
          } else if (this.getIsPlannedqtyOrderqtyControl()) {
            qty = vpoShipDtlDtoRow.qty;
          }

          if (this.getIsPlannedqtyOrderqtyControl()) {
            if (!vpoShipDtlDtoRow.packUnit) {
              const vpoItem = vpoItemList.filter((vpoItemRow) => vpoItemRow.refNo === vpoShipDtlDtoRow.vpoItemRef);
              if (!vpoItem) {
                packUnit = vpoItem[0].qtyPerExportCarton;
              }
            } else if (vpoShipDtlDtoRow.packUnit) {
              packUnit = vpoShipDtlDtoRow.packUnit;
            }
          }

          vpoShipDtlDtoRow.packUnit = packUnit;
          vpoShipDtlDtoRow.qty = qty;
          const vpoItem = vpoItemList.filter((vpoItemRow) => vpoItemRow.refNo === vpoShipDtlDtoRow.vpoItemRef);
          vpoShipDtlDtoRow.originalQty = vpoItem[0].planedQty;
          let totalQty = 0;
          vpoShipDtlDtoGroupList.forEach((vpoShipDtlDtos) => {
            vpoShipDtlDtos.forEach((vpoShipDtlDto) => {
              if (vpoShipDtlDto.vpoItemRef === vpoShipDtlDtoRow.vpoItemRef) {
                totalQty += vpoShipDtlDto.qty || 0;
              }
            });
          });

          vpoShipDtlDtoRow.qtyVariance = totalQty - vpoShipDtlDtoRow.originalQty;

          if (!vpoShipDtlDtoRow.treePath.includes('::')) {
            vpoShipDtlDtoRow.noOfCartons =
              !!packUnit && isNotNil(qty) ? Math.round(vpoShipDtlDtoRow.qty / vpoShipDtlDtoRow.packUnit) : null;
          }
          vpoShipDtlDtoRow.totalCbm =
            vpoShipDtlDtoRow.noOfCartons <= 0 || cbm <= 0 ? undefined : vpoShipDtlDtoRow.noOfCartons * cbm;
          return vpoShipDtlDtoRow;
        }),
      ),
    ),
  );

  private readonly vpoChargeListCalculation$ = combineLatest([
    this.selectData$('vpoChargeList'),
    this.vpoItemTotalAmountGroupByItem$,
  ]).pipe(
    auditTime(0),
    distinctUntilChanged(deepEqual),
    map(([vpoChargeList, vpoItemTotalAmount]: [VpoDocumentState['vpoChargeList'], AnyObject<{ totalAmt: number }>]) =>
      vpoChargeList.map((vpoCharge: VpoChargeDto) => {
        const { vpoItemId, calculateType, chargeValue } = vpoCharge;

        if (!vpoItemId || !calculateType || !isNotEmptyOrNil(chargeValue)) {
          return vpoCharge;
        }

        const vpoItemNo = vpoItemId?.code;
        if (!vpoItemNo || !vpoItemTotalAmount[vpoItemNo]) {
          return vpoCharge;
        }

        const foundVpoItemAmount = vpoItemTotalAmount[vpoItemNo].totalAmt;

        if (calculateType?.code === CalculateType.FixedAmount) {
          return { ...vpoCharge, chargeAmt: chargeValue };
        }

        if (calculateType?.code === CalculateType.Percentage) {
          return { ...vpoCharge, chargeAmt: (foundVpoItemAmount * chargeValue) / 100 };
        }

        return vpoCharge;
      }),
    ),
  );

  private readonly vpoDocumentChargeListCalculation$ = combineLatest([
    this.selectData$('vpoChargeOnDocList'),
    this.totalAmount$,
  ]).pipe(
    auditTime(0),
    distinctUntilChanged(deepEqual),
    map(([vpoChargeOnDocList = [], totalAmount]) =>
      vpoChargeOnDocList.map((vpoDocCharge: VpoChargeOnDocDto) => {
        if (vpoDocCharge?.calculateType?.code === CalculateType.FixedAmount) {
          return { ...vpoDocCharge, chargeAmt: vpoDocCharge.chargeValue };
        }

        if (vpoDocCharge?.calculateType?.code === CalculateType.Percentage) {
          return { ...vpoDocCharge, chargeAmt: (totalAmount * vpoDocCharge.chargeValue) / 100 };
        }

        return vpoDocCharge;
      }),
    ),
  );

  private readonly summaryAmount$ = combineLatest([
    this.vpoChargeListCalculation$,
    this.vpoDocumentChargeListCalculation$,
    this.totalAmount$.pipe(distinctUntilChanged(deepEqual)),
  ]).pipe(
    auditTime(0),
    map(([vpoChargeList, vpoDocumentChargeList, totalAmount]) => {
      let finalAmount = totalAmount;
      vpoChargeList.forEach((vpoCharge: VpoChargeDto) => {
        const sign = vpoCharge.chargeType?.code === ChargeType.Charge ? 1 : -1;
        finalAmount += sign * vpoCharge.chargeAmt;
      });

      vpoDocumentChargeList.forEach((vpoDocumentCharge: VpoChargeOnDocDto) => {
        const sign = vpoDocumentCharge.chargeType?.code === ChargeType.Charge ? 1 : -1;
        finalAmount += sign * vpoDocumentCharge.chargeAmt;
      });
      return finalAmount;
    }),
    distinctUntilChanged(),
  );

  private readonly summaryItems$ = this.vpoItemList$.pipe(
    auditTime(0),
    map((vpoItemList) => vpoItemList.filter((vpoItem) => vpoItem.itemType.code !== 'PACK').length),
    distinctUntilChanged(),
  );

  summaryItemItems$ = this.selectData$('vpoItemList').pipe(
    debounceTime(500),
    map((vpoItemList: VpoItemDto[]) => {
      if (vpoItemList.length === 0) {
        return 0;
      }
      let sum = 0;
      vpoItemList.forEach((vpoItem) => {
        if (vpoItem.itemType.code === 'ITEM' && !vpoItem.treePath.includes('::')) sum += 1;
      });
      return sum;
    }),
  );

  private readonly summaryShipments$ = this.vpoShipDtlDtoGroupList$.pipe(
    auditTime(0),
    map((vpoShipDtlDtoGroupList) => vpoShipDtlDtoGroupList.length),
    distinctUntilChanged(),
  );

  summaryTotalPlannedQty$ = this.vpoItemList$.pipe(
    map((vpoItems) => {
      let sum = 0;
      vpoItems.forEach(({ planedQty }) => {
        sum += planedQty ?? 0;
      });
      return sum;
    }),
    distinctUntilChanged(),
  );
  summaryPlannedAmount$ = this.dynamicVPOItemCsList$.pipe(
    map((vpoItemCsList) => {
      let sum = 0;
      vpoItemCsList.forEach((vpoItemCss) => {
        vpoItemCss.forEach(({ price, defaultPrice, plannedQty }) => {
          sum += (price || defaultPrice) * (plannedQty ?? 0);
        });
      });
      return sum;
    }),
    distinctUntilChanged(),
  );

  private readonly summaryTotalQty$ = combineLatest([
    this.vpoItemList$.pipe(distinctUntilChanged(deepEqual)),
    this.vpoShipDtlCsRatioAndQtySubject.pipe(distinctUntilChanged(deepEqual)),
  ]).pipe(
    auditTime(0),
    map(([vpoItemList, vpoShipDtlCsRatioAndQty]) => {
      const vpoItemRefArray = [];
      let sum = 0;
      Object.keys(vpoShipDtlCsRatioAndQty).forEach((shipRef) => {
        const vpoShipDtlCSGroup = vpoShipDtlCsRatioAndQty[shipRef];
        Object.keys(vpoShipDtlCSGroup).forEach((vpoItemRef) => {
          const vpoShipDtlCSList = vpoShipDtlCSGroup[vpoItemRef];
          vpoItemRefArray.push(vpoItemRef);

          vpoShipDtlCSList
            .filter(
              (data: any) => !this.dataTransformService.getShipmentStatus(data.shipmentStatus?.code)?.custCheckbox1,
            )
            .forEach((vpoShipDtlCS) => {
              if (!Number.isNaN(vpoShipDtlCS.colorSizeQty)) {
                sum += vpoShipDtlCS.colorSizeQty ?? 0;
              }
            });
        });
      });
      vpoItemList.forEach((vpoItem) => {
        if (vpoItemRefArray.indexOf(vpoItem.refNo) === -1) {
          if (!Number.isNaN(vpoItem.shipQty)) {
            sum += vpoItem.shipQty ?? 0;
          }
        }
      });
      return sum;
    }),
    distinctUntilChanged(),
  );

  private readonly summaryTotalQtyByShipments$ = this.vpoShipDtlDtoGroupList$.pipe(
    auditTime(0),
    map((vpoShipDtlDtoGroupList) => {
      if (vpoShipDtlDtoGroupList.length === 0) {
        return 0;
      }

      let sum = 0;
      vpoShipDtlDtoGroupList.forEach((vpoShipDtlDtoGroup) => {
        vpoShipDtlDtoGroup.forEach((vpoShipDtlDtoRow) => {
          sum += vpoShipDtlDtoRow.qty || 0;
        });
      });
      return sum;
    }),
    distinctUntilChanged(),
  );

  private readonly summaryTotalCartons$ = this.vpoItemList$.pipe(
    auditTime(0),
    map((vpoItemList: VpoItemDto[]) => {
      let sum = 0;
      vpoItemList
        .filter((vpoItem) => !vpoItem.treePath?.includes('::'))
        .forEach((vpoItem) => {
          sum += vpoItem.noOfCartons || 0;
        });
      return sum;
    }),
    distinctUntilChanged(),
  );

  private readonly chainOfCustodyOverview$ = this.selectData$('vpoChainOfCustodyList').pipe(
    auditTime(0),
    map((chainOfCustodyList: VpoDocumentState['vpoChainOfCustodyList']) => {
      if (isEmptyOrNil(chainOfCustodyList)) {
        return null;
      }

      const priorityList = ['Requested', 'Submitted', 'Rejected', 'Approved'];

      let status = null;
      let currentIndex = 0;

      while (currentIndex < priorityList.length) {
        const priority = priorityList[currentIndex];
        const chainOfCustody = chainOfCustodyList.find((row) => row.chainOfCustodyStatus?.name === priority);

        if (isEmptyOrNil(status) && isNotEmptyOrNil(chainOfCustody)) {
          status = chainOfCustody.chainOfCustodyStatus;
          break;
        }
        currentIndex += 1;
      }

      return status;
    }),
    distinctUntilChanged(),
  );

  private calculateNoOfCartonsForVpoItemList(sectionId: string, currentRowData: VpoItemDto, rowIndex: number) {
    const { qtyPerExportCarton, planedQty, itemId } = currentRowData;
    if (itemId) {
      const value =
        !qtyPerExportCarton || isNil(planedQty)
          ? null
          : new Decimal(planedQty).div(qtyPerExportCarton).round().toNumber();

      const result = R.assocPath(['noOfCartons'], value, currentRowData);

      this.documentDataService.updateRowValueByIndex(sectionId, rowIndex, result);
      this.documentDataService.dynamicRowChanges$.next({
        changes: [
          {
            sectionId,
            changeType: 'update',
            rowId: result.id,
            rowIndex,
            content: result,
          },
        ],
      });
    }
  }

  constructor(
    private readonly documentValueChangeService: DocumentValueChangeService,
    private readonly messageDialogService: MessageDialogService,
    private readonly documentGridApiService: DocumentGridApiService,
  ) {
    super();
    this.documentValueChangeService.confirmBeforeValueChange = {
      vpoItemList: {
        itemColorsList: this.popupIfDeleteItemColorsOrItemSizes.bind(this),
        itemSizesList: this.popupIfDeleteItemColorsOrItemSizes.bind(this),
        factId: this.popupIfChangeVpoItemFact.bind(this),
      },
    };
  }

  private buildVpoItemCsList(sectionId: string, currentRowData: VpoItemDto, rowIndex: number) {
    const { itemNo, lotNo, vpoItemCsList } = currentRowData;
    const cacheKey = `${itemNo}_${lotNo}`;
    of(this.VpoItemCsMap.get(cacheKey) ?? [])
      .pipe(
        switchMap((vpoItemCsFromMap: any[]) =>
          isNotEmptyOrNil(vpoItemCsFromMap)
            ? of(this.replaceVpoItemCs(vpoItemCsList, vpoItemCsFromMap))
            : this.documentService
                .getLineItems(
                  'vpo',
                  null,
                  'VpoSelectItemColorAction',
                  [currentRowData] as any,
                  this.documentDataQuery.getValue(),
                )
                .pipe(
                  tap((response) => this.VpoItemCsMap.set(cacheKey, response.vpoItemCsDto)),
                  map((response) => this.replaceVpoItemCs(vpoItemCsList, response.vpoItemCsDto)),
                  catchError(() => of(vpoItemCsList)),
                ),
        ),
        tap((updatedList) => {
          this.documentDataService.updateRowValueByIndex(sectionId, rowIndex, {
            ...currentRowData,
            vpoItemCsList: updatedList,
          });
        }),
        take(1),
        untilDestroyed(this),
      )
      .subscribe();
  }

  private replaceVpoItemCs(existsVpoItemCss: any[], completedVpoItemCss: any[]): any[] {
    return (
      completedVpoItemCss?.map(
        (targetCs) =>
          existsVpoItemCss.find(
            (sourceCs) =>
              sourceCs.vpoItemColorRef === targetCs.vpoItemColorRef &&
              sourceCs.itemLotNo === targetCs.itemLotNo &&
              sourceCs.vpoItemSizeRef === targetCs.vpoItemSizeRef,
          ) ?? targetCs,
      ) ?? existsVpoItemCss
    );
  }

  private popupIfDeleteItemColorsOrItemSizes(value: any, field: FieldDefine, rowIndex: number): Observable<boolean> {
    const hasReduction = (currentList: any[], previousList: any[], vpoItemCss: any[]) =>
      (this.isVpoItemCsDefaultNoRow || previousList?.length !== 1 || currentList?.length !== 0) &&
      ((!this.isVpoItemCsDefaultNoRow &&
        isEmptyOrNil(previousList) &&
        currentList?.length !==
          new Set(
            vpoItemCss
              ?.map((vpoItemCs) => vpoItemCs[`vpoItem${field.id === 'itemSizesList' ? 'Size' : 'Color'}Ref`])
              .filter(isNotEmptyOrNil) ?? [],
          ).size) ||
        !previousList?.every(
          (previousItem) =>
            !!currentList?.find(
              (item) =>
                (item.sizeDisplayName ?? item.shortName) === (previousItem.sizeDisplayName ?? previousItem.shortName),
            ),
        ));

    const { vpoItemList: { [rowIndex]: currentVpoItem } = [], vpoItemCsList: { [rowIndex]: vpoItemCss } = [] } =
      this.documentDataQuery.getValue();
    return isNotEmptyOrNil(vpoItemCss) && hasReduction(value, currentVpoItem[field.id], vpoItemCss)
      ? this.messageDialogService
          .openYesNoDialog(
            'Removing the Color or Size on the current table will remove all relate data in the "Items by Color/Size" table below once you save this VPO. Are you sure to continue?',
            'warning',
          )
          .closed.pipe(
            map((result) => result?.type === 'done'),
            take(1),
          )
      : of(true);
  }

  private popupIfChangeVpoItemFact(value: any, field: FieldDefine, rowIndex: number): Observable<boolean> {
    if (this.isVpoCocItemLevel) {
      const cocSupplierLabel =
        this.documentGridApiService
          .getGridApi('vpoChainOfCustodyList')
          ?.getColumns()
          ?.find((column) => column.getColId() === 'chainOfCustodySupplier')
          ?.getColDef().headerName ?? 'Supplier';

      return this.messageDialogService
        .openYesNoDialog(
          `Reselecting the Factory will remove related ${cocSupplierLabel} on the Chain of Custody section below. Are you sure to continue?`,
          'warning',
        )
        .closed.pipe(
          tap((result) => {
            if (result?.type === 'done') {
              this.clearRelateCocSupplier(rowIndex);
            }
          }),
          map((result) => result?.type === 'done'),
          take(1),
        );
    }
    return of(true);
  }

  private clearRelateCocSupplier(rowIndex: number) {
    const itemRefNo = this.documentDataQuery.getValue().vpoItemList[rowIndex]?.itemId?.refNo;
    const fieldsToUpdate = ['chainOfCustodySupplier', 'countryOfOrigin', 'city', 'supplierRemarks'];

    this.documentDataQuery
      .getValue()
      .vpoChainOfCustodyList.filter(
        (item) => item.itemRefNo === itemRefNo && isNotEmptyOrNil(item.chainOfCustodySupplier),
      )
      .forEach((item) => {
        fieldsToUpdate.forEach((field) => {
          this.documentDataService.updateCellValueByFieldId(null, 'vpoChainOfCustodyList', null, field, item.id);
        });
      });
  }

  private calculateCbmAndOuterCartonCFTForVpoItemList(sectionId: string, currentRowData: VpoItemDto, rowIndex: number) {
    const { l, w, h } = currentRowData;

    const dimensionUOM = dotPath('dimensionUOM', currentRowData);

    const cbm = allHasValue([l, w, h])
      ? new Decimal(l)
          .mul(w)
          .mul(h)
          .mul(this.dataTransformService.getGetUOMConversionRate(dimensionUOM, 'M'))
          .toNumber()
      : null;

    const outerCartonCFT = allHasValue([l, w, h])
      ? new Decimal(l)
          .mul(w)
          .mul(h)
          .mul(this.dataTransformService.getGetUOMConversionRate(dimensionUOM, 'INCH'))
          .div(1728)
          .toNumber()
      : null;

    const result = { ...currentRowData, cbm, outerCartonCFT };

    this.documentDataService.updateRowValueByIndex(sectionId, rowIndex, result);
    this.documentDataService.dynamicRowChanges$.next({
      changes: [
        {
          sectionId,
          changeType: 'update',
          rowId: result.id,
          rowIndex,
          content: result,
        },
      ],
    });
  }

  private handleChainOfCustodySupplierRemoved(
    sectionId: string,
    currentRowData: VpoChainOfCustodyDto,
    rowIndex: string,
  ) {
    let result = isEmptyOrNil(currentRowData.chainOfCustodySupplier)
      ? { ...currentRowData, countryOfOrigin: null, city: null }
      : currentRowData;

    if (currentRowData.chainOfCustodySupplier) {
      const rows = this.documentDataQuery.getValue()[sectionId] as VpoChainOfCustodyDto[];
      const row = rows.find(
        (r, index) =>
          index !== +rowIndex &&
          r.chainOfCustodySupplier?.id &&
          r.chainOfCustodySupplier.id === currentRowData.chainOfCustodySupplier.id,
      );
      if (row) {
        result = { ...result, countryOfOrigin: row.countryOfOrigin, city: row.city };
      }
    }

    this.documentDataService.updateRowValueByIndex(sectionId, +rowIndex, result);
    this.documentDataService.dynamicRowChanges$.next({
      changes: [
        {
          sectionId,
          changeType: 'update',
          rowId: result.id,
          rowIndex: +rowIndex,
          content: result,
        },
      ],
    });
  }

  private handleAlertChangeDetection() {
    this.documentDataQuery
      .select('headerFactory')
      .pipe(debounceTime(100), skip(1), distinctUntilChanged(deepEqual), untilDestroyed(this))
      .subscribe(() => {
        this.alertChangeDetection$.next();
      });
  }

  private filterActiveShareFile(sectionData: Vpo_VpoShareFileDto[]) {
    return of(
      sectionData?.filter(
        (row) =>
          row.shareFile.docStatus !== 'inactive' &&
          row.shareFile.docStatus !== 'canceled' &&
          !row.shareFile?.dataInVisible &&
          (isEmptyOrNil(row.shareFile?.dataInaccessiable) || !row.shareFile?.dataInaccessiable),
      ) ?? [],
    );
  }

  private handleShareFileList(shareFileListFieldId: string) {
    this.documentDataQuery
      .select(shareFileListFieldId)
      .pipe(
        filter(isNotEmptyOrNil),
        map((shareFileList) =>
          shareFileList?.filter(
            (shareFileRefLine: any) => shareFileRefLine.shareFile.refNo && isNil(shareFileRefLine.dataAccessiable),
          ),
        ),
        mergeMap((shareFileList: any[]) => {
          const requests = shareFileList.map((shareFileRefLine: any) => {
            const { refNo } = shareFileRefLine.shareFile;
            const { version } = shareFileRefLine.shareFile;
            return this.documentService.getDataAccessiableIgnoreError$('shareFile', refNo, version).pipe(
              take(1),
              map((dataAccessiable) => ({ dataAccessiable, shareFileRefLine })),
            );
          });
          return forkJoin(requests);
        }),
        untilDestroyed(this),
      )
      .subscribe((results: any[]) => {
        results.forEach(({ dataAccessiable, shareFileRefLine }) => {
          this.documentDataService.updateCellValueByFieldId(
            dataAccessiable,
            shareFileListFieldId,
            null,
            'dataAccessiable',
            shareFileRefLine.id,
          );
        });
      });
  }

  private handleVpoItemTotalAmount() {
    this.docEditing$
      .pipe(
        switchMap(() => this.dataInitializing$),
        untilDestroyed(this),
      )
      .subscribe(() => {
        this.totalAmount$.next(0);
        this.vpoItemTotalAmountGroupByItem$.next({});
      });
  }

  private handleItemCsQtyAndSize() {
    this.dataInitializing$.pipe(untilDestroyed(this)).subscribe(() => {
      this.vpoItemCSPriceAndPlanQtySubject.next({});
    });

    const byItemRefNo = R.groupBy((vpoItemCsList: VpoOverrideItemCsDto) => vpoItemCsList.refNo);

    this.docEditing$
      .pipe(
        switchMap(() =>
          this.selectData$('vpoItemCsList').pipe(takeUntil(merge(this.docNotEditing$, this.dataInitializing$))),
        ),
        untilDestroyed(this),
      )
      .subscribe((vpoItemCsGroupList: VpoOverrideItemCsDto[][]) => {
        if (vpoItemCsGroupList.length === 0) {
          this.vpoItemCSPriceAndPlanQtySubject.next({});
          return;
        }

        let groupingByItemRefLotNo: AnyObject<ItemCSPriceAndPlanQty[]> = {};
        vpoItemCsGroupList.forEach((vpoItemCsGroup) => {
          const itemLotGroup = byItemRefNo(vpoItemCsGroup);
          Object.keys(itemLotGroup).forEach((key) => {
            groupingByItemRefLotNo = {
              ...groupingByItemRefLotNo,
              [key]: itemLotGroup[key].map((vpoItemCsList: VpoOverrideItemCsDto) => ({
                vpoItemCsId: vpoItemCsList.id,
                defaultPrice: vpoItemCsList.defaultPrice,
                price: vpoItemCsList.price,
                plannedQty: vpoItemCsList.plannedQty,
                innerCasePack: vpoItemCsList.innerCasePack,
                outerCasePack: vpoItemCsList.outerCasePack,
                itemLotNo: vpoItemCsList.itemLotNo,
                colorSeq: vpoItemCsList.colorSeq,
                seqNo: vpoItemCsList.seqNo,
                sizeSeq: vpoItemCsList.sizeSeq,
                vpoItemSizeRef: vpoItemCsList.vpoItemSizeRef,
                vpoItemColorRef: vpoItemCsList.vpoItemColorRef,
              })),
            };
          });
        });

        if (R.isEmpty(groupingByItemRefLotNo)) {
          return;
        }

        this.vpoItemCSPriceAndPlanQtySubject.next(groupingByItemRefLotNo);
      });
  }

  private handleShipGroupDtlPercentage() {
    this.dataInitializing$.pipe(untilDestroyed(this)).subscribe(() => {
      this.shipGroupDtlPercentage$.next({});
    });

    const byShipRefNo = R.groupBy((vpoShipDtlDto: VpoOverrideShipDtlDto) => vpoShipDtlDto.vpoShipId?.refNo);
    const byItemRefNo = R.groupBy((vpoShipDtlDto: VpoOverrideShipDtlDto) => vpoShipDtlDto.vpoItemRef);

    this.docEditing$
      .pipe(
        switchMap(() =>
          this.vpoShipDtlDtoGroupList$.pipe(takeUntil(merge(this.docNotEditing$, this.dataInitializing$))),
        ),
        untilDestroyed(this),
      )
      .subscribe((vpoShipDtlDtoGroupList: VpoOverrideShipDtlDto[][]) => {
        let vpoShipDtl = {};
        vpoShipDtlDtoGroupList.forEach((vpoShipDtlDtoGroup) => {
          const vpoShipRef = vpoShipDtlDtoGroup[0].vpoShipId?.refNo;
          const shipRefNoGroup = byShipRefNo(vpoShipDtlDtoGroup) as AnyObject<VpoOverrideShipDtlDto[]>;
          const itemRefGroup = byItemRefNo(shipRefNoGroup[vpoShipRef]) as AnyObject<VpoOverrideShipDtlDto[]>;

          let shipItemGroup: AnyObject<VpoOverrideShipDtlDto> = {};
          Object.keys(itemRefGroup).forEach((itemRef) => {
            const vpoShipItemDtl = itemRefGroup[itemRef][0];
            const tempItemDtl: AnyObject<VpoOverrideShipDtlDto> = {
              [itemRef]: {
                originalQty: vpoShipItemDtl.originalQty,
                packUnit: vpoShipItemDtl.packUnit,
                percentage: Math.floor(vpoShipItemDtl.originalQty / vpoShipItemDtl.packUnit),
              } as VpoOverrideShipDtlDto,
            };
            shipItemGroup = { ...shipItemGroup, ...tempItemDtl };
          });

          vpoShipDtl = { ...vpoShipDtl, [vpoShipRef]: shipItemGroup };
        });

        this.shipGroupDtlPercentage$.next(vpoShipDtl);
      });
  }

  private handleShipDtlCsRatioAndQty() {
    this.dataInitializing$.pipe(untilDestroyed(this)).subscribe(() => {
      this.vpoShipDtlCsRatioAndQtySubject.next({});
    });

    const byShipRefNo = R.groupBy((vpoShipDtlCsGroup: VpoOverrideShipDtlCsDto) => vpoShipDtlCsGroup.vpoShipId?.refNo);
    const byItemRefNo = R.groupBy((vpoShipDtlCsGroup: VpoOverrideShipDtlCsDto) => vpoShipDtlCsGroup.vpoItemRef);

    // The `vpoShipDtlCsGroupList` value is always generated in frontend since not provide in document data.
    this.selectData$('vpoShipDtlCsGroupList')
      .pipe(untilDestroyed(this))
      .subscribe((vpoShipDtlCsGroupList: VpoDocumentState['vpoShipDtlCsGroupList']) => {
        let vpoShipDtlCsRatio: AnyObject<AnyObject<VpoOverrideShipDtlCsDto[]>> = {};
        vpoShipDtlCsGroupList.forEach((vpoShipDtlCsGroup) => {
          // vpoItem does not has Color/Size data
          if (vpoShipDtlCsGroup.length === 0) {
            this.vpoShipDtlCsRatioAndQtySubject.next({});
            return;
          }

          /* unique id */
          const vpoShipRef = vpoShipDtlCsGroup[0].vpoShipId?.refNo;
          const { vpoItemRef } = vpoShipDtlCsGroup[0];
          const shipRefNoGroup = byShipRefNo(vpoShipDtlCsGroup) as AnyObject<VpoOverrideShipDtlCsDto[]>;
          const itemRefGroup = byItemRefNo(shipRefNoGroup[vpoShipRef]) as AnyObject<VpoOverrideShipDtlCsDto[]>;

          itemRefGroup[vpoItemRef] = itemRefGroup[vpoItemRef].map(
            (vpoShipDtlCs) =>
              ({
                shipDtlCsId: vpoShipDtlCs.id,
                innerCasePack: vpoShipDtlCs.innerCasePack,
                ratio: vpoShipDtlCs.ratio,
                colorSizeQty: vpoShipDtlCs.colorSizeQty,
                shipmentNo: vpoShipDtlCs.shipmentNo,
                colorSeq: vpoShipDtlCs.colorSeq,
                sizeSeq: vpoShipDtlCs.sizeSeq,
                seqNo: vpoShipDtlCs.seqNo,
                itemLotNo: vpoShipDtlCs.itemLotNo,
                vpoItemSizeRef: vpoShipDtlCs.vpoItemSizeRef,
                vpoItemColorRef: vpoShipDtlCs.vpoItemColorRef,
                shipmentStatus: vpoShipDtlCs.vpoShipId?.shipmentStatus,
              } as unknown as VpoOverrideShipDtlCsDto),
          );

          if (vpoShipDtlCsRatio[vpoShipRef]) {
            vpoShipDtlCsRatio[vpoShipRef] = {
              ...vpoShipDtlCsRatio[vpoShipRef],
              [vpoItemRef]: itemRefGroup[vpoItemRef],
            };
            return;
          }

          vpoShipDtlCsRatio = { ...vpoShipDtlCsRatio, [vpoShipRef]: itemRefGroup } as AnyObject<
            AnyObject<VpoOverrideShipDtlCsDto[]>
          >;
        });

        if (R.isEmpty(vpoShipDtlCsRatio)) {
          this.vpoShipDtlCsRatioAndQtySubject.next({});
          return;
        }

        this.vpoShipDtlCsRatioAndQtySubject.next(vpoShipDtlCsRatio);
      });
  }

  registerBusinessLogic() {
    this.handleVpoItemTotalAmount();
    this.handleItemCsQtyAndSize();
    this.handleShipGroupDtlPercentage();
    this.handleShipDtlCsRatioAndQty();
    this.handleShareFileList('vpoShareFileList');
    this.handleAlertChangeDetection();
    this.handleVpoItemCsDto();
    this.handleVpoShipItemCsDto();
    this.handleTotalAmtBeforeChargeType();
    this.handleVpoCustomerOwnedVendorRef();

    const alwaysRunDataMap: AnyObject<Observable<any>> = {
      vpoItemCsList: this.dynamicVPOItemCsList$,
      vpoShipDtlCsGroupList: this.dynamicVPOShipDtlCsGroupList$,
    };

    const dataMap: AnyObject<Observable<any>> = {
      vpoItemList: merge(this.vpoItemListLotChanged$, this.vpoItemCalculationStream$),
      vpoShipDtlDtoGroupList: merge(this.dynamicVPOShipDtlGroupListLotChanged$, this.vpoShipDtlCalculationStream$),
      vpoChargeList: merge(this.vpoChargeListOptionsChanged$, this.vpoChargeListCalculation$),
      vpoChargeOnDocList: this.vpoDocumentChargeListCalculation$,
      totalPlannedAmt: this.summaryPlannedAmount$,
      totalAmt: this.summaryAmount$,
      totalItems: this.summaryItems$,
      totalItemItems: this.summaryItemItems$,
      totalShipments: this.summaryShipments$,
      totalPlannedQty: this.summaryTotalPlannedQty$,
      totalQty: this.getIsPlannedqtyOrderqtyControl() ? this.summaryTotalQtyByShipments$ : this.summaryTotalQty$,
      totalCartons: this.summaryTotalCartons$,
      headerFactoryRef: this.headerFactoryRef$,
      chainOfCustodyOverview: this.chainOfCustodyOverview$,
    };

    Object.entries(alwaysRunDataMap).forEach(([id, data$]) => {
      data$.pipe(auditTime(0), distinctUntilChanged(deepEqual), untilDestroyed(this)).subscribe((data) => {
        this.documentDataService.updateData({ [id]: data });
      });
    });

    Object.entries(dataMap).forEach(([id, data$]) => {
      this.docEditing$
        .pipe(
          switchMap(() => data$.pipe(takeUntil(merge(this.docNotEditing$, this.dataInitializing$)))),
          auditTime(0),
          distinctUntilChanged(deepEqual),
          untilDestroyed(this),
        )
        .subscribe((data) => {
          this.documentDataService.updateData({ [id]: data });
        });
    });
  }

  selectData$(id: string) {
    return this.documentDataQuery.select(id).pipe(
      map((data) => data ?? []),
      filter((data) => {
        // group grid
        if (Array.isArray(data) && Array.isArray(data[0])) {
          return data[0]?.[0]?.id;
        }
        // normal grid

        return !data.some((row) => isNil(row.id));
      }),
    );
  }

  private getVpoItemCsList(refNo: VpoItemDto['refNo']): VpoOverrideItemCsDto[] {
    const documentData = this.documentDataQuery.getValue() as unknown as VpoDocumentState;

    return (
      documentData?.vpoItemList
        .map((vpoItem) => vpoItem?.vpoItemCsList)
        .find((vpoItemCsList) => vpoItemCsList?.some((itemCs) => itemCs.refNo === refNo)) || []
    );
  }

  private getIsPlannedqtyOrderqtyControl(): boolean {
    const plannedqtyOrderqtyControl =
      this.domainAttributeService.state[DOMAIN_ATTRIBUTES.VPO_PLANNEDQTY_ORDERQTY_CONTROL] || 'FALSE';
    return plannedqtyOrderqtyControl === 'TRUE';
  }

  private handleVpoItemCsDto() {
    this.documentDataQuery
      .select('vpoItemCsList')
      .pipe(auditTime(0), distinctUntilChanged(deepEqual), untilDestroyed(this))
      .subscribe((vpoItemCsList: VpoDocumentState['vpoItemCsList']) => {
        const documentData = this.documentDataQuery.getValue() as unknown as VpoDocumentState;
        let vpoItemCsDto = [...(documentData.vpoItemCsDto ?? [])];

        vpoItemCsList?.forEach((vpoItemCss) => {
          vpoItemCss.forEach((vpoItemCs) => {
            const find = vpoItemCsDto.find(
              (vicd) =>
                vicd.itemLotNo === vpoItemCs.itemLotNo &&
                vicd.colorSeq === vpoItemCs.colorSeq &&
                vicd.vpoItemColorRef === vpoItemCs.vpoItemColorRef &&
                vicd.vpoItemSizeRef === vpoItemCs.vpoItemSizeRef &&
                vicd.seqNo === vpoItemCs.seqNo,
            );
            if (!find) {
              vpoItemCsDto.push(vpoItemCs);
            } else {
              const vpoItemCsDtoDtoIndex = vpoItemCsDto.indexOf(find);
              vpoItemCsDto = vpoItemCsDto.map((data, index) => {
                if (vpoItemCsDtoDtoIndex === index) {
                  return vpoItemCs;
                }
                return data;
              });
            }
          });
        });
        this.documentDataService.updateData({ vpoItemCsDto });
      });
  }

  private handleVpoShipItemCsDto() {
    this.documentDataQuery
      .select('vpoShipDtlCsGroupList')
      .pipe(auditTime(0), distinctUntilChanged(deepEqual), untilDestroyed(this))
      .subscribe((vpoShipDtlCsGroupList: VpoDocumentState['vpoShipDtlCsGroupList']) => {
        let vpoShipDtlCsDto: VpoDocumentState['vpoShipDtlCsDto'] = [];
        vpoShipDtlCsGroupList?.forEach((vpoShipDtlCss) => {
          vpoShipDtlCss.forEach((vpoShipDtlCs) => {
            const find = vpoShipDtlCsDto?.find(
              (vicd) =>
                vicd.itemLotNo === vpoShipDtlCs.itemLotNo &&
                vicd.colorSeq === vpoShipDtlCs.colorSeq &&
                vicd.vpoItemColorRef === vpoShipDtlCs.vpoItemColorRef &&
                vicd.shipmentNo === vpoShipDtlCs.shipmentNo &&
                vicd.vpoItemSizeRef === vpoShipDtlCs.vpoItemSizeRef,
            );
            if (!find) {
              vpoShipDtlCsDto?.push(vpoShipDtlCs);
            } else {
              const vpoShipDtlCsDtoIndex = vpoShipDtlCsDto.indexOf(find);
              vpoShipDtlCsDto = vpoShipDtlCsDto.map((data, index) => {
                if (vpoShipDtlCsDtoIndex === index) {
                  return vpoShipDtlCs;
                }
                return data;
              });
            }
          });
        });
        this.documentDataService.updateData({ vpoShipDtlCsDto });
      });
  }

  private handleTotalAmtBeforeChargeType() {
    this.docEditing$
      .pipe(
        auditTime(0),
        switchMap(() =>
          this.documentDataQuery
            .select('vpoItemList')
            .pipe(takeUntil(merge(this.docNotEditing$, this.dataInitializing$))),
        ),
        distinctUntilChanged((previous: VpoDocumentState['vpoItemList'], current: VpoDocumentState['vpoItemList']) => {
          const previousLotList = previous?.map((vpoItem) => vpoItem.totalAmt);
          const currentLotList = current?.map((vpoItem) => vpoItem.totalAmt);
          return deepEqual(previousLotList, currentLotList);
        }),
        map((vpoItemList) => vpoItemList ?? []),
        untilDestroyed(this),
      )
      .subscribe((vpoItemList) => {
        if (vpoItemList.length === 0) {
          this.vpoItemTotalAmountGroupByItem$.next({});
          return;
        }

        let totalAmountGroup: AnyObject<{ totalAmt: number }> = {};
        let totalAmount = 0;
        vpoItemList
          .filter((vpoItem) => !vpoItem.treePath?.includes('::'))
          .forEach((vpoItem) => {
            totalAmount += vpoItem.totalAmt;
            const tempAmountGroupByItemRef = { [vpoItem.refNo]: { totalAmt: vpoItem.totalAmt } };
            totalAmountGroup = { ...totalAmountGroup, ...tempAmountGroupByItemRef };
          });

        this.totalAmount$.next(totalAmount);
        this.vpoItemTotalAmountGroupByItem$.next(totalAmountGroup);
        this.documentDataService.updateData({ totalAmtBeforeChargeType: totalAmount });
      });
  }

  private handleVpoCustomerOwnedVendorRef() {
    this.documentDataQuery
      .select('vendorId')
      .pipe(auditTime(0), distinctUntilChanged(deepEqual), untilDestroyed(this))
      .subscribe((vendor) => {
        if (isEmptyOrNil(vendor)) {
          this.documentDataService.updateTieredData(null, ['customerOwnedVendorRef']);
        }
      });
  }
}
