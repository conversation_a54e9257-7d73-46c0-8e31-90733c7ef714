.control-pdf-page-wrapper {
  height: 24px;
  margin-right: 14px;
}

.control-pdf-page-text {
  margin-right: 6px;
  font-size: 14px;
  line-height: 14px;
  color: var(--color-text-subtlest);
  align-content: center;
}

.control-container {
  justify-content: space-between;
  padding-left: 10px;
}

.content {
  height: calc(100% - 108px);
  margin: 16px;
}

.tools-wrapper {
  width: 48px;
  display: grid;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #f8f8f8;
}

.tool-button {
  width: 48px;
  height: 48px;
  border: 0;
  outline: 0;
  padding: 0;
  background: transparent;
  cursor: pointer;
  color: rgba(58, 70, 77, 0.72);
  &:focus {
    background-color: rgba(232, 232, 232, 0.4);
  }
  &:hover {
    background-color: rgba(232, 232, 232, 0.8);
  }
  &:active {
    color: #1d1d1d;
    background-color: #b7b7b7;
  }
  &:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  }
}
