import { DIALOG_DATA } from '@angular/cdk/dialog';
import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Inject } from '@angular/core';

import { TranslocoModule } from '@ngneat/transloco';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { ColDef, GridApi, GridReadyEvent } from 'ag-grid-community';
import { BehaviorSubject, Observable, of, switchMap } from 'rxjs';
import { catchError, map, take, tap } from 'rxjs/operators';

import { ReadUserDetailData } from './read-user-detail-data';
import { CBX_URL, DETAIL_ROW_HEIGHT } from 'src/app/config/constant';
import { ReadUserDetailDialogData } from 'src/app/interface/model';
import { CbxDialogHeaderComponent } from 'src/app/modules/shared/common/cbx-dialog-header/cbx-dialog-header.component';
import { CbxTableCellMapperService } from 'src/app/modules/shared/common/cbx-table/cbx-table-cell-mapper.service';
import { CbxTableComponent } from 'src/app/modules/shared/common/cbx-table/cbx-table.component';
import { ApiService } from 'src/app/services/api.service';

@UntilDestroy()
@Component({
  selector: 'app-read-user-detail-dialog',
  templateUrl: './read-user-detail-dialog.component.html',
  styleUrls: ['./read-user-detail-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [AsyncPipe, TranslocoModule, CbxDialogHeaderComponent, CbxTableComponent],
})
export class ReadUserDetailDialogComponent {
  private readonly apiService = inject(ApiService);

  private readonly recordCache = new Map<'READ' | 'UNREAD', ReadUserDetailData[]>();

  readonly cbxTableCellComponents = inject(CbxTableCellMapperService).components;

  readonly currentStatus$ = new BehaviorSubject<'READ' | 'UNREAD'>('READ');

  readonly loading$ = new BehaviorSubject<boolean>(true);

  readonly rowHeight = DETAIL_ROW_HEIGHT;

  readonly defaultColDef: ColDef = {
    cellRenderer: 'textComponent',
    resizable: true,
    minWidth: 220,
    cellClass: 'popup-table-cell',
  };

  readonly colDefs: ColDef[] = [
    {
      field: 'userName',
      headerName: 'User Name',
      cellRenderer: 'avatarComponent',
    },
    {
      field: 'loginId',
      headerName: 'User ID',
      cellRenderer: 'linkComponent',
    },
    {
      field: 'actionDate',
      headerName: 'Read on',
      cellRenderer: 'dateComponent',
    },
  ];

  title: string;
  readTabLabel: string;
  unreadTabLabel: string;
  currentRecord: ReadUserDetailData[];
  gridApi: GridApi;

  constructor(@Inject(DIALOG_DATA) public data: ReadUserDetailDialogData) {
    const { title, moduleId, refNo, readRatio, unreadRatio } = data;
    this.title = title;

    this.readTabLabel = `Read ${readRatio}`;
    this.unreadTabLabel = `Unread ${unreadRatio}`;

    this.handleCurrentStatusChange(moduleId, refNo);
  }

  gridReady(ready: GridReadyEvent) {
    this.gridApi = ready.api;
  }

  handleCurrentStatusChange(moduleId: string, refNo: string) {
    this.currentStatus$
      .pipe(
        tap(() => this.loading$.next(true)),
        switchMap((status: 'READ' | 'UNREAD') => {
          if (this.recordCache.has(status)) {
            return of(this.recordCache.get(status));
          }

          return this.getRecord$(moduleId, refNo, status);
        }),
        untilDestroyed(this),
      )
      .subscribe((record) => {
        this.currentRecord = record;
        this.loading$.next(false);
      });
  }

  private getRecord$(moduleId: string, refNo: string, status: 'READ' | 'UNREAD'): Observable<ReadUserDetailData[]> {
    return this.apiService.get<ReadUserDetailData[]>(CBX_URL.getReadUserDetailList(moduleId, refNo, status)).pipe(
      take(1),
      map((userList) => userList ?? []),
      tap((userList) => this.recordCache.set(status, userList)),
      catchError(() => []),
    );
  }
}
