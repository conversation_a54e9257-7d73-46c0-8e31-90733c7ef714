<button
  *ngFor="let item of model"
  class="link-bar-button flex-noshrink flat-button flat-button-height-24 margin-right-16 gap-4"
  [class.button-with-icon]="item?.iconKey"
  (click)="linkBarItem.emit(item)"
>
  <app-icon *ngIf="item?.iconKey" class="icon-size-18 color-primary" [svgIcon]="item.iconKey"></app-icon>
  <span
    *ngIf="item | textTransform : labelTransform as label"
    class="label font-size-12"
    [class.label-without-icon]="!item?.iconKey"
  >
    {{ label }}

    <ng-container *ngIf="item.format">
      {{ formatTransform$(item.format) | async }}
    </ng-container>
  </span>
</button>
