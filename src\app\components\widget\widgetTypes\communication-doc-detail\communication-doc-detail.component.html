<div class="record-section">
  <ng-container *ngFor="let record of sortedData$ | async">
    <div class="flex pointer wrapper items-center justify-between" (click)="openCommunicationDoc(record)">
      <div class="hint-wrapper">
        <div class="unread-hint" [class.opacity-0]="!record.isUnread"></div>
      </div>
      <div class="content height-100">
        <div class="flex">
          <div class="header text-ellipsis">{{ record.documentName }}</div>
          <div *ngIf="record.isActionRequired" class="label text-center text-ellipsis">Action required</div>
          <div class="flex-spacer"></div>
          <div class="date justify-end">
            {{ record.publishedOn | dateLabelFormat : dateType : (dateFormat$ | async) : true }}
          </div>
        </div>
        <div class="description text-ellipsis">{{ record.description }}</div>
      </div>

      <div class="flex-spacer"></div>
    </div>
    <div class="divider"></div>
  </ng-container>
</div>

<div
  *ngIf="!edit && isViewAllButtonExist$ | async"
  class="view-all-button pointer flex items-center justify-center"
  (click)="directToListing()"
>
  View All {{ (sortedData$ | async).length }}
</div>
