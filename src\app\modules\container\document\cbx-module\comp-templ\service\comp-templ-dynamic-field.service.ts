import { Injectable } from '@angular/core';

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { combineLatest, Observable } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';

import { DataListType_DataListTypeDto } from '../../../../../../entities/api';
import { DocumentDynamicFieldService } from '../../../service/document-dynamic-field.service';
import { DocumentDataQuery, DocumentStatusService } from '../../../state';
import { FieldDefine } from 'src/app/entities/form-field';

@UntilDestroy()
@Injectable()
export class CompTemplDynamicFieldService extends DocumentDynamicFieldService {
  dynamicFieldLogical = {
    // evaluationDtlsList: this.handleEvaluationDetails.bind(this),
    CompTemplDtlList: this.CompTemplDtlsFieldFromDataListType.bind(this),
  };

  constructor(
    protected readonly documentDataQuery: DocumentDataQuery,
    private readonly documentStatusService: DocumentStatusService,
  ) {
    super();
  }

  // private handleEvaluationDetails(fields$: Observable<FieldDefine[]>): Observable<FieldDefine[]> {
  //   const dataListType$: Observable<DataListType> = this.documentDataQuery.select('dataListType');
  //   return null;
  // }

  private CompTemplDtlsFieldFromDataListType(fields$: Observable<FieldDefine[]>): Observable<FieldDefine[]> {
    const dataListType$: Observable<DataListType_DataListTypeDto> = this.documentDataQuery.select('dataListType');

    return combineLatest([fields$, dataListType$]).pipe(
      map(([fields, dataListType]) => {
        if (!dataListType?.dataListTypeItemsList) {
          return fields;
        }

        const { dataListTypeItemsList } = dataListType;

        const newFields = this.generateDynamicFieldsFromDataListTypeItemsList(dataListTypeItemsList);
        return fields.concat(newFields);
      }),
      untilDestroyed(this),
      shareReplay(1),
    );
  }
}
