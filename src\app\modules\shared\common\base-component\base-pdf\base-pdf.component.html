<pdf-viewer
  [src]="value"
  [page]="page"
  [zoom]="zoom"
  [original-size]="originalSize"
  [show-all]="showAll"
  [zoom-scale]="zoomScale"
  [show-borders]="showBorders"
  [render-text]="renderText"
  (after-load-complete)="afterLoadComplete($event); loading$.next(false)"
  class="width-100 height-100"
></pdf-viewer>

<div *ngIf="loading$ | async" class="transform-center">
  <span class="bouncing-loading"><span class="bouncing-point"></span></span>
</div>
