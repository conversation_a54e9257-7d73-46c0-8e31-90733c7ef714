import { DataListType_DataListTypeDto } from '../../../../../../entities/api';

export interface CompTempl {
  id: string;
  refNo: string;
  revision: number;
  entityVersion: number;
  domainId: string;
  hubDomainId: string;
  isForReference: boolean;
  version: number;
  status: string;
  docStatus: string;
  editingStatus: string;
  createUser: string;
  createUserName: string;
  updateUser: string;
  updateUserName: string;
  createdOn: string;
  updatedOn: string;
  isCpmInitialized: boolean;
  isLatest: boolean;
  inspectReportNo: string;
  description: string;
  dataListTypeName: string;
  dataListType: DataListType_DataListTypeDto;
  evaluationSectionDtlList: EvaluationSectionDtlList[];
  CompTemplDtlList: CompTemplDtlList[];
  CompTemplRuleList: CompTemplRuleList[];
  partiesList: any[];
  amIOwner: boolean;
  initializePartyByDocumentId: string;
  agreementTemplateItemsList: any[];
}

export interface EvaluationSectionDtlList {
  id: string;
  seq: number;
  name: string;
  isUnavailable: boolean;
}

export interface CompTemplDtlList {
  id: string;
  seq: number;
  description: string;
  name: string;
  dataListType: string;
  updatedOn: string;
  editingStatus: string;
  evaluationCode: string;
  evaluationName: string;
  isUnavailable: boolean;
  section: EvaluationSectionDtlList;
  customFields: any;
}

export interface CompTemplRuleList {
  refNo: string;
  businessRefNo: string;
  priority: number;
  condition: EmbedCodelist;
}

export interface EmbedCodelist {
  code: string;
  name: string;
  version: number;
}
