import { Dialog } from '@angular/cdk/dialog';
import { inject, Injectable } from '@angular/core';

import { DocumentDefineStore } from '../../../state';
import { AbstractDocumentUIAction } from '../../model/abstract-document-ui-action';
import { ReadUserDetailDialogComponent } from './read-user-detail-dialog/read-user-detail-dialog.component';
import { defaultDialogConfig } from 'src/app/config/dialog';
import { ReadUserDetailDialogData } from 'src/app/interface/model';

@Injectable()
export class ViewReadUserDetailAction extends AbstractDocumentUIAction {
  private readonly documentDefineStore = inject(DocumentDefineStore);
  private readonly dialog = inject(Dialog);

  process() {
    const { readCount, assignedUserCount } = this.documentData;
    const readRatio = `${readCount}/${assignedUserCount}`;
    const unreadRatio = `${assignedUserCount - readCount}/${assignedUserCount}`;

    return this.dialog.open<any, ReadUserDetailDialogData, ReadUserDetailDialogComponent>(
      ReadUserDetailDialogComponent,
      {
        ...defaultDialogConfig,
        height: '565px',
        width: '700px',
        data: {
          title: this.actionContext.popupMeta.title,
          readRatio,
          unreadRatio,
          moduleId: this.moduleId,
          refNo: this.refNo,
        },
      },
    ).closed;
  }

  finalizeProcess() {
    this.documentDefineStore.setLoading(false);
  }
}
