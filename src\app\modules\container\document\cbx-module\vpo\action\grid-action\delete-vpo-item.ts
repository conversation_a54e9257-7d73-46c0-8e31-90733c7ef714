import { Dialog } from '@angular/cdk/dialog';
import { Injectable } from '@angular/core';

import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';

import { DeleteAction } from '../../../../grid-section-content/grid-action/action';
import { VpoChargeDto } from '../../model/vpo-dto';
import { generalAlertDialogConfig } from 'src/app/config/dialog';
import { Vpo_VpoChainOfCustodyDto, Vpo_VpoItemDto } from 'src/app/entities/api';
import { AlertDialogComponent } from 'src/app/modules/shared/common/warning-dialog/alert-dialog.component';

@Injectable()
export class DeleteVpoItem extends DeleteAction {
  constructor(private readonly dialog: Dialog) {
    super();
  }

  initialActionParams() {
    super.initialActionParams();
  }

  validate(): Observable<boolean> {
    const vpoItemRefList = this.selectedRows.map((vpoItem) => vpoItem.refNo);
    const { vpoShipDtlDtoGroupList } = this.documentDataQuery.getValue();
    const { vpoChargeList } = this.documentDataQuery.getValue();
    const hasRelatedShipDtlDto = vpoShipDtlDtoGroupList?.some((vpoShipDtlDtoGroupData) =>
      vpoShipDtlDtoGroupData.some((vpoShipDtlDtoRowData) =>
        vpoItemRefList.find((itemRef) => vpoShipDtlDtoRowData.vpoItemRef === itemRef),
      ),
    );

    const hasRelatedChargeList = vpoChargeList?.some((vpoCharge: VpoChargeDto) =>
      vpoItemRefList.find((itemRef) => vpoCharge?.vpoItemId?.code === itemRef),
    );

    if (hasRelatedShipDtlDto || hasRelatedChargeList) {
      const { type, buttonType, message } = this.warningDialogMeta;
      return this.messageDialogService.openDialog(buttonType, message, type).closed.pipe(map(() => false));
    }

    return this.openWarningDialog().closed.pipe(map((result) => result?.type === 'done'));
  }

  process(): Observable<any> {
    const vpoItemRefList = this.selectedRows.map((vpoItem) => vpoItem.treePath);

    let { rowData } = this;
    vpoItemRefList.forEach((treePath) => {
      if (treePath.includes('::')) {
        rowData = rowData.filter((row) => !row.treePath.includes(treePath.split('::')[0]));
      } else {
        rowData = rowData.filter((row) => !row.treePath.includes(treePath));
      }
    });

    const data = { [this.sectionId]: rowData };
    this.documentDataService.updateData({ ...data });
    this.updateChainOfCustodyRequirement();
    return of(true);
  }

  openWarningDialog() {
    return this.dialog.open<any>(AlertDialogComponent, {
      ...generalAlertDialogConfig,
      data: {
        messageButtons: 'YesNo',
        message:
          'Removing the Item will remove related items on the Chain of Custody section below. Are you sure to continue?',
        title: 'warning',
      },
    });
  }

  updateChainOfCustodyRequirement() {
    const existingVpoItemList: Vpo_VpoItemDto[] = this.documentDataQuery.getValue().vpoItemList ?? [];
    const existingCocData: Vpo_VpoChainOfCustodyDto[] = this.documentDataQuery.getValue().vpoChainOfCustodyList ?? [];
    const vpoItemRefList = existingVpoItemList.map((vpoItem) => vpoItem.itemNo);
    const updateCocData = existingCocData.filter((row) => vpoItemRefList.includes(row.itemNo));

    this.documentDataService.updateData({ vpoChainOfCustodyList: updateCocData });
  }
}
