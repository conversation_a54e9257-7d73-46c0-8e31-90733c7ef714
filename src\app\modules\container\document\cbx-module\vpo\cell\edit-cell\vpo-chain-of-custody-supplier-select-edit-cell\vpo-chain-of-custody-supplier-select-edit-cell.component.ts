import { Dialog } from '@angular/cdk/dialog';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostBinding,
  OnDestroy,
  Optional,
} from '@angular/core';

import { coerceArray } from '@datorama/akita';
import { UntilD<PERSON>roy, untilDestroyed } from '@ngneat/until-destroy';
import * as _ from 'lodash';
import { distinctUntilChanged, filter } from 'rxjs';

import { ChainOfCustodySupplierSelectDialogComponent } from '../../../dialog/chain-of-custody-supplier-select-dialog/chain-of-custody-supplier-select-dialog.component';
import {
  ChainOfCustodySupplier,
  ChainOfCustodySupplierSelectData,
} from '../../../model/chain-of-custody-supplier-select-data';
import { defaultDialogConfig } from 'src/app/config/dialog';
import { FieldDefine } from 'src/app/entities/form-field';
import { DocumentOverlayService } from 'src/app/modules/container/document/document-preview/document-overlay.service';
import { CellRendererParams } from 'src/app/modules/container/document/grid-section-content/grid-section-grid/cell/cell-component';
import { FillHandleComponent } from 'src/app/modules/container/document/grid-section-content/grid-section-grid/cell/cell-tool/fill-handle/fill-handle.component';
import { DocumentDataQuery, DocumentDataService } from 'src/app/modules/container/document/state';
import { BaseTableSelectComponent } from 'src/app/modules/shared/common/base-component/base-table-select/base-table-select.component';
import {
  AngularCellComponent,
  CellRendererAngularComp,
} from 'src/app/modules/shared/common/cbx-table/cell-renderer-params';
import { AgGridRowHeightDirective } from 'src/app/modules/shared/directives/ag-grid-row-height.directive';
import { CopyCellObjectValueDirective } from 'src/app/modules/shared/directives/grid/copy-cell-object-value.directive';
import { FillHandleCellDirective } from 'src/app/modules/shared/directives/grid/fill-handle-cell.directive';
import { deepClone, isEmptyOrNil, isNotEmptyOrNil } from 'src/app/utils';

@UntilDestroy()
@Component({
  selector: 'app-vpo-chain-of-custody-supplier-select-edit-cell',
  templateUrl: './vpo-chain-of-custody-supplier-select-edit-cell.component.html',
  styleUrls: ['./vpo-chain-of-custody-supplier-select-edit-cell.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [BaseTableSelectComponent, FillHandleComponent, FillHandleCellDirective, CopyCellObjectValueDirective],
})
export class VpoChainOfCustodySupplierSelectEditCellComponent
  extends AngularCellComponent
  implements CellRendererAngularComp, OnDestroy
{
  @HostBinding('class') class = 'block';

  params: CellRendererParams;

  isSingleMode = true;

  format = '{businessName}';

  supplier: ChainOfCustodySupplier[];

  field: FieldDefine;

  constructor(
    protected readonly cdr: ChangeDetectorRef,
    readonly host: ElementRef,
    protected readonly dialog: Dialog,
    protected readonly documentOverlayService: DocumentOverlayService,
    @Optional() private readonly agGridRowHeightDirective: AgGridRowHeightDirective,
    private readonly documentDataService: DocumentDataService,
    private readonly documentDataQuery: DocumentDataQuery,
  ) {
    super(host);
  }

  private updateParams(params: CellRendererParams) {
    this.params = params;
    this.field = params.field;
    const { value } = params;

    this.supplier = isEmptyOrNil(value) ? [] : coerceArray(value);
  }

  agInit(params: CellRendererParams): void {
    this.updateParams(params);

    this.agGridRowHeightDirective?.registerResizeCell(params.node, this.host.nativeElement);
  }

  ngOnDestroy(): void {
    this.agGridRowHeightDirective?.removeResizeCell(this.params.node, this.host.nativeElement);
  }

  refresh(params: CellRendererParams): boolean {
    this.updateParams(params);

    this.cdr.markForCheck();
    return true;
  }

  emitValueChange(data: ChainOfCustodySupplierSelectData[]) {
    this.params.setValue(data?.[0] ?? null);
    this.resize();
  }

  openDocumentRef(supplier: ChainOfCustodySupplier) {
    const payload = {
      tabId: 'master',
      moduleId: supplier.module,
      refNo: supplier.refNo,
    };

    this.documentOverlayService.openDocumentRef(payload);
  }

  onClickSelect() {
    const documentData = this.documentDataQuery.getValue();

    const dialogRef = this.dialog.open<any, any, ChainOfCustodySupplierSelectDialogComponent>(
      ChainOfCustodySupplierSelectDialogComponent,
      {
        ...defaultDialogConfig,
        maxWidth: '960px',
        width: '80vw',
        data: {
          factRefNos: Array.from(
            new Set(
              documentData?.vpoItemList?.flatMap((item) =>
                item.itemId?.refNo === this.params.data?.itemRefNo ? [item.factId?.factCode] : [],
              ) ?? [],
            ),
          ),
          vendorRefNo:
            this.params.context.sectionId !== 'vpoChainOfCustodyDtlUpdateList'
              ? this.documentDataQuery.getValue().vendorId?.refNo
              : this.params.data.vendor?.refNo,
          refNo:
            this.params.context.sectionId !== 'vpoChainOfCustodyDtlUpdateList'
              ? this.documentDataQuery.getValue().refNo
              : this.params.data.vpo.refNo,
          supplier: this.supplier,
        },
      },
    );

    dialogRef.closed
      .pipe(
        filter((result) => result?.type === 'done'),
        untilDestroyed(this),
      )
      .subscribe(({ payload }) => {
        const { businessName, refNo, module, countryOfOrigin, city, id } = payload[0];
        const chainOfCustodySupplier = { businessName, refNo, id, module };
        // const chainOfCustodyToSupplier = { businessName, refNo, id, module };

        const fieldName = this.field.id;
        let mergeData = {};
        if (fieldName === 'chainOfCustodyToSupplier') {
          mergeData = {
            chainOfCustodyToSupplier: payload[0],
            countryOfOrigin,
            city,
          };
        }
        if (fieldName === 'chainOfCustodySupplier') {
          mergeData = {
            chainOfCustodySupplier: payload[0],
            countryOfOrigin,
            city,
          };
        }

        const commonValue = {
          changeTo: payload[0],
          [this.field.id]: payload[0],
        };

        if (this.params.context.sectionId === 'vpoChainOfCustodyDtlUpdateList') {
          this.params.setValue(chainOfCustodySupplier);
          this.documentDataService.mergeRowValue('vpoChainOfCustodyDtlUpdateList', this.params.data.id, mergeData);
        } else {
          const mergeRowValue =
            this.field.id === 'chainOfCustodySupplier' ? { ...commonValue, countryOfOrigin, city } : commonValue;

          this.documentDataService.mergeRowValue('vpoChainOfCustodyList', this.params.data.id, mergeRowValue);
          this.resize();
        }
      });
  }

  private resize() {
    setTimeout(() => {
      this.params.api.autoSizeColumns([this.params.column], false);
    }, 10);
  }

  vpoChainOfCustodyDtlUpdateList$ = this.documentDataQuery
    .select('vpoChainOfCustodyDtlUpdateList')
    .pipe(
      filter(isNotEmptyOrNil),
      distinctUntilChanged((prev, curr) => _.isEqual(prev, curr)),
    )
    .subscribe((chainOfCustodyList: any[]) => {
      if (isEmptyOrNil(chainOfCustodyList)) {
        return null;
      }
      const tempChainOfCustodyList = deepClone(chainOfCustodyList);

      const updatedList = chainOfCustodyList.map((item) => {
        if (!item.chainOfCustodySupplier) {
          return {
            ...item,
            countryOfOrigin: null,
            city: null,
          };
        }
        if (item.chainOfCustodySupplier) {
          const tempItems = tempChainOfCustodyList.find((tempItem) => {
            if (tempItem.chainOfCustodySupplier?.id === item.chainOfCustodySupplier?.id) {
              return { ...item, countryOfOrigin: tempItem.countryOfOrigin, city: tempItem.city };
            }
            return null;
          });
          return { ...item, countryOfOrigin: tempItems.countryOfOrigin, city: tempItems.city };
        }
        return item;
      });

      this.documentDataService.updateData({ vpoChainOfCustodyDtlUpdateList: updatedList });
      return null;
    });
}
