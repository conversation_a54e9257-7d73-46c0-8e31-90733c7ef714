<app-cbx-dialog-header [ngClass]="isWarningDialog || isErrorDialog ? 'warning-title' : 'info-title'">
  <app-icon *ngIf="isWarningDialog || isErrorDialog; else InfoIcon" class="margin-right-4">error</app-icon>

  <ng-template #InfoIcon>
    <app-icon svgIcon="icon_info" class="margin-right-4"></app-icon>
  </ng-template>

  <span *ngIf="title.includes('D&B'); else commonTitle">{{ title }}</span>
  <ng-template #commonTitle>
    <span>{{ title | titlecase }}</span>
  </ng-template>
</app-cbx-dialog-header>

<div class="padding-x-16 font-size-14 color-black-85 overflow-auto message break-all">
  <div [innerHTML]="message"></div>
</div>

<div class="flex justify-end items-center padding-x-16 padding-y-8 gap-8">
  <ng-container [ngSwitch]="messageButtons">
    <ng-container *ngSwitchCase="'YesNo'">
      <button class="basic-button min-width-94" (click)="onCancel()">No</button>
      <button class="flat-button primary-color min-width-94 close-button-class" cdkFocusInitial (click)="onDone()">
        {{ 'yes' | transloco }}
      </button>
    </ng-container>

    <ng-container *ngSwitchCase="'YesCancel'">
      <button class="basic-button min-width-94" (click)="onCancel()">{{ 'cancel' | transloco }}</button>
      <button class="flat-button primary-color min-width-94 close-button-class" cdkFocusInitial (click)="onDone()">
        {{ 'yes' | transloco }}
      </button>
    </ng-container>

    <ng-container *ngSwitchCase="'OkayCancel'">
      <button class="basic-button min-width-94" (click)="onCancel()">{{ 'cancel' | transloco }}</button>
      <button class="flat-button primary-color min-width-94 close-button-class" cdkFocusInitial (click)="onDone()">
        OK
      </button>
    </ng-container>

    <ng-container *ngSwitchCase="'ConfirmCancel'">
      <button class="basic-button min-width-94" (click)="onCancel()">{{ 'cancel' | transloco }}</button>
      <button class="flat-button primary-color min-width-94 close-button-class" cdkFocusInitial (click)="onDone()">
        {{ 'confirm' | transloco }}
      </button>
    </ng-container>

    <ng-container *ngSwitchDefault>
      <button class="flat-button primary-color min-width-94 close-button-class" cdkFocusInitial (click)="onDone()">
        OK
      </button>
    </ng-container>
  </ng-container>
</div>
