import { DialogRef } from '@angular/cdk/dialog';
import { ComponentType } from '@angular/cdk/portal';
import { ComponentRef } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';

import { ColDef } from 'ag-grid-community';
import { Observable } from 'rxjs';

import {
  AnyObject,
  ColumnResult,
  CpmEndToEndViewResult,
  ItemImage,
  NaviView,
  Record,
  SearchCondition,
  ViewColumn,
} from '.';
import { DocumentViewData } from '../../entities';
import { SubSection } from '../../modules/container/document/state/sub-sections.store';
import { TableSelectionMode } from '../../modules/shared/common/cbx-table/cbx-table';
import { ApprovalActionPayload, ApprovalActions, ApprovalProfile, StageApprover } from './approval';
import { ApprovalTemplate } from './approval-template';
import { CommonPopupTable } from './common-popup-table';
import { PopAssigneeView } from './cpm';
import { CpmEndToEndView } from './cpm-end-to-end-view';
import { FileInfo, FileMetaData } from './file-info';
import { Template, TemplateItemsList } from './template';
import { VendorRegistrationPreApproveData } from './vendor-registration';
import {
  ChatDtoMessage_ReferenceDocumentContent,
  Common_EntitySelectionDto,
  CorrectiveActionPlans_CorrectiveActionPlanEnergySourceDto,
} from 'src/app/entities/api';
import { CodelistItem } from 'src/app/entities/codelist';
import { AlertDialogTypes, DialogButtonTypes } from 'src/app/entities/dialog';
import { BaseCriterion, DocumentViewDefine, FieldDefine } from 'src/app/entities/form-field';
import { CapEnergySourceListDto } from 'src/app/modules/container/document/cbx-module/cap/model/cap-energy-source-list-dto';
import { FactAgreementList } from 'src/app/modules/container/document/cbx-module/fact/model/fact-dto';
import { VendorAgreementList } from 'src/app/modules/container/document/cbx-module/vendor/model/vendor-dto';
import { ChainOfCustodyMap } from 'src/app/modules/container/document/cbx-module/vpo/model/vpo-dto';
import {
  FilterCondition,
  WidgetComponent,
  WidgetFilterDefine,
} from 'src/app/modules/container/document/cbx-module/widget/model/widget-dto';
import { DocumentDataState, Section } from 'src/app/modules/container/document/state';
import { VersionComparison } from 'src/app/modules/container/document/version-comparison/model/version-comparison';
import { AuditScheduleEventData } from 'src/app/modules/container/listing/audit-schedule/audit-schedule.service';
import {
  InspectionBookingInspectorState,
  InspectionBookingScheduleEventState,
} from 'src/app/modules/container/listing/inspection-booking-schedule/inspection-booking-schedule.service';

export interface DialogResult<T> {
  type: 'cancel' | 'done';
  payload?: T;
  otherData?: any;
}

export type ColumnEditDialogResult = DialogResult<ColumnResult>;
export type GridColumnSettingResult = DialogResult<SubSection>;
export type TextareaDialogResult = DialogResult<string>;
export type DocumentSelectDialogResult = DialogResult<Record[]>;
export type TableSelectDialogResult = DialogResult<Record[]>;
export type AddRemarkToSnapshotDialogResult = DialogResult<string>;
export type PackingListPopupResult = DialogResult<Record>;
export type BatchSubmissionPopupResult = DialogResult<Record>;
export type TemplateSelectDialogResult = DialogResult<TemplateItemsList[]>;
export type BookMarkDialogResult = DialogResult<{ type: 'new' | 'replace'; name: string }>;
export type WarningDialogResult = DialogResult<boolean>;
export type ExportDialogResult = DialogResult<{ body: string[]; field: string }>;
export type ExportExcelDialogResult = DialogResult<ExportExcelDialogData['setting']>;
export type WidgetEditDialogResult = DialogResult<WidgetComponent>;
export type CpmTrackerDetailPopupComponentResult = DialogResult<any>;
export type KanbanWidgetEditDialogResult = DialogResult<KanbanWidgetEditDialog>;

export interface TableSelectDialogDataSource<C = any> {
  fieldDefine?: FieldDefine;
  rowSelection: TableSelectionMode;
  viewId: string;
  moduleId?: string;
  widgetType: string;
  additionalSearchCondition?: SearchCondition;
  viewData?: DocumentViewData;
  required?: boolean;
  dataKeys?: string[];
  selectedRows?: Record[];
  searchStr?: string;
  footerComponentRef?: ComponentRef<C>;
  canClose?(selectedRows: any[]): Observable<{ canClose: boolean; payload?: any }>;
  alwaysEnable?: boolean;
}
export interface KanbanWidgetEditDialog {
  sourceField: ColDef[];
  viewName: string;
  widgetType: string;
}
export type BatchUpdateFieldDialogResult = DialogResult<{
  entityName: string;
  body: Record[];
  batchUpdateFields: [{ fieldId: string; fieldLabel: string; changeTo: string; inputType: string; actionType: string }];
  saveType: string;
  isChecked: boolean;
  childFieldId?: string;
  recordCount?: number;
  formatByDataAbstract?: string;
}>;
export type BatchUpdateCpmDialogResult = DialogResult<{
  entityName: string;
}>;
export type BatchUpdatePartyDialogResult = DialogResult<{
  entityName: string;
  body: string[];
  fromUserName: string;
  toUserName: string;
  isCheckOwner: boolean;
  selectedPartyType: string;
  actionType: string;
  tmplRef: string;
}>;
export type ImageGalleryEditDialogResult = DialogResult<ItemImage[]>;
export type ApprovalDialogResult = DialogResult<{ result: DocumentViewData; payload: ApprovalActionPayload }>;
export type InviteVendorDialogResult = DialogResult<{ email: string }[]>;
export type VendorRegistrationCongratulationsDialogResult = DialogResult<{ url: string }[]>;
export type ScheduleDetailDialogResult = DialogResult<{
  id: string;
  inspectionDate: string;
  inspectors: Common_EntitySelectionDto[];
}>;
export type RequestForSignaturePopupResult = DialogResult<Record>;
export type SignatureInfoPopupResult = DialogResult<Record>;
export type DnbCompanyMatchPopupResult = DialogResult<Record>;
export type KharonCompanyMatchPopupResult = DialogResult<Record>;
export type DnbShowMorePopupResult = DialogResult<Record>;
export type KharonShowMorePopupResult = DialogResult<Record>;
export type ReferenceDocumentContent = DialogResult<ChatDtoMessage_ReferenceDocumentContent[]>;
export type CpmFilterEditDialogResult = DialogResult<CpmEndToEndViewResult>;
export interface ColumnEditDialogData {
  viewName: string;
  columnDefs: ColDef[];
  mode?: 'list' | 'detail' | 'grid';
  saveView?: boolean;
}

export interface ExpirationPopupDialogResult {
  closeType: 'stayLogin' | 'close';
}
export interface AttachmentPopupDialogResult {
  attachmentList: FileInfo[];
}
export interface GridColumnSettingDialogData {
  sectionId: string;
  section$: Observable<SubSection>;
  moduleId: string;
  section: any;
}

export interface ColumnDefs {
  id: string;
  size: string | number;
  isFrozenColumn: boolean;
  visible: boolean;
  columnGroupShow?: string;
  readonly?: boolean;
  isDynamic?: boolean;
  children?: ColumnDefs[];
}

export interface TextareaDialogData {
  headerLabel: string;
  required: boolean;
  value: string;
  maxLength: number;
  readonly?: boolean;
}

export interface DocumentSelectDialogData {
  title: string;
  additionalSearchCondition?: SearchCondition;
  dataKeys: string[];
  module: string;
}

export interface MapAddress {
  businessName?: string;
  refNo?: string;
  address1?: string;
  address2?: string;
  address3?: string;
  address4?: string;
  amforiFactoryID?: string;
  contactName?: string;
  businessRegistrationNo?;
  gpsLng?: string;
  gpsLat?: string;
  localAddress?: string;
  country?: string;
  countryName?: string;
  room?: string;
  building?: string;
  street?: string;
  district?: string;
  stateProvince?: string;
  stateProvinceName?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  isNeedTransate?: boolean;
  isDefaultAddress?: boolean;
}

export interface MapSelectDialogData {
  defaultAddres: MapAddress;
  moduleInfo: any;
}

export type MapSelectDialogResult = DialogResult<MapAddress>;

export interface TableSelectDialogData<C = any> {
  fieldDefine?: FieldDefine;
  rowSelection: TableSelectionMode;
  viewId: string;
  title: string;
  moduleId?: string;
  additionalSearchCondition?: SearchCondition;
  viewData?: DocumentViewData;
  required?: boolean;
  dataKeys?: string[];
  selectedRows?: Record[];
  searchStr?: string;
  footerComponentRef?: ComponentRef<C>;
  baseCriteria?: BaseCriterion[];
  canClose?(selectedRows: any[]): Observable<{ canClose: boolean; payload?: any }>;
  alwaysEnable?: boolean;
}

export interface ViewTableSelectDialogData<> {
  fieldDefine?: FieldDefine;
  rowSelection: TableSelectionMode;
  title: string;
  dataKeys?: string[];
  selections?: Record[];
  columns: ViewColumn[];
  canClose?(selectedRows: any[]): Observable<{ canClose: boolean; payload?: any }>;
}

export interface DefaultSelectDialogData {
  rowSelection: TableSelectionMode;
  viewId: string;
  title: string;
  moduleId?: string;
  dataKeys?: string[];
  selectedRows?: Record[];
  canClose?(selectedRows: any[]): Observable<{ canClose: boolean; payload?: any }>;
}

export interface TemplateSelectDialogData {
  rowSelection: TableSelectionMode;
  title: string;
  templateList?: Template[];
  dataKeys?: string[];
  selectedRows?: TemplateItemsList[];
  viewId?: string;
}

export interface RequirementSelectDialogData {
  rowData: any[];
  templateName: string;
  rowSelection: TableSelectionMode;
}

export interface SaveFilterDialogData {
  filterName?: string;
  existingNames?: string[];
}

export interface BookMarkDialogData {
  naviView?: NaviView;
  searchCriteria?: SearchCondition;
  naviModuleId?: string;
  moduleLabel?: string;
  currentNaviView?: NaviView;
}

export interface AlertDialogData {
  messageButtons: DialogButtonTypes;
  message: string;
  title: AlertDialogTypes;
  titleColor?: string;
}

export interface ExportDialogData {
  body: string[];
}

export interface ExportExcelDialogData {
  setting?: {
    fileName?: string;
    fontSize?: number;
    onlySelected?: boolean;
    allColumns?: boolean;
  };
  option?: {
    disabledOnlySelected?: boolean;
    hideOnlySelectedOption?: boolean;
    title?: string;
  };
}

export interface BatchUpdateFieldDialogData {
  entityName: string;
  body: Record[];
  recordCount?: number;
  isSupportSendToVendor: boolean;
  childFieldId?: string;
  viewName?: string;
  batchUpdateLineItemProgressBarData?: BatchUpdateLineItemProgressBarData[];
  childDtoFieldName?: string;
  moduleId?: string;
}

export interface BatchUpdateLineItemProgressBarData {
  id: string;
  displayField: string;
  childId: string;
  docRef: string;
  processStatus?: string;
}

export interface BatchUpdateProgressBarData {
  id?: string;
  docId?: string;
  displayField?: string;
  childId?: string;
  formatValue?: string;
  refNo?: string;
  childIds?: string[];
  docRef?: string;
  processStatus?: string;
  action?: string;
  sourceEntityName?: string;
  sourceIds?: string;
  targetEntityName?: string;
  count?: number;
  childDtoFieldName?: string;
  batchUpdateFields?: { fieldId: string; changeTo: string; inputType: string }[];
  targetRefNos?: string;
  vendorAgreementId?: string;
  vendorAgreementCode?: string;
  vendorAgreementName?: string;
  failReason?: string;
  batchUpdateStatus?: string;
  reasons?: string;
}

export interface ShareFileListDialogData {
  edit: boolean;
  data: any;
  field?: FieldDefine;
  moduleId?: string;
  modeTemplate?: string;
}

export interface BatchUpdateNotificationData {
  refNo?: string;
  docId?: string;
  message?: string;
  success?: boolean;
  docRefs?: string;
  contractFile?: string;
  failReason?: string;
  markAsDocVersion?: number;
  isStatusResult?: boolean;
  fieldLabel?: string;
}

export interface batchSubmissionPopupData {
  selectedRows?: string;
  refNo?: string;
  version?: string;
  addRecords?: Record[];
  delRecords?: Record[];
  maxCartonNo?: number;
  readonlyList?: string[];
  formShowField?: string[];
}

export interface BatchUpdateCocDetailProcessDialogData {
  id?: string;
  docId?: string;
  displayField?: string;
  childId?: string;
  formatValue?: string;
  refNo?: string;
  childIds?: string[];
  docRef?: string;
  processStatus?: string;
  action?: string;
  sourceEntityName?: string;
  sourceIds?: string;
  targetEntityName?: string;
  count?: number;
  childDtoFieldName?: string;
  batchUpdateFields?: { fieldId: string; changeTo: string; inputType: string }[];
  targetRefNos?: string;
  vendorAgreementId?: string;
  vendorAgreementCode?: string;
  vendorAgreementName?: string;
  reasons?: string;
  failReason?: string;
  cocId?: string;
  vpo?: BatchSubmissionVpoPopupData;
  sectionName?: string;
  description?: string;
}

export interface BatchSubmissionVpoPopupData {
  id: string;
  refNo: string;
  version: number;
  vpoNo: string;
}

export interface BatchUpdateNotificationData {
  refNo?: string;
  docId?: string;
  message?: string;
  success?: boolean;
  docRefs?: string;
  contractFile?: string;
  failReason?: string;
}

export interface BatchUpdateCpmDialogData {
  entityName: string;
  moduleId: string;
  body: Record[];
  failedMessage: string;
  successMessage: string;
  filterByCpmAssignToMe: boolean;
  filterByCpmInComplete: boolean;
}
export interface BatchUpdatePartyDialogData {
  entityName: string;
  body: string[];
  isMarkAsOwnerConfig: string;
}

export interface CommonPopupDialogData<T1> {
  component: ComponentType<CommonPopupTable>;
  title: string;
  needWarningDialog: boolean;
  data: T1 | any;
}

export interface PartySelectDialogData {
  selectionMode: TableSelectionMode;
  moduleId: string;
  refNo: string;
  filterFormCtrl: UntypedFormControl;
}

export interface UrlImageViewDialogData {
  urlImage: string;
}

export interface ImageGalleryViewDialogData {
  images: ItemImage[];
  activeIndex?: number;
  multipleMode?: boolean;
  imageNumber?: number;
  suppressComments?: boolean;
}

export interface PdfViewerDialogData {
  attachment: FileInfo;
}

export interface ImageGalleryEditDialogData {
  images: ItemImage[];
  activeIndex?: number;
  uploadImages?: File[];
  multipleMode?: boolean;
  infiniteImage?: boolean;
  imageNumber?: number;
  filterHeaderType?: boolean;
  uploadLimitedType?: string;
  uploadFromDrop?: boolean;
  promptMessage?: string;
}

export interface ShareFiletDialogData {
  images?: ItemImage[];
  activeIndex?: number;
  uploadImages?: File[];
  multipleMode?: boolean;
  infiniteImage?: boolean;
  imageNumber?: number;
  filterHeaderType?: boolean;
  uploadLimitedType?: string;
  uploadFromDrop?: boolean;
  promptMessage?: string;
  isEditMode?: boolean;
  shareFileList?: any[];
  recordCountLabel?: string;
  fieldLabel?: string;
}

export interface ApprovalHistoryDialogData {
  approvalProfiles: ApprovalProfile[];
}

export interface ApprovalActionDialogData {
  docId?: string;
  action: ApprovalActions;
  approvalProfiles: ApprovalProfile[];
  activeAssignees: StageApprover[];
  latestProfileId: string;
  latestStageId: string;
  moduleId: string;
  refNo: string;
  subject: string;
  rejectReasonOptions?: Observable<CodelistItem[]>;
  submitOutside?: boolean;
  differentData?: VersionComparison[];
  docVersion?: string;
}

export interface ApprovalTemplateDialogData {
  selectionMode: TableSelectionMode;
  approvalTemplates: ApprovalTemplate[];
  filterFormCtrl: UntypedFormControl;
}

export interface RelatedDocumentDialogData {
  moduleId: string;
  docId: string;
  docRefNo: string;
  docVersion: number;
  documentData: DocumentDataState;
  showReplaceBomButton: boolean;
}

export interface VendorRegistrationPreApproveDialogData {
  title?: string;
  content?: Observable<VendorRegistrationPreApproveData>;
}

export interface VendorRegistrationCongratulationsDialogData {
  refNo: string;
  relationshipType: string;
  requesterName$: Observable<string>;
  isSubUser: boolean;
}

export interface CpmToolsDialogData {
  actionName: string;
  dialogTitle: string;
  dialogRef: DialogRef<any>;
  rootComponent?: any;
  cpmTemplId?: string;
}

export interface CpmTrackerDetailPopupData {
  module: string;
  refNo: string;
  docVersionStatus: string;
  businessReference: string;
  updatedOn: string;
  updateUserName: string;
  reloadData?: boolean;
  isAssignToMe?: boolean;
}

export interface CostSheetPopupData {
  isNeedEditCost?: boolean;
  module: string;
  refNo: string;
  formData: AnyObject<any>;
}
export interface InviteVendorOrFactData {
  moduleId: string;
}

export interface AgreementAgreedPopupData {
  module: string;
  refNo: string;
  body: VendorAgreementList | FactAgreementList;
}

export interface DialogFormFieldData {
  readonly: boolean;
  sectionId: string;
  fields: FieldDefine[][];
  formData: AnyObject<any>;
  title: string;
  codelistBookData?: any;
}

export interface GridSelectorData {
  selectionMode: TableSelectionMode;
  selectedRecords?: string[];
  selectedReferenceKey?: string[];
  rowData: AnyObject<any>[];
  fields: FieldDefine[];
  rowHeight: number;
  filterFormCtrl: UntypedFormControl;
}

export interface CreateOrEditViewDialogData {
  moduleName: Observable<string>;
  columnDefs: ColDef[];
  viewName: string;
  moduleId?: string;
  naviGroup?: string;
  searchCriteria?: Observable<SearchCondition>;
  isEditView?: boolean;
  shareWithList?: PopAssigneeView[];
  viewType?: string;
  viewId?: string;
  description?: string;
  options?: Map<string, string>;
  baseViewName?: string;
}

export interface ScheduleDetailDialogData {
  eventDetail?: InspectionBookingScheduleEventState;
  inspectorCodelist?: Observable<InspectionBookingInspectorState[]>;
  from: string;
}

export interface AuditScheduleDetailDialogData {
  eventDetail: AuditScheduleEventData;
  inspectorCodelist: Observable<CodelistItem[]>;
  from: string;
}

export interface SelectCpmTemplateDialogData {
  moduleId: string;
  refNo: string;
  rootComponent?: any;
}

export interface SelectCpmAssigneeDialogData {
  viewData: DocumentViewData;
  rowSelection: TableSelectionMode;
  dataKeys?: string[];
  selectedRows?: Record[];
  needParties?: boolean;
}

export interface FactAuditImageAndAttachmentDialogData {
  edit: boolean;
  data: DocumentDataState;
  sectionId?: string;
  docVersion?: string;
  docRefNo?: string;
}

export interface CorrectiveActionDialogData {
  edit: boolean;
  data: DocumentDataState;
  sectionId?: string;
  docVersion?: string;
  docRefNo?: string;
}

export interface PopupDefineColumnsDialogData {
  edit: boolean;
  data: CapEnergySourceListDto;
  sectionId?: string;
  scoreCalculation?: string;
}

export interface QualityPlanTemplateDialogData {
  edit: boolean;
  data: DocumentDataState;
  sectionId?: string;
  params: any;
}

export type CorrectiveActionDialogResult = DialogResult<CorrectiveActionPlans_CorrectiveActionPlanEnergySourceDto[]>;

export interface AuthenticatorData {
  secretKey: string;
  status: string;
  loginId: string;
  errorMessage: string;
}

export interface QuoteCompareDialogData {
  moduleId: string;
  refNo?: string;
  refNos?: string[];
  costSheetRef?: string;
}

export interface RequirementSelectDialogData {
  rowData: any[];
  templateName: string;
  rowSelection: TableSelectionMode;
}

export interface AuditScheduleDetailDialogData {
  eventDetail: AuditScheduleEventData;
  inspectorCodelist: Observable<CodelistItem[]>;
  from: string;
}

export interface PackingListPopupData {
  selectedRows?: string;
  addRecords?: Record[];
  delRecords?: Record[];
  maxCartonNo?: number;
  readonlyList?: string[];
  isViewInForm?: boolean;
}

export interface PackingListDetailSplitPopupData {
  selectedRow?: string;
  cartonNo?: string;
  orgRecords?: Record[];
}

export interface batchSubmissionCOCPopupData {
  moduleId: string;
  refNo?: string;
}

export interface ChainOfCustodySupplierSelectDialogData {
  vendorRefNo: string;
  factRefNos: string[];
  refNo: string;
  supplier?: AnyObject<any>[];
}

export interface ChainOfCustodySelectDialogData {
  vpoRefNo: string;
  itemNo: string;
}

export interface AttachmentFieldsDialogData {
  edit: boolean;
  data: any;
  field?: FieldDefine;
  moduleId?: string;
  modeTemplate?: string;
  dialogUploadTask?: DialogUploadTask;
}

export interface ShareFileFieldsDialogData {
  edit: boolean;
  data: any;
  field?: FieldDefine;
  moduleId?: string;
  modeTemplate?: string;
  dialogUploadTask?: DialogUploadTask;
  docVersion?: string;
  docRefNo?: string;
  originalDocData?: any;
  popupShareFileDefine?: DocumentViewDefine;
}

export interface DialogUploadTask {
  isNeedUpload?: boolean;
  uploadFiles?: any[];
}

export interface AttachmentPopupDialogData {
  edit: boolean;
  attachmentList?: FileInfo[];
  uploadFiles?: ArrayLike<FileMetaData>;
}

export interface RequestForSignaturePopupData {
  formData?: AnyObject<any>;
  actionParams?: any;
  moduleId?: string;
  signatureShareFile?: any;
  defaultData?: any;
}

export interface SignatureInfoPopupData {
  formData?: AnyObject<any>;
  actionParams?: any;
}

export interface CodelistEditDialogData {
  codelistItem?: any;
  columnDefs?: ColDef[];
  sectionId?: string;
  section?: Section;
  codelistBookData?: any;
  documentDefineStore?: any;
}

export interface CodelistItemData {
  bookName: string;
  bookRefNo: string;
  bookVersion: number;
  code: string;
  name: string;
  parentId: string;
  disabled: boolean;
  isNew: boolean;
  condition: string;
  customFields: AnyObject<any>;
}

export interface FactAuditParentInfoSelectDialogData {
  factRefNo: string;
  supplier?: AnyObject<any>[];
}

export interface CapParentInfoSelectDialogData {
  factRefNo: string;
  supplier?: AnyObject<any>[];
}

export type SelectTypeDialogResult = DialogResult<{
  id: string;
  refNo: string;
  name: string;
  version: number;
}>;

export interface SelectTypeDialogData {
  entityName: string;
  message: string;
  title: string;
  currentHclTypeValue?: string;
}

export interface CustFieldDetailDialogData {
  edit: boolean;
  data: DocumentDataState;
  moduleId: string;
  id?: string;
}

export interface DefaultContactDialogData {
  edit: boolean;
  data: DocumentDataState;
  id?: string;
}

export type LookupTypeDialogResult = DialogResult<{
  id: string;
  refNo: string;
  name: string;
  version: number;
}>;

export interface LookupTypeDialogData {
  entityName: string;
  dataListTypeName: string;
  message: string;
  title: string;
}

export type ImportDialogResult = DialogResult<{
  headerRow?: number;
  startRow?: number;
  file?: any;
  fileIdId?: any;
}>;

export interface ImportDialogData {
  entityName?: string;
  moduleId?: string;
  message?: string;
  title: string;
  headerRow?: number;
  startRow?: number;
  fileId?: any;
}

export interface ChangeHistoryDialogData {
  moduleId: string;
  tabId: string;
  refNo: string;
}

export interface SnapshotDialogData {
  moduleId: string;
  tabId: string;
  refNo: string;
}

export interface WidgetContainerDialog extends WidgetComponent {
  filterDefineList?: WidgetFilterDefine[];
  widgetColorMap?: AnyObject<AnyObject<string>>;
  viewUrl?: string;
  isListingViewMode?: boolean;
  widgetIndex?: number;
  defaultQueryConditions?: AnyObject<FilterCondition>;
  widget?: WidgetComponent;
}

export interface VpoTraceabilityDialogData {
  chainOfCustodyMap: ChainOfCustodyMap;
  popupLabel: string;
  colorfulMap: AnyObject<string>;
}

export interface CpmFilterEditDialogData {
  viewName: string;
  cpmEndToEndView: CpmEndToEndView;
}

export interface RestoreLastWorkingVendorData {
  userId: string;
  lastLogonDomain: string;
  lastLogonUserName: string;
}

export interface AttachmentGalleryEditDialogData {
  edit: boolean;
  images: ItemImage[];
  activeIndex?: number;
  uploadImages?: File[];
  multipleMode?: boolean;
  infiniteImage?: boolean;
  imageNumber?: number;
  filterHeaderType?: boolean;
  isBarcodeScanner?: boolean;
}

export interface DnbCompanyMatchPopupData {
  formData?: AnyObject<any>;
  responData?: any;
  responError?: any;
  moduleId?: string;
}

export interface DnbShowMorePopupData {
  formData?: AnyObject<any>;
}

export interface KharonCompanyMatchPopupData {
  formData?: AnyObject<any>;
  responData?: any;
  responError?: any;
  moduleId?: string;
}

export interface KharonShowMorePopupData {
  formData?: AnyObject<any>;
}

export interface PopupDocumentDetailData {
  edit: boolean;
  moduleId: string;
  refNo: string;
  id: string;
  action: 'create' | 'view' | 'snapshot';
  tabId: string;
  documentData: DocumentDataState;
  documentData$: Observable<DocumentDataState>;
  define$: Observable<DocumentViewDefine>;
  actionParams?: any;
}

export interface RefreshPageSwitchVendorData {
  vendorUserOnBehalfValue: string;
}
export interface ReadUserDetailDialogData {
  title: string;
  readRatio: string;
  unreadRatio: string;
  moduleId: string;
  refNo: string;
}

export interface AcknowledgedUserDetailDialogData {
  title: string;
  acknowledgedRatio: string;
  unacknowledgedRatio: string;
  moduleId: string;
  refNo: string;
}
