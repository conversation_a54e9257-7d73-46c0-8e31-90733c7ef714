import { inject, Injectable } from '@angular/core';

import { addSuffix } from '../../../../../../utils';
import { editSuffix } from '../../../service/cell-mapper-constant';
import { CellMapperRootService } from '../../../service/cell-mapper-root.service';
import { OptionalCellMapperProperty } from '../../../service/cell-mapper.property';
import { communicationEditComponents, communicationViewComponents } from '../cell/communication-cell-component-type';

@Injectable()
export class CommunicationCellMapperService implements OptionalCellMapperProperty {
  cellMapperRootService = inject(CellMapperRootService);

  viewComponents = communicationViewComponents;
  editComponents = addSuffix(communicationEditComponents, editSuffix);

  cellComponents = {
    ...this.viewComponents,
    ...this.editComponents,
  };

  fieldDefineGetterMap = {
    communicationAttachmentList: {
      file: this.cellMapperRootService.attachmentOrUrlGetter,
      attachment: this.cellMapperRootService.attachmentOrUrlGetter,
    },
  };
}
