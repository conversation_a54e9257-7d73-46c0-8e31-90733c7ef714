<ng-template [ngTemplateOutlet]="default" [ngTemplateOutletContext]="{}"></ng-template>
<ng-template #default>
  <div class="cbx-chat-list-topbar-wrapper">
    <div class="cbx-chat-list-title">
      <button
        type="button"
        class="select-wrapper flex justify-between items-center pointer bg-transparent border-0 padding-0 outline-0 width-100"
        cdkOverlayOrigin
        #menuTrigger="cdkOverlayOrigin"
        (click)="chatListTitleIsOpen = !chatListTitleIsOpen"
      >
        {{ selectedChatClassifyValue$ | async }}
        <span class="arrow-down-icon"></span>
      </button>

      <ng-template
        cdkConnectedOverlay
        [cdkConnectedOverlayOrigin]="menuTrigger"
        [cdkConnectedOverlayOpen]="chatListTitleIsOpen"
        (overlayKeydown)="overlayKeydown($event)"
        (overlayOutsideClick)="clickOutsideMenu($event, menuTrigger)"
      >
        <div class="cbx-chat-list-overlay-menu">
          <button
            *ngFor="let classify of chatClassifies$ | async"
            class="cbx-chat-room-list-menu-item"
            [class.cbx-chat-room-list-menu-item-active]="(selectedChatClassify$ | async) === classify"
            (click)="chatListTitleIsOpen = false; changeChatClassify(classify)"
          >
            {{ classify.label }}
          </button>
        </div>
      </ng-template>
    </div>

    <button class="cbx-chat-list-icon-button" (click)="createRoom()">
      <cbx-create-icon class="cbx-chat-list-icon"></cbx-create-icon>
    </button>
    <div class="cbx-chat-list-spacer"></div>
    <button class="cbx-chat-list-icon-button" (click)="close()">
      <cbx-close-icon class="cbx-chat-list-icon"></cbx-close-icon>
    </button>
  </div>
  <div class="cbx-chat-list-search-wrapper">
    <label class="cbx-chat-list-search" [class.cbx-chat-list-display-search-input]="displaySearchInput$ | async">
      <cbx-search-icon class="cbx-chat-list-icon"></cbx-search-icon>
      <input
        #search
        type="text"
        placeholder="Keywords"
        class="cbx-chat-list-search-input"
        (input)="searchInput$.next(search.value)"
        (focus)="searchInputFocused$.next(true)"
        (blur)="searchInputFocused$.next(false)"
      />
    </label>
  </div>

  <div class="cbx-chat-list-content-wrapper" cdkMenuBar>
    <cdk-virtual-scroll-viewport itemSize="78" class="cbx-chat-list-items-viewport">
      <div
        *cdkVirtualFor="let room of selectableChatRoom$ | async; trackBy: roomTrackBy"
        class="cbx-chat-list-item-wrapper"
        [class.cbx-chat-list-item-active]="(targetRoom$ | async)?.id === room?.id"
        (click)="openRoom(room)"
      >
        <div class="cbx-chat-list-item-inner-wrapper">
          <div class="cbx-chat-list-item-hint-wrapper align-center">
            <div
              class="cbx-chat-list-unread-hint"
              [class.cbx-chat-list-opacity-0]="room.lastReadMessageId === room.lastMessage?.id && !room.unread"
            ></div>
          </div>

          <cbx-chat-room-avatar class="align-center" [chatRoom]="room" />

          <div class="cbx-chat-list-item-content-wrapper">
            <div class="cbx-chat-list-item-title-wrapper">
              <h3 class="cbx-chat-list-item-title">{{ room | roomTopic : userId }}</h3>
              <time class="cbx-chat-list-item-date">{{
                room.lastMessage?.created || room.created | dateLabelFormat : dateType : (dateFormat$ | async)
              }}</time>
            </div>
            <div class="cbx-chat-list-item-description-wrapper">
              <div class="cbx-chat-list-item-description">
                <cbx-chat-list-message
                  *ngIf="room.lastMessage?.content"
                  [chatMessage]="room.lastMessage"
                  [isSenderNameHidden]="room.isSenderNameHidden"
                ></cbx-chat-list-message>
              </div>
              <div class="cbx-chat-list-item-status-wrapper flex">
                <cbx-lock-icon
                  class="cbx-chat-list-icon cbx-chat-list-lock-icon"
                  [class.cbx-chat-list-opacity-0]="room.visible !== 'PRIVATE'"
                ></cbx-lock-icon>

                <button
                  tabindex="-1"
                  *ngIf="room.userFavorite"
                  class="cbx-chat-list-pin-icon pointer width-100"
                  (click)="toggleFavorite(room); $event.stopPropagation()"
                >
                  <app-icon svgIcon="pin"></app-icon>
                </button>
              </div>
            </div>
          </div>
          <button
            class="cbx-chat-room-more-btn pointer padding-0 border-0"
            cdkMenuItem
            [cdkMenuTriggerFor]="menuPanel"
            (click)="$event.stopPropagation()"
          >
            <app-icon svgIcon="more" class="icon-size-14"></app-icon>
          </button>

          <ng-template #menuPanel>
            <div class="menu-panel-container" cdkMenu>
              <ng-container *ngFor="let setting of chatRoomSettingList">
                <button
                  *ngIf="handleSettingActionExistCondition(setting.action, room)"
                  cdkMenuItem
                  class="menu-panel-button flat-button flat-button-height-32"
                  (click)="handleSettingAction(setting.action, room)"
                >
                  <span class="cbx-chat-list-menu-item-label">{{ setting.label }}</span>
                </button>
              </ng-container>
            </div>
          </ng-template>
        </div>
      </div>

      <div
        *ngIf="hasMoreRooms$ | async"
        class="cbx-chat-list-item-wrapper"
        cbxInViewport
        (inViewport)="loadingInViewport$.next($event)"
      >
        <div class="cbx-chat-list-item-inner-wrapper">
          <div class="cbx-chat-list-item-hint-wrapper align-center">
            <div class="cbx-chat-list-unread-hint cbx-chat-list-opacity-animation"></div>
          </div>
          <div class="cbx-chat-list-item-content-wrapper">
            <div class="cbx-chat-list-item-title-wrapper">
              <h3 class="cbx-chat-list-item-title">
                <div class="cbx-chat-list-loading-brick cbx-chat-list-opacity-animation"></div>
              </h3>
              <span class="cbx-chat-list-item-date"
                ><div
                  class="cbx-chat-list-loading-brick cbx-chat-list-opacity-animation cbx-chat-list-animation-delay"
                ></div
              ></span>
            </div>
            <div class="cbx-chat-list-item-description-wrapper">
              <div class="cbx-chat-list-item-description">
                <div
                  class="cbx-chat-list-loading-brick cbx-chat-list-opacity-animation cbx-chat-list-animation-delay"
                ></div>
              </div>
              <div class="cbx-chat-list-item-status-wrapper flex">
                <cbx-lock-icon
                  class="cbx-chat-list-icon cbx-chat-list-lock-icon cbx-chat-list-opacity-animation"
                ></cbx-lock-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </cdk-virtual-scroll-viewport>
    <div *ngIf="noRooms$ | async" class="cbx-chat-list-new-room-wrapper">
      <button class="cbx-chat-list-icon-button" (click)="createRoom()">
        <cbx-create-icon class="cbx-chat-list-icon"></cbx-create-icon>
        <span class="cbx-chat-list-new-room-label">{{ newRoomLabel$ | async }}</span>
      </button>
    </div>
  </div>
</ng-template>
