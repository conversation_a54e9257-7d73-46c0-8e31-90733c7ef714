export const DOMAIN_ATTRIBUTES = {
  FORMAT_DATE: 'ui.format.date',
  FORMAT_DATETIME: 'ui.format.datetime',
  HOMEPAGE_PANEL_SETTING: 'ui.homePage.panel.enabled',
  CREATE_VIEW_ENABLED: 'ui.view.createview.enabled',
  CROSS_MODULE_ALERT_SETTING: 'cbx.feature.CrossModuleAlerts',
  COMPANY_BRAND_URL: 'ui.company.brand.path',
  AUTO_VALIDATE_QA_RESULT: 'ui.sampleEvaluation.fitAssessment.measurement.qaResult.auto',
  DOCUMENT_FORM_FIELD_MIN_WIDTH: 'ui.document.form.field.min.width.px',
  DOCUMENT_FORM_FIELD_MAX_WIDTH: 'ui.document.form.field.max.width.px',
  CPM_DUE_SOON_DAYS: 'cpm.duesoon.days',
  CPM_CALCULATE_DATE_LATEST: 'cpm.calculateDate.Latest',
  CPM_CALCULATE_COLOR_CODE: 'cpm.calculate.colorCode',
  RESPONSIBLE_BY_ME: 'cbx.responsibleByMe.enable.modules',
  FORUM_DEFAULT_SELECT_REF_OPTION: 'forum.defaultSelectRefOption',
  CBX_DEMO_FEATURE: 'cbx.demo.feature',
  SAMPLE_EVALUATION_BATCH_UPDATE_SEND_TO_VENDOR: 'sampleEvaluation.batch.update.support.sendToVendor',
  UI_LOCALE: 'ui.locale',
  BATCH_UPDATE_RESPONSIBLE_PARTY_RE_INITIAL_DISABLE: 'batchUpdate.responsibleParty.reInitial.disable',
  VPO_PLANNEDQTY_ORDERQTY_CONTROL: 'vpo.PlannedqtyOrderqty.control',
  CBX_ROW_GROUPING_VIEW: 'cbx.row.grouping.view',
  PARTIAL_COPY_TO_ITEM_MAXIMUN_DOCMENT: 'system.partial.copy.to.item.maximum.document',
  IB_AUTOSCHEDULE_ENABLE: 'ib.autoschedule.enable',
  ENABLE_FORM_APPROVALSTATUSLABEL: 'form.approvalStatusLabel.enable',
  UN_READ: 'cbx.unread.enable.modules',
  SUPERSET_DEFAULT_HEIGHT: 'ui.homepage.superset.height',
  SYSTEM_BATCH_UPDATE_MAXIMUM_DOCUMENT: 'system.batch.update.maximum.document',
  SYSTEM_BATCH_UPDATE_MAXIMUM_PARTY: 'system.batch.update.maximum.party',
  SYSTEM_SESSION_TIMEOUT_MINUTES: 'system.webapp.sessionTimeout.minutes',
  CBX_CONCURRENT_EDIT_ENABLED_MODULES: 'cbx.concurrent.edit.enabled.modules',
  SAMPLE_EVALUATION_ENABLED: 'sampleEvaluation.enabled',
  SHIPMENT_SUBSCRIPTION_ACTIVATED: 'shipment.subscription.activated',
  SYSTEM_BATCH_UPDATE_MAXIMUM_VPO: 'system.batch.update.maximum.vpo',
  ENABLE_VENDOR_FACT_STATUS_SYNC: 'system.vendorFactoryStatusSync.enable',
  SPECIFIC_SMART_ALERT_MODULES: 'system.show.specific.smart.alert.modules',
  UI_LISTING_WIDGETS_ENABLED: 'ui.listingViews.widgets.enabled',
  UI_WIDGETS_MOCK_DATA: 'ui.widgets.mock.data',
  INSPECT_BOOKING_PLANNED_INSPECT_DATE_DAYS_IN_ADVANCE: 'inspectBooking.plannedInspectDate.daysInAdvance',
  UI_GRID_PAGING_CONFIG: 'ui.grid.paging.config',
  UI_DOCUMENT_GRID_IMAGE_SIZE: 'ui.document.grid.image.size',
  FILE_BLACKLISTED_EXTENSIONS: 'file.blacklisted.extensions',
  BATCH_UPDATE_GRID_SECTION_FIELD_VIEWS: 'ui.enable.new.batchupdate.view',
  UI_SECTION_FIELD_LABEL_WIDTH: 'ui.section.field.label.width.px',
  PAGING_GRID_VPO_ITEM: 'paging.grid.vpo.vpoItem',
  PAGING_GRID_VPO_SHIP_DETAIL: 'paging.grid.vpo.vpo.shipDetail',
  PAGING_GRID_VPO_ITEM_CS: 'paging.grid.vpo.vpoItemCS',
  PAGING_GRID_VPO_SHIP_DETAIL_CS: 'paging.grid.vpo.vpo.shipDetailCS',
  PAGING_GRID_SZIE_VPO_ITEM: 'paging.grid.size.vpo.vpoItem',
  PAGING_GRID_SZIE_VPO_SHIP_DETAIL: 'paging.grid.size.vpo.vpo.shipDetail',
  PAGING_GRID_SZIE_VPO_ITEM_CS: 'paging.grid.size.vpo.vpoItemCS',
  PAGING_GRID_SZIE_VPO_SHIP_DETAIL_CS: 'paging.grid.size.vpo.vpo.shipDetailCS',
  UI_EXTERNAL_CHATROOM_ENABLE: 'cbx.ui.external.chatRoom.enable',
  EXPORT_SEARCH_VIEW_MAX_RECORDS: 'export.searchview.maxrecords',
  PAGING_GRID_TEST_REPORT_DETAIL: 'paging.grid.testReport.tabTestDetails',
  PAGING_GRID_SZIE_TEST_REPORT_DETAIL: 'paging.grid.size.testReport.tabTestDetails',
  CBX_SITE_ID: 'cbx.site.id',
  INSPECTBOOKING_SPLITINSPECTION: 'inspectBooking.splitInspection',
  SHARE_FILE_DEFAULT_FILE_TYPE: 'cbx.sharefile.default.fileType',
  AUTOFILL_VPOSHIPCS_COLORSIZEQTY_ENABLE: 'autoFill.vpoShipCsColorSizeQty.enable',
  INSPECT_BOOKING_ENABLE_QUALITY_PLAN: 'inspectBooking.enableQualityPlan',
  FACT_AUDIT_ENABLE_QUALITY_PLAN: 'factAudit.enableQualityPlan',
  UI_COMPARE_QUOTATIONS_EXPORT_ENABLED: 'ui.compare.quotations.export.enabled',
  UI_COMPARE_QUOTATIONS_ACCEPT_ENABLED: 'ui.compare.quotations.accept.enabled',
  UI_COMPARE_QUOTATIONS_REJECT_ENABLED: 'ui.compare.quotations.reject.enabled',
  UI_COMPARE_QUOTATIONS_ORIGINAL_ENABLED: 'ui.compare.quotations.original.enabled',
  UI_COMPARE_QUOTATIONS_WHATIF_ENABLED: 'ui.compare.quotations.whatif.enabled',
  UI_COMPARE_QUOTATIONS_FINANCIALSUMMARY_ENABLED: 'ui.compare.quotations.financialSummary.enabled',
  FORMAT_VPOITEMPACK_REFNO: 'format.vpoItemPack.refNo',
  UI_CONCURRENTEDITMODE_SNAPSHOT_ENABLED: 'ui.concurrentEditMode.snapshot.enabled',
  UI_IMAGE_MARKUP_ENABLED: 'ui.image.markup.enabled',
  VPO_ITEM_CS_DEFAULT_NO_ROW: 'vpoItemCS.default.no.row',
  SYSTEM_ATTACHMENT_PROTOCOL: 'system.attachment.protocol',
  BATCH_MARK_AS_IN_BACKEND: 'batch.markAs.in.backend',
  MULTIPACK_INSPECTION_ENABLED: 'multipack.inspection.enabled',
  VPO_COC_LEVEL: 'vpo.coc.level',
} as const;

export type DomainAttributeState = {
  [prop in (typeof DOMAIN_ATTRIBUTES)[keyof typeof DOMAIN_ATTRIBUTES]]?: string;
};

export interface DomainAttributeStates {
  [prop: string]: string;
}

export interface HomePagePanelSetting {
  bookmarkBar?: boolean;
  reports?: boolean;
  superSet?: boolean;
  scanQRCode?: boolean;
  widget?: boolean;
}
