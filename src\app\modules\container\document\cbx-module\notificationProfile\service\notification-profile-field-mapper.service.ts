import { Injectable } from '@angular/core';

import { OptionalFieldMapperProperty } from '../../../service/field-mapper.property';
import { CareInstructionSelectionViewComponent } from '../../item/field/view-cell';
import { ItemFieldTypes } from '../../item/model/item-field-type';

@Injectable()
export class NotificationProfileFieldMapperService implements OptionalFieldMapperProperty {
  viewComponents = {
    [ItemFieldTypes.CareInstructionImageSelect]: CareInstructionSelectionViewComponent,
  } as const;
}
