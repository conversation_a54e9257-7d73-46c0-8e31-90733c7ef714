<app-cbx-dialog-header>{{ title }}</app-cbx-dialog-header>

<div class="tab-wrapper flex">
  <button
    class="tab pointer"
    [class.tab-active]="(currentStatus$ | async) === 'ACKNOWLEDGED'"
    (click)="currentStatus$.next('ACKNOWLEDGED')"
  >
    {{ acknowledgedTabLabel }}
  </button>

  <button
    class="tab pointer"
    [class.tab-active]="(currentStatus$ | async) === 'UNACKNOWLEDGED'"
    (click)="currentStatus$.next('UNACKNOWLEDGED')"
  >
    {{ unacknowledgedTabLabel }}
  </button>
</div>

<div class="table-wrapper padding-x-16 height-100">
  <app-cbx-table
    [components]="cbxTableCellComponents"
    class="flex-auto common-grid"
    [oneLine]="false"
    [defaultColDef]="defaultColDef"
    [columnDefs]="colDefs"
    [rowHeight]="rowHeight"
    [rowData]="currentRecord"
    [loading]="loading$ | async"
    (gridReady)="gridReady($event)"
  ></app-cbx-table>
</div>
