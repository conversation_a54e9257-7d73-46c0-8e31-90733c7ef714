import { ChangeDetectionStrategy, Component, inject } from '@angular/core';

import { <PERSON><PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';

import { DocumentComponentStrategy } from '../../../document-switch/document-component-strategy';
import { GroupRowActionDispatcherService } from '../../grid-section-content/service/group-row-action-dispacher.service';
import { CellMapperService } from '../../service/cell-mapper.service';
import { DocumentActionService } from '../../service/document-action.service';
import { DocumentDropdownService } from '../../service/document-dropdown.service';
import { DocumentDynamicDataService } from '../../service/document-dynamic-data.service';
import { DocumentDynamicFieldService } from '../../service/document-dynamic-field.service';
import { DocumentFacadeService } from '../../service/document-facade.service';
import { DocumentFieldReadonlyService } from '../../service/document-field-readonly.service';
import { DocumentValueChangeService } from '../../service/document-value-change.service';
import { FieldMapperService } from '../../service/field-mapper.service';
import { SectionHeaderContentMapperService } from '../../service/section-header-content-mapper.service';
import { CompTemplActionService } from './service/comp-templ-action.service';
import { CompTemplCellMapperService } from './service/comp-templ-cell-mapper.service';
import { CompTemplDropdownService } from './service/comp-templ-dropdown.service';
import { CompTemplDynamicDataService } from './service/comp-templ-dynamic-data.service';
import { CompTemplDynamicFieldService } from './service/comp-templ-dynamic-field.service';
import { CompTemplFacadeService } from './service/comp-templ-facade.service';
import { CompTemplFieldMapperService } from './service/comp-templ-field-mapper.service';
import { CompTemplFieldReadonlyService } from './service/comp-templ-field-readonly.service';
import { CompTemplSectionHeaderContentMapperService } from './service/comp-templ-section-header-content-mapper.service';
import { CompTemplValueChangeService } from './service/comp-templ-value-change-service';
// import { CompTemplValueChangeService } from './service/comp-templ-value-change.service';

@UntilDestroy()
@Component({
  selector: 'app-comp-template',
  template: '',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: DocumentFacadeService,
      useClass: CompTemplFacadeService,
    },
    {
      provide: DocumentDynamicDataService,
      useClass: CompTemplDynamicDataService,
    },
    {
      provide: DocumentDynamicFieldService,
      useClass: CompTemplDynamicFieldService,
    },
    {
      provide: DocumentDropdownService,
      useClass: CompTemplDropdownService,
    },
    {
      provide: DocumentFieldReadonlyService,
      useClass: CompTemplFieldReadonlyService,
    },
    {
      provide: CellMapperService,
      useClass: CompTemplCellMapperService,
    },
    {
      provide: FieldMapperService,
      useClass: CompTemplFieldMapperService,
    },
    {
      provide: SectionHeaderContentMapperService,
      useClass: CompTemplSectionHeaderContentMapperService,
    },
    {
      provide: DocumentActionService,
      useClass: CompTemplActionService,
    },
    {
      provide: GroupRowActionDispatcherService,
      useClass: GroupRowActionDispatcherService,
    },
    {
      provide: DocumentValueChangeService,
      useClass: CompTemplValueChangeService,
    },
  ],
})
export default class CompTemplComponent extends DocumentComponentStrategy {
  private readonly documentFacadeService = inject(DocumentFacadeService);

  constructor() {
    super();
    this.documentFacadeService.registerBusinessLogic();
  }
}
