<ng-container *ngFor="let attachment of attachmentList; let i = index; trackBy: trackByAttachmentId">
  <div class="attachment-wrapper inline-block position-relative">
    <div class="attachment-thumbnail position-relative pointer" (click)="onClick(attachment)">
      <app-base-image
        class="width-100 height-100"
        *ngIf="doNotNeedFileIcon[attachment.fileExtension] && attachment.url; else fileIcon"
        class="height-100"
        [value]="attachment.url"
      ></app-base-image>

      <ng-template #fileIcon>
        <div *ngIf="!isShowPdfSigned(attachment)" class="file-icon" [attr.file]="attachment.fileExtension"></div>
        <div *ngIf="isShowPdfSigned(attachment)" class="pdf-signed-icon"></div>
      </ng-template>

      <button
        *ngIf="edit"
        class="delete-btn opacity-0 fab-button fab-button-size-20 primary-color position-absolute top-0 right-0"
        (click)="deleteImage(attachment, i, $event)"
      >
        <app-icon class="icon-size-18">close</app-icon>
      </button>
    </div>

    <div class="attachment-name">{{ attachment.fileName }}</div>

    <div *ngIf="!attachment.fileAddress" class="flex justify-between attachment-metadata">
      <div>{{ attachment.updatedOn | dateTimeFormat : 'date' }}</div>
      <div>{{ attachment.size }}</div>
    </div>
    <div *ngIf="attachment.signStatus" class="attachment-status" (click)="openSignatureInfo(attachment)">
      {{ attachment.signStatus }}
    </div>

    <app-upload-task
      *ngIf="attachment.needUpload"
      class="position-absolute full-size-offset width-100"
      type="attachment"
      [file]="attachment.fileToUpload"
      (uploadSucceed)="handleUploadSucceed(attachment, $event, i)"
      (uploadFailed)="handleUploadFailed(attachment, i)"
    >
    </app-upload-task>
  </div>
</ng-container>
