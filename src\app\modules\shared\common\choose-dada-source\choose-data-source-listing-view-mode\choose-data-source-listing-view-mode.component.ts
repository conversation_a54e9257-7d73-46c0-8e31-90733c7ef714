import { Dialog } from '@angular/cdk/dialog';
import { CdkMenuModule } from '@angular/cdk/menu';
import { NgIf, AsyncPipe, CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  OnInit,
  AfterViewInit,
  OnDestroy,
  Component,
  inject,
  ComponentRef,
  ViewChild,
  ViewContainerRef,
  Input,
  Output,
  EventEmitter,
  QueryList,
  ViewChildren,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

import { TranslocoPipe, TranslocoService } from '@ngneat/transloco';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import {
  ColDef,
  GridApi,
  GridOptions,
  GridReadyEvent,
  RowDataUpdatedEvent,
  SelectionChangedEvent,
  ValueFormatterParams,
  ValueGetterParams,
} from 'ag-grid-community';
import { AngularSplitModule } from 'angular-split';
import {
  BehaviorSubject,
  map,
  tap,
  combineLatest,
  filter,
  switchMap,
  distinctUntilChanged,
  shareReplay,
  of,
  Subject,
  Observable,
  catchError,
  startWith,
  debounceTime,
  withLatestFrom,
  auditTime,
  take,
  mergeMap,
  ReplaySubject,
} from 'rxjs';

import { SyncVerticalScrollContainerDirective } from '../../../directives/sync-vertical-scroll-container.directive';
import { SyncVerticalScrollItemDirective } from '../../../directives/sync-vertical-scroll-item.directive';
import { CbxDialogHeaderComponent } from '../../cbx-dialog-header/cbx-dialog-header.component';
import { TableSelectionMode } from '../../cbx-table/cbx-table';
import { CbxTableCellMapperService } from '../../cbx-table/cbx-table-cell-mapper.service';
import { CbxTableComponent } from '../../cbx-table/cbx-table.component';
import { FloatingFilterComponent } from '../../cbx-table/floating-filter/floating-filter.component';
import { TimeSelectionComponent } from '../../time-selection/time-selection.component';
import { IconComponent } from 'src/app/component/icon/icon.component';
import { DETAIL_ROW_HEIGHT, DEFAULT_ROW_HEIGHT } from 'src/app/config/constant';
import { defaultDialogConfig } from 'src/app/config/dialog';
import { columnMinWidth, getViewFieldWidth, viewColumnSortingIconWidth } from 'src/app/config/width-code-map';
import { TooltipDirective } from 'src/app/directives/tooltip.directive';
import { DocumentViewData, SearchCondition, SearchCriteria } from 'src/app/entities';
import { MenuItem, FieldDefine } from 'src/app/entities/form-field';
import { FormFieldTypes } from 'src/app/entities/form-field-types';
import {
  ViewSorting,
  ColumnTypeMap,
  FilterType,
  Record,
  ViewColumn,
  NaviModule,
  NaviTab,
  View,
  ColumnEditDialogResult,
  ColumnEditDialogData,
  TableSelectDialogDataSource,
  NaviView,
} from 'src/app/interface/model';
import { ColumnEditDialogComponent } from 'src/app/modules/container/listing/column-edit-dialog/column-edit-dialog.component';
import { EditColumnsComponent } from 'src/app/modules/container/listing/edit-columns/edit-columns.component';
import { FieldBackgroundColorfulService } from 'src/app/modules/services/field-background-colorful.service';
import { ListingService } from 'src/app/modules/services/listing.service';
import { CodelistService } from 'src/app/services/codelist.service';
import { ConfigurationService } from 'src/app/services/configuration.service';
import { MessageDialogService } from 'src/app/services/message-dialog.service';
import { NavigationService } from 'src/app/services/navigation.service';
import { RecordService } from 'src/app/services/record.service';
import { SearchFilter, CONDITION_SYMBOL } from 'src/app/services/search/search.service';
import {
  compatibleUrl,
  isNotNil,
  getValueFormatter,
  deepEqual,
  isNotEmptyOrNil,
  isEmptyOrNil,
  deepClone,
} from 'src/app/utils';
import { notMap } from 'src/app/utils/operators';

@UntilDestroy()
@Component({
  selector: 'app-choose-data-source-listing-view-mode',
  templateUrl: './choose-data-source-listing-view-mode.component.html',
  styleUrls: ['./choose-data-source-listing-view-mode.component.scss'],
  providers: [RecordService],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CbxDialogHeaderComponent,
    NgIf,
    EditColumnsComponent,
    CbxTableComponent,
    AsyncPipe,
    TranslocoPipe,
    TimeSelectionComponent,
    CdkMenuModule,
    TooltipDirective,
    CommonModule,
    IconComponent,
    SyncVerticalScrollItemDirective,
    AngularSplitModule,
    SyncVerticalScrollContainerDirective,
  ],
})
export class ChooseDataSourceListingViewModeComponent implements OnInit, AfterViewInit, OnDestroy {
  cbxTableCellComponents = inject(CbxTableCellMapperService).components;

  @ViewChild('footerSpacer', { read: ViewContainerRef }) footerVCR: ViewContainerRef;
  @ViewChildren(CbxTableComponent) tables!: QueryList<CbxTableComponent<Record>>;

  @Input() viewData$ = new BehaviorSubject<DocumentViewData>({} as DocumentViewData);
  @Input() data: TableSelectDialogDataSource;
  @Input() fieldDefine: FieldDefine;

  currentViewId$ = new BehaviorSubject<string>(null);
  viewId: string;

  @Input() set currentViewId(viewId: string) {
    this.viewId = viewId;
    this.currentViewId$.next(viewId);
  }

  @Input() set lastDateRange(dateRange: string) {
    if (dateRange) {
      this.changeTimeRange(dateRange);
    }
  }

  @Input() rowSelection: TableSelectionMode;
  @Input() searchStr: string;
  @Input() defaultTab: NaviTab;
  @Input() defaultModule: NaviModule;
  @Input() defaultView: NaviView;
  @Input() widgetCbxql: any;
  @Input() widgetDateRange: any;
  @Input() widgetFilterMap: any;
  @Output() changeCurrentViewId = new EventEmitter<string>();
  @Output() changeViewColumns = new EventEmitter<any[]>();
  @Output() changeTab = new EventEmitter<NaviTab>();
  @Output() changeModule = new EventEmitter<NaviModule>();
  @Output() changeView = new EventEmitter<NaviView>();
  @Output() changeCbxql = new EventEmitter<string>();
  @Output() changeDateRange = new EventEmitter<string>();
  @Output() changeFilterMap = new EventEmitter<any>();
  gridScrollViewport$ = new ReplaySubject<HTMLElement>(1);
  gridScrollViewport2$ = new ReplaySubject<HTMLElement>(1);
  isFilterColumnDefs$ = new BehaviorSubject<boolean>(false);
  gridOptions: GridOptions = {
    alwaysShowHorizontalScroll: true,
  };

  private readonly checkboxCol: ColDef = {
    cellClass: 'popup-table-cell checkbox-padding-0',
    headerComponent: 'commonCheckboxHeader',
    width: 40,
    pinned: 'left',
    resizable: false,
    lockPosition: true,
    cellRenderer: FormFieldTypes.commonCheckbox,
    suppressSizeToFit: true,
  };

  private readonly thumbnailCol: ColDef = {
    width: 60,
    pinned: 'left',
    resizable: false,
    lockPosition: true,
    valueGetter: (params: ValueGetterParams) => params.data.thumbnailUrl || params.data.thumbnail,
    valueFormatter: (params: ValueFormatterParams) => (params.value ? compatibleUrl(params.value) : ''),
    cellRenderer: 'imageLinkComponent',
    suppressSizeToFit: true,
  };

  private readonly colorCol: ColDef = {
    width: 60,
    pinned: 'left',
    resizable: false,
    lockPosition: true,
    valueFormatter(params: ValueFormatterParams) {
      return params.value ? `#${params.value}` : '';
    },
    cellRenderer: 'colorComponent',
    suppressSizeToFit: true,
  };

  // TODO: type wrong in cbx-table
  rowSelection$ = new BehaviorSubject<TableSelectionMode>(null);
  hasAccessToEditView = false;
  viewName: string;
  description: string;

  cbxql = '';
  showSearchBox = false;
  additionCondition: string;

  isMultiple: boolean;
  isMultiple$ = this.rowSelection$.pipe(
    map((mode) => mode === 'multiple'),
    tap((isMultiple: boolean) => {
      this.isMultiple = isMultiple;
    }),
  );

  viewId$ = new BehaviorSubject<string>('');
  moduleId: string;
  typeId: string;
  codeListBookName: string;

  viewType: string;
  optionsMap: Map<string, string>;
  // naviView$ = this.navigationService.view$;
  naviLoading$ = this.navigationService.actionLoading$;
  // viewCache$ = new ReplaySubject<any>(1);
  // get naviViewOptions$() {
  //   return this.viewCache$.asObservable();
  // }
  bookmarkNaviViewOptions$ = combineLatest([
    this.navigationService.bookmarkNaviTabs$.pipe(filter(isNotNil)),
    this.navigationService.tabId$,
    this.navigationService.moduleId$,
  ]).pipe(
    map(
      ([bookmarkNaviTabs, tabId, moduleId]) =>
        bookmarkNaviTabs.find((tab) => tab.id === tabId)?.menuGroups.find((group) => group.id === moduleId)?.menuItems,
    ),
  );

  // naviView$ = this.naviViewOptions$.pipe(map((list) => list.find((item) => item.id === this.viewName)));
  // naviView$ = combineLatest([this.naviViewOptions$, this.viewId$]).pipe(
  //   map(([list, viewName]) => list.find((item) => item.id === viewName)),
  // );

  naviView({ type, ...params }: { type: string; [prop: string]: any }) {
    this.viewId$.next(params.id);
    this.changeCurrentViewId.emit(params.id);
    return null;
  }

  module$ = new BehaviorSubject<NaviModule>(null);

  viewOptions$: Observable<NaviView[]> = this.module$.pipe(
    map((group: NaviModule) => group?.menuItems),
    distinctUntilChanged(),
  );

  searchGroup = this.formBuilder.group(new SearchCriteria());

  chooseAllDomain(option: any) {
    const domain = {
      tab: option,
      module: option,
      view: option,
    };
    this.searchGroup.patchValue(domain);
  }

  changeDomain(tab: NaviTab, module: NaviModule, naviView?: NaviView) {
    if (!(tab && module)) {
      this.chooseAllDomain(null);
      return;
    }
    const view = naviView || module?.defaultView || module?.menuItems[0];
    if (!naviView) {
      this.filterMap$.next({});
      this.widgetCbxql = null;
      this.widgetDateRange = 'PST1Y';
      this.changeTimeRange('PST1Y');
      this.widgetFilterMap = null;
      this.filterCondition$.next(null);
      this.changeCbxql.emit(null);
      this.changeFilterMap.emit(null);
      this.searchInput.setValue('');
    }
    this.viewId$.next(view?.id);
    this.viewId = view?.id;

    this.searchGroup.patchValue({ tab, module, view });
    this.changeCurrentViewId.emit(view?.id);
    this.changeTab.emit(tab);
    // this.changeModule.emit(module);
    // this.changeView.emit(view);
  }

  // tabs$ = this.naviViewOptions$.pipe(map((tabs) => tabs?.filter((tab) => tab.menuGroups?.length && !tab.isHidden)));
  tabs: NaviTab[];
  time$ = new BehaviorSubject<string>('PST1Y');

  changeTimeRange(dateRange: string) {
    this.time$.next(dateRange);
    this.timeCondition$.next(`dateRange=${dateRange}`);
    this.changeDateRange.emit(dateRange);
  }

  view$ = this.viewId$.pipe(
    filter((viewId) => !!viewId),
    switchMap((viewId) => {
      if (viewId === 'popCustSectionCodelistView') {
        const urlParams: string = this.data?.additionalSearchCondition.urlParams;
        return this.listingService.getView$(viewId, this.currentEntityName, this.moduleId, this.typeId, urlParams);
      }
      return this.listingService.getView$(viewId, this.currentEntityName, this.moduleId, this.typeId);
    }),
    tap((view) => {
      this.description = view.description;
      this.viewId = view.name;
      this.viewType = view.type;
      const actions = view.actions as MenuItem[];
      this.hasAccessToEditView = false;
      actions.forEach((action) => {
        if (action.id === 'settingView') {
          this.hasAccessToEditView = true;
          action.disabled = true;
          if (!this.viewType || this.viewType === 'PersonalizeView') {
            this.listingService
              .getPersonalizeViewOptionsMap$(this.viewId)
              .pipe(distinctUntilChanged(deepEqual))
              .subscribe((optionsMap) => {
                this.optionsMap = optionsMap;
              });
          }
        }
      });
      this.handleSearchCriterion(view.searchCriterion);
    }),
    untilDestroyed(this),
    shareReplay(1),
  );

  moduleId$ = this.view$.pipe(
    map((view) => view?.module),
    distinctUntilChanged(),
    tap((moduleId) => {
      this.moduleId = moduleId;
    }),
    untilDestroyed(this),
    shareReplay(1),
  );

  option$ = this.view$.pipe(map((view) => view?.option));

  pageSize$ = this.option$.pipe(
    map((option) => option?.pageSize),
    map((size) => `pageSize=${size || 20}`),
  );

  mode$ = this.option$.pipe(
    filter((options) => !!options),
    map((option) => (option.showThumbnailInPopup ? 'detail' : option.defaultMode)),
  );

  hasImageColumn$ = new BehaviorSubject<boolean>(false);

  multiLine$ = combineLatest([this.mode$, this.hasImageColumn$]).pipe(
    map(([mode, hasImageCol]) => mode === 'detail' || hasImageCol),
  );

  rowHeight$ = this.multiLine$.pipe(map((mode) => (mode ? DETAIL_ROW_HEIGHT : DEFAULT_ROW_HEIGHT)));

  oneLine$ = this.multiLine$.pipe(notMap);

  columns$ = this.view$.pipe(
    map((view) => view?.columns),
    distinctUntilChanged(),
    switchMap((columns) => {
      if (this.configurationService.CONFIG.FLAG_APP_FIELD_BACKGROUND_COLORFUL) {
        return combineLatest([this.fieldBackgroundColorfulService.backgroundColorMap$, this.moduleId$]).pipe(
          map(([backgroundColorMap, moduleId]) =>
            columns.map((column) => {
              const columnBackgroundColorMap = backgroundColorMap[moduleId]?.[column.fieldId];
              if (columnBackgroundColorMap) {
                return {
                  ...column,
                  backgroundColorMap: columnBackgroundColorMap,
                };
              }
              return column;
            }),
          ),
          tap((viewColumns) => {
            this.changeViewColumns.emit(viewColumns);
          }),
        );
      }
      this.changeViewColumns.emit(columns);
      return of(columns);
    }),
    untilDestroyed(this),
    shareReplay(1),
  );

  defaultSorting$ = this.view$.pipe(map((view) => view?.sortings));

  sortingCondition$ = new BehaviorSubject<ViewSorting[]>([]);

  sortings$ = combineLatest([this.defaultSorting$, this.sortingCondition$]).pipe<ViewSorting[]>(
    map(([defaultSortings, sortingCondition]) => (sortingCondition.length ? sortingCondition : defaultSortings)),
  );

  filterBus$ = new Subject<{ fieldId: string; filters: SearchFilter[]; filterCodeList?: SearchFilter[] }>();
  searchCondition: SearchCondition = {};
  searchCondition$ = new BehaviorSubject<SearchCondition | null>(null);
  filterCondition$ = new BehaviorSubject<string>('');
  timeCondition$ = new BehaviorSubject<string>('');

  filterMap$ = new BehaviorSubject<{
    [fieldId: string]: SearchFilter[];
  }>({});

  filterMapObs$ = this.searchCondition$.pipe(
    filter((searchCondition) => isNotNil(searchCondition?.cbxql)),
    map(({ cbxql }) => cbxql),
    map((filtersString) => {
      const filterMap: { [fieldId: string]: SearchFilter[] } = {};
      const filters = filtersString.split(CONDITION_SYMBOL.and);
      filters.forEach((filterString) => {
        const [andFilter, ...orFilters] = filterString.split(CONDITION_SYMBOL.globalOr);

        const { fieldId, condition } = andFilter.match(/(?<fieldId>\w+)\s?(?<condition>[~!=(IS)><].*)/).groups;
        if (!filterMap[fieldId]) {
          filterMap[fieldId] = [];
        }
        filterMap[fieldId].push({ condition, joinOperator: CONDITION_SYMBOL.and });

        orFilters.forEach((orFilterString) => {
          const { orFieldId, orCondition } = orFilterString.match(
            /(?<orFieldId>\w+)\s?(?<orCondition>[~!=(IS)><].*)/,
          ).groups;
          if (!filterMap[orFieldId]) {
            filterMap[orFieldId] = [];
          }
          filterMap[orFieldId].push({ condition: orCondition, joinOperator: CONDITION_SYMBOL.globalOr });
        });
      });
      return filterMap;
    }),
  );

  columnDefs$: Observable<ColDef[]> = combineLatest([this.columns$, this.sortings$, this.filterMap$]).pipe(
    map(([columns, sortings = [], filterMap]) => this.buildColumn(columns, sortings, filterMap)),
    untilDestroyed(this),
    shareReplay(1),
  );
  filterColumnDefs$ = combineLatest([this.columns$, this.sortings$, this.filterMap$])
    .pipe(
      map(([columns, sortings = [], filterMap]) => this.buildColumn(deepClone(columns), sortings, filterMap)),
      untilDestroyed(this),
      shareReplay(1),
    )
    .pipe(
      map((displayColumnDefs: any) =>
        displayColumnDefs
          .filter((displayColumnDef) => displayColumnDef.filter)
          .map((displayColumnDef) => ({ ...displayColumnDef, suppressAutoSize: true, pinned: null, hide: false })),
      ),
      tap((displayColumnDefs) => {
        this.isFilterColumnDefs$.next(isNotEmptyOrNil(displayColumnDefs));
      }),
    );
  checkboxColumnDefs$: Observable<ColDef[]> = this.isMultiple$.pipe(map((mode) => (mode ? [this.checkboxCol] : [])));

  thumbnailColumnDef$: Observable<ColDef[]> = this.mode$.pipe(
    map((mode) => (mode === 'detail' && this.data.viewId !== 'popupItemColorView' ? [this.thumbnailCol] : [])),
  );
  displayColumnDefs: ColDef[] = [];
  displayColumnDefs$ = combineLatest([this.checkboxColumnDefs$, this.thumbnailColumnDef$, this.columnDefs$])
    .pipe(
      map(([fixedColumnDefs, thumbnailColumnDef, columnDefs]) => [
        ...fixedColumnDefs,
        ...thumbnailColumnDef,
        ...columnDefs,
      ]),
    )
    .subscribe((result) => {
      this.displayColumnDefs = result;
    });

  // records$ = this.recordService.records$;
  loading$ = this.recordService.loading$;
  totalSize$ = this.recordService.pagination$.pipe(map((pagination) => pagination?.totalSize));

  searchInput = new UntypedFormControl();
  searchString$ = this.searchInput.valueChanges.pipe(
    startWith(''),
    debounceTime(500),
    tap((search) => {
      const selectedRows: Record[] = this.gridApi.getSelectedRows();
      selectedRows.forEach((row) => {
        const newRow = { ...row, module: this.module };
        this.selectedRowsEntities[this.getKey(row)] = newRow;
      });
      this.searchCondition = { ...this.searchCondition, q: search };
    }),
    map((search) => `q=${encodeURIComponent(search)}`),
    distinctUntilChanged(),
  );

  sortingString$ = this.sortings$.pipe(
    map((sortings) =>
      (sortings || [])
        .map((sort) => `${sort.fieldId}:${sort.type}${sort.type === 'desc' ? ':blankLast' : ''}`)
        .join(','),
    ),
    tap((sortings) => {
      this.searchCondition = { ...this.searchCondition, sorting: sortings };
    }),
    map((sorting) => `sorting=${sorting}`),
    distinctUntilChanged(),
  );

  additionCondition$ = new BehaviorSubject<string>('');

  queryString$ = combineLatest([
    this.searchString$,
    this.sortingString$,
    this.filterCondition$,
    this.additionCondition$,
    this.pageSize$,
    this.timeCondition$,
  ]).pipe(
    tap(() => this.recordService.resetRecordState()),
    map((conditions) => conditions.join('&')),
  );

  loadMore$ = new Subject<boolean>();

  module: string;
  fetchRecords$ = combineLatest([this.loadMore$, this.view$, this.viewData$, this.queryString$]).pipe(
    map(([, view, viewData, query]) => ({ view, viewData, query })),
    tap(({ view }) => {
      this.module = view.module;
    }),
    untilDestroyed(this),
  );

  dataKeys: string[];
  getKey: (data: any) => string;
  selectedRowsEntities: { [key: string]: Record } = {};
  required: boolean;
  selectionLength = 0;
  gridApi: GridApi;
  alwaysEnable: boolean;
  widgetType: string;
  footerComponentRef: ComponentRef<any>;
  currentEntityName: string;

  records$ = combineLatest([this.recordService.records$, this.viewData$]).pipe(
    map(([records, viewData]) => {
      const details = viewData?.testAccreditationRequirementsList;
      if (details?.length && records?.length) {
        const filterId = details.map((detail) => detail?.testMethod?.id);
        return records.filter((record) => !filterId.includes(record.id));
      }
      return records;
    }),
  );

  constructor(
    private readonly listingService: ListingService,
    private readonly recordService: RecordService,
    private readonly codelistService: CodelistService,
    private readonly translocoService: TranslocoService,
    private readonly configurationService: ConfigurationService,
    private readonly fieldBackgroundColorfulService: FieldBackgroundColorfulService,
    readonly navigationService: NavigationService,
    protected readonly messageDialogService: MessageDialogService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    protected readonly dialog: Dialog,
    private readonly formBuilder: UntypedFormBuilder,
  ) {
    // this.navigationService.fetchAllView$().subscribe((data) => {
    //   this.viewCache$.next(data);
    // });
    this.tabs = this.navigationService.state?.naviTab;
  }

  ngOnInit() {
    // this.changeCbxql.emit(null);
    // this.changeDateRange.emit(null);
    this.filterColumnDefs$.subscribe();
    this.filterMapObs$.subscribe(this.filterMap$);

    const currentEntityName = this.fieldDefine?.popupMeta?.baseCriteria?.find(
      (bc) => bc.key === 'currentEntityName',
    )?.value;
    this.currentEntityName = currentEntityName;

    this.rowSelection$.next(this.rowSelection);

    this.additionCondition$.next(this.additionCondition);

    this.searchGroup
      .get('module')
      .valueChanges.pipe(untilDestroyed(this))
      .subscribe({
        next: (module) => {
          this.module$.next(module);
          this.changeModule.emit(module);
        },
      });

    this.searchGroup
      .get('view')
      .valueChanges.pipe(untilDestroyed(this))
      .subscribe({
        next: (view) => {
          if (view?.id !== this.defaultView?.id) {
            this.filterMap$.next({});
            this.widgetCbxql = null;
            this.widgetDateRange = 'PST1Y';
            this.changeTimeRange('PST1Y');
            this.widgetFilterMap = null;
            this.filterCondition$.next(null);
            this.changeCbxql.emit(null);
            this.changeFilterMap.emit(null);
          }
          this.changeCurrentViewId.emit(view?.id);
          this.changeView.emit(view);
          this.viewId$.next(view.id);
        },
      });

    this.selectDefaultTab();

    // auto select tab & module by view id
    this.currentViewId$.subscribe(() => {
      if ((isEmptyOrNil(this.defaultTab) || isEmptyOrNil(this.defaultModule)) && this.viewId) {
        const { tabs } = this;
        if (isNotEmptyOrNil(tabs)) {
          let isMatchedTabFound = false;
          tabs.forEach((tab) => {
            if (!isMatchedTabFound) {
              tab?.menuGroups?.forEach((menuGroup) => {
                if (!isMatchedTabFound) {
                  menuGroup?.menuItems?.forEach((menuItem) => {
                    if (menuItem?.id === this.viewId) {
                      this.defaultTab = tab;
                      this.defaultModule = menuGroup;
                      this.defaultView = menuItem;
                      this.changeTab.emit(tab);
                      this.changeModule.emit(menuGroup);
                      isMatchedTabFound = true;
                    }
                  });
                }
              });
            }
          });
        }
        this.selectDefaultTab();
      }
    });

    this.handleFetchRecords();
    if (this.widgetCbxql && this.widgetFilterMap) {
      this.filterMap$.next(this.widgetFilterMap);
      const cbxql = this.widgetCbxql.code ? this.widgetCbxql.code : this.widgetCbxql;
      this.filterCondition$.next(`cbxql=${cbxql}`);
      this.changeCbxql.emit(cbxql);
      this.changeFilterMap.emit(this.widgetFilterMap);
    } else {
      this.viewId$.next(this.viewId);
    }
    if (isNotEmptyOrNil(this.widgetDateRange)) {
      this.changeTimeRange(this.widgetDateRange.code ? this.widgetDateRange.code : this.widgetDateRange);
    } else {
      this.changeTimeRange('PST1Y');
    }
  }

  selectDefaultTab() {
    if (isNotEmptyOrNil(this.defaultTab) && isNotEmptyOrNil(this.defaultModule)) {
      this.changeDomain(this.defaultTab, this.defaultModule, this.defaultView);
    }
  }

  ngAfterViewInit() {
    if (this.footerComponentRef) {
      this.footerVCR?.insert(this.footerComponentRef.hostView);
    }
  }

  ngOnDestroy() {
    this.footerVCR?.clear();
  }

  handleFetchRecords() {
    this.handleFilter();
    this.fetchRecords$.subscribe({
      next: ({ view, viewData, query }) => {
        const { module, name } = view;
        this.recordService.fetchRecord(module, name, query, viewData);
      },
      error: (error) => {
        console.error(error);
        // this.dialogRef.close();
      },
    });
    this.searchInput.setValue(this.searchStr);
  }

  private handleFilter() {
    this.filterBus$
      .pipe(withLatestFrom(this.filterMap$), auditTime(0), untilDestroyed(this))
      .subscribe(([{ fieldId, filters }, filterMap]) => {
        const result = { ...filterMap, [fieldId]: filters };
        if (filters.length === 0) {
          delete result[fieldId];
        }
        this.cbxql = '';
        this.filterMap$.next(result);
        const str = 'cbxql=';
        Object.entries(result).forEach(([resultId, resultFilters]) => {
          resultFilters.forEach(({ condition, joinOperator }) => {
            if (condition.includes('IS BLANK') || condition.includes('IS NOT BLANK')) {
              if (this.cbxql.includes(resultId)) {
                this.cbxql = this.handleSameFilter(resultId, condition);
              } else {
                this.cbxql += `${this.cbxql ? joinOperator ?? CONDITION_SYMBOL.and : ''}${resultId} ${condition}`;
              }
            } else {
              this.cbxql += `${this.cbxql ? joinOperator ?? CONDITION_SYMBOL.and : ''}${resultId}${condition}`;
            }
            this.showSearchBox = true;
          });
        });

        if (this.cbxql.includes('&')) {
          this.cbxql = this.cbxql.replace(/&/g, '0#_#0');
        }

        this.searchCondition = { ...this.searchCondition, cbxql: this.cbxql };

        this.filterCondition$.next(str + this.cbxql);
        this.changeCbxql.emit(this.cbxql);
        this.changeFilterMap.emit(result);
      });
  }

  private handleSearchCriterion(searchCriterion: string) {
    if (!searchCriterion) {
      return;
    }
    const conditions = searchCriterion.substring(1, searchCriterion.length - 1).split(' AND ');
    conditions.forEach((condition) => {
      if (condition.indexOf('cbxql') > 0) {
        this.cbxql = condition.replace("(cbxql LIKE|STRING '", '').replace("')", '');
        this.searchCondition = { ...this.searchCondition, cbxql: this.cbxql };
        this.filterCondition$.next(`cbxql=${this.cbxql}`);
        this.changeCbxql.emit(this.cbxql);
      } else if (condition.indexOf('q') > 0) {
        const q = condition.replace("(q LIKE|STRING '", '').replace("')", '');
        this.searchInput.setValue(q);
        this.searchCondition = { ...this.searchCondition, q };
      }
      this.searchCondition$.next(this.searchCondition);
    });
  }

  handleSameFilter(resultId: string, condition: string): string {
    let reBuildCbxql = '';
    let reBuildArray: string[] = [];
    const splitCondition = this.cbxql.split(CONDITION_SYMBOL.and);
    reBuildArray = splitCondition.filter((item) => !item.includes(resultId));
    reBuildArray.forEach((value, index) => {
      if (index === 0) {
        reBuildCbxql += value;
      } else {
        reBuildCbxql += CONDITION_SYMBOL.and + value;
      }
    });

    if (condition.includes('IS BLANK') || condition.includes('IS NOT BLANK')) {
      if (reBuildCbxql === '') {
        return `${reBuildCbxql}${resultId} ${condition}`;
      }
      return `${reBuildCbxql}${CONDITION_SYMBOL.and}${resultId} ${condition}`;
    }
    if (reBuildCbxql === '') {
      return `${reBuildCbxql}${resultId}${condition}`;
    }
    return `${reBuildCbxql}${CONDITION_SYMBOL.and}${resultId}${condition}`;
  }

  loadMore() {
    this.loadMore$.next(true);
  }

  gridReady(ready: GridReadyEvent) {
    this.gridApi = ready.api;
    this.loadMore();
  }

  checkSelectedRow(rowDataUpdated: RowDataUpdatedEvent) {
    const originalSelectedLength = Object.keys(this.selectedRowsEntities).length;
    if (originalSelectedLength && this.dataKeys.length) {
      rowDataUpdated.api.forEachNode((rowNode) => {
        const key = this.getKey(rowNode.data);

        if (this.selectedRowsEntities[key]) {
          delete this.selectedRowsEntities[key];
          rowNode.setSelected(true);
        }
      });
    }
  }

  selectionChanged(selectionChanged: SelectionChangedEvent) {
    const originalSelectedLength = Object.keys(this.selectedRowsEntities).length;
    const currentSelectedLength = selectionChanged.api.getSelectedRows().length;
    this.selectionLength = currentSelectedLength + originalSelectedLength;
  }

  columnSort(sortingResult: ViewSorting[]) {
    const sortings = sortingResult.map((sort) => ({ fieldId: sort.fieldId, type: sort.type } as ViewSorting));

    this.sortingCondition$.next(sortings);
  }

  columnChange(columnDefs: ColDef[]) {
    const viewColumns = columnDefs.map(
      (column) =>
        ({
          ...column.cellRendererParams.col,
          size: String(column.width),
          frozenType: column.pinned,
          visible: !column.hide,
        } as ViewColumn),
    );

    const newView$ = this.listingService.setColumns(this.viewName, viewColumns);

    this.handleUpdateColumns(newView$);
  }

  private handleColumns(columnDefs: ColDef[]): ColDef[] {
    const indexedColumnDefs = columnDefs.reduce((acc, cur) => {
      acc[cur.field] = cur;
      return acc;
    }, {});
    const colDefs: ColDef[] = this.tables.last.agGrid.api
      .getColumnState()
      .map((column) => {
        const col = indexedColumnDefs[column.colId.replace(/_\d?$/, '')];
        if (!col || col.lockPosition) {
          return null;
        }
        return { ...col, pinned: column.pinned, width: column.width };
      })
      .filter((columnDef) => !!columnDef);

    return colDefs;
  }

  openEditColumnDialog() {
    const newView$ = combineLatest([this.viewId$, this.columnDefs$]).pipe(
      take(1),
      mergeMap(([viewName, columnDefs]) => {
        const dialogRef = this.dialog.open<ColumnEditDialogResult, ColumnEditDialogData, ColumnEditDialogComponent>(
          ColumnEditDialogComponent,
          {
            ...defaultDialogConfig,
            maxWidth: '900px',
            width: '850px',
            data: {
              viewName,
              columnDefs: this.handleColumns(columnDefs),
            },
          },
        );

        return dialogRef.closed;
      }),
      filter((result) => result?.type === 'done'),
      map((result) => result.payload.view),
    );

    this.handleUpdateColumns(newView$);
  }

  private handleUpdateColumns(newView$: Observable<View>) {
    newView$
      .pipe(
        switchMap((newView) => combineLatest([of(newView), this.view$]).pipe(take(1))),
        map(([newView, view]) => ({ ...view, columns: newView.columns, option: newView.option })),
        untilDestroyed(this),
      )
      .subscribe((view: View) => this.viewId$.next(view.name));
  }

  dumbChangeDetect() {}

  get tabControl(): UntypedFormControl {
    return this.searchGroup.get('tab') as UntypedFormControl;
  }

  get moduleControl(): UntypedFormControl {
    return this.searchGroup.get('module') as UntypedFormControl;
  }

  get viewControl(): UntypedFormControl {
    return this.searchGroup.get('view') as UntypedFormControl;
  }
  filterColumnGridReady() {}
  private buildColumn(columns: ViewColumn[], sortings: any[], filterMap: unknown) {
    const sortingCols = sortings.reduce((acc, sorting) => {
      acc[sorting.fieldId] = sorting.type;
      return acc;
    }, {});

    const cellRendererMapByType: ColumnTypeMap<string> = {
      Date: 'dateComponent',
      Datetime: 'dateComponent',
      ColorImage: 'colorImageComponent',
    };

    const canFilterMap: ColumnTypeMap<boolean> = {
      RefNo: true,
      Text: true,
      Label: true,
      Date: true,
      Datetime: true,
      Hyperlink: true,
      HyperlinkOrNew: true,
      Number: true,
      Decimal: true,
      Codelist: true,
      checkbox: true,
      CostSheetNo: true,
      NotificationSubjectLink: true,
    };

    const filterTypeMap: ColumnTypeMap<FilterType> = {
      Date: 'date',
      Datetime: 'date',
      Number: 'number',
      Decimal: 'number',
    };

    return columns.map((col) => {
      if (col.type === 'Thumbnail') {
        this.hasImageColumn$.next(true);
        return {
          headerName: col.label,
          pinned: col.frozenType ?? null,
          field: col.fieldId,
          hide: !col.visible,
          resizable: true,
          headerComponentParams: {
            sortable: col.sortable,
          },
          sort: sortingCols[col.fieldId] ?? null,
          comparator: () => 0,
          minWidth: columnMinWidth,
          width: 60,
          valueFormatter: (params: ValueFormatterParams) => (params.value ? compatibleUrl(params.value) : ''),
          cellRenderer: 'imageLinkComponent',
          suppressSizeToFit: true,
        };
      }
      if (col.type === 'ColorChip') {
        return {
          ...this.colorCol,
          headerName: col.label,
          valueGetter: (params: ValueGetterParams) => params.data[col.fieldId],
        };
      }

      const width = getViewFieldWidth(col.type, col.size);

      const valueFormatter = getValueFormatter(
        col?.dataFormat,
        this.translocoService.translate.bind(this.translocoService),
      );

      const hasCodelist =
        (col.dataFormat && col.visible && col.dataFormat?.includes('bookName')) ||
        col.fieldId === 'productCategory' ||
        col.dataFormat?.includes('labelIdPrefix') ||
        col.dataFormat?.includes('labelPrefix');
      let moduleId = '';
      let viewName = '';
      this.view$.pipe(untilDestroyed(this)).subscribe((view) => {
        const { module, name } = view;
        moduleId = module;
        viewName = name;
      });

      const codelistOptions$ = hasCodelist
        ? this.codelistService
            .getCodelistOptions(moduleId, viewName, col.fieldId, this.codeListBookName, this.typeId)
            .pipe(
              map((result) => result[col.fieldId] ?? []),
              catchError(() => of([])),
              untilDestroyed(this),
              shareReplay(1),
            )
        : of([]);

      return {
        cellClass: 'popup-table-cell',
        headerName: col.label,
        pinned: col.frozenType ?? null,
        field: col.fieldId,
        hide: !col.visible,
        width: width + (col.sortable ? viewColumnSortingIconWidth : 0),
        minWidth: width * 0.5 + (col.sortable ? viewColumnSortingIconWidth : 0),
        maxWidth: width * 2 + (col.sortable ? viewColumnSortingIconWidth : 0),
        resizable: true,
        headerComponentParams: {
          filterConditions$: of(filterMap[col.fieldId]),
          filter: canFilterMap[col.type],
          filterType: filterTypeMap[col.type],
          applyFilter: (filters: SearchFilter[], filterCodeList: SearchFilter[]) =>
            this.filterBus$.next({ fieldId: col.fieldId, filters, filterCodeList }),
          codelistOptions$,
          hasCodelist,
          sortable: col.sortable,
          popHost: true,
        },
        filter: !!filterMap[col.fieldId],
        floatingFilter: !!filterMap[col.fieldId],
        floatingFilterComponent: FloatingFilterComponent,
        floatingFilterComponentParams: {
          filterConditions: filterMap[col.fieldId],
          suppressFilterButton: true,
        },
        sort: sortingCols[col.fieldId] ?? null,
        comparator: () => 0,
        cellRenderer: cellRendererMapByType[col.type] ?? 'textComponent',
        cellRendererParams: {
          col,
        },
        valueFormatter,
      } as ColDef;
    });
  }
}
