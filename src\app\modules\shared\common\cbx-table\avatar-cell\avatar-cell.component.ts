import { NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, HostBinding } from '@angular/core';

import { Until<PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';
import { ICellRendererParams } from 'ag-grid-community';
import { LazyLoadImageModule } from 'ng-lazyload-image';
import { AbbreviationPipe } from 'projects/chat/src/lib/pipe/abbreviation.pipe';

import { AngularCellComponent, CellRendererAngularComp } from '../cell-renderer-params';

@UntilDestroy()
@Component({
  selector: 'app-avatar-cell',
  templateUrl: './avatar-cell.component.html',
  styleUrls: ['./avatar-cell.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [NgIf, LazyLoadImageModule, AbbreviationPipe],
})
export class AvatarCellComponent extends AngularCellComponent implements CellRendererAngularComp {
  @HostBinding('class') hostClass = 'flex height-100 position-relative';

  thumbnailUrl: string;
  username: string;

  agInit(params: ICellRendererParams): void {
    const { thumbnailUrl, userName } = params.data;

    this.thumbnailUrl = thumbnailUrl;
    this.username = userName;
  }

  refresh(): boolean {
    return false;
  }
}
