import { Async<PERSON>ipe, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, HostBinding, Input, Output } from '@angular/core';

import { PDFDocumentProxy, PdfViewerModule } from 'ng2-pdf-viewer';
import { BehaviorSubject } from 'rxjs';

@Component({
  selector: 'app-base-pdf',
  templateUrl: './base-pdf.component.html',
  styleUrls: ['./base-pdf.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [PdfViewerModule, NgIf, AsyncPipe],
})
export class BasePdfComponent {
  @HostBinding('class') baseImageClass = 'block margin-auto position-relative';

  @Input() value: any;
  @Input() page: number;
  @Input() zoom: number;
  @Input() originalSize: boolean;
  @Input() showAll: boolean;
  @Input() zoomScale: 'page-height' | 'page-fit' | 'page-width';
  @Input() showBorders: boolean;
  @Input() renderText: boolean;
  @Input() stickToPage: boolean;

  @Output() pdfNumPages = new EventEmitter<number>();

  loading$ = new BehaviorSubject<boolean>(true);

  // testSrc = 'https://vadimdez.github.io/ng2-pdf-viewer/assets/pdf-test.pdf';

  constructor() {
    /**
     * ng2-pdf-viewer version > 10.2.2 will with pdfjs-dist v4,
     * which will change to use "pdf.worker.min.mjs" instead of "pdf.worker.min.js"
     * so that need to update the MIME type setting (e.g., in Nginx).
     */

    (window as any).pdfWorkerSrc = '/assets/js/pdf.worker.min.js';
  }

  afterLoadComplete(pdf: PDFDocumentProxy) {
    this.pdfNumPages.emit(pdf.numPages);
  }
}
