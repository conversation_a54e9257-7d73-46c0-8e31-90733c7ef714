import { Dialog } from '@angular/cdk/dialog';
import { ChangeDetectionStrategy, Component, ElementRef } from '@angular/core';

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { filter, switchMap } from 'rxjs/operators';

import { CellRendererParams } from '../../../../../grid-section-content/grid-section-grid/cell/cell-component';
import { LineSheetSourcingStatus } from '../../../model/line-sheet-row.model';
import { CBX_URL } from 'src/app/config/constant';
import { defaultDialogConfig } from 'src/app/config/dialog';
import { QuoteCompareDialogData } from 'src/app/interface/model';
import { DocumentGridCellValueChange } from 'src/app/interface/model/web-socket-frame';
import { ComparisonService } from 'src/app/modules/container/comparison/service/comparison.service';
import { DocumentDataQuery, DocumentDataService } from 'src/app/modules/container/document/state';
import { CompareQuotationsDialogComponent } from 'src/app/modules/container/listing/compare-quotations-dialog/compare-quotations-dialog.component';
import {
  AngularCellComponent,
  CellRendererAngularComp,
} from 'src/app/modules/shared/common/cbx-table/cell-renderer-params';
import { ApiService } from 'src/app/services/api.service';

@UntilDestroy()
@Component({
  selector: 'app-line-sheet-sourcing-status-cell-view',
  templateUrl: './line-sheet-sourcing-status-cell-view.component.html',
  styleUrls: ['./line-sheet-sourcing-status-cell-view.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
})
export class LineSheetSourcingStatusCellViewComponent extends AngularCellComponent implements CellRendererAngularComp {
  params: CellRendererParams;
  value: LineSheetSourcingStatus;
  receivedList: string[];
  displayLabel: string;
  moduleId: string;
  fieldId: string;
  refNo: string;
  itemRef: string;

  moduleMap = {
    quotation: 'vq',
  };

  openDialogMap = {
    quotation: this.openQuoteCompareDialog.bind(this),
    sample: this.openSampleEvaluation.bind(this),
    specification: this.openSpecification.bind(this),
  };

  constructor(
    host: ElementRef,
    private readonly dialog: Dialog,
    private readonly documentDataQuery: DocumentDataQuery,
    private readonly documentDataService: DocumentDataService,
    private readonly apiService: ApiService,
    private readonly comparisonService: ComparisonService,
  ) {
    super(host);
  }

  agInit(params: CellRendererParams) {
    const { value, field, data } = params;

    this.params = params;
    this.value = value;
    this.moduleId = this.moduleMap[field.id];
    this.fieldId = field.id;
    this.itemRef = data?.itemRef;

    const { received, requested, receivedList } = value ?? {};
    this.displayLabel = received || requested ? `${received ?? 0} / ${requested ?? 0}` : '';
    this.receivedList = receivedList;

    this.refNo = this.documentDataQuery.getValue().refNo;
  }

  refresh(): boolean {
    return false;
  }

  openDialog(fieldId: string, itemRef: string) {
    return this.openDialogMap[fieldId] && this.openDialogMap[fieldId](itemRef);
  }

  openQuoteCompareDialog() {
    const dialogRef = this.dialog.open<any, QuoteCompareDialogData, CompareQuotationsDialogComponent>(
      CompareQuotationsDialogComponent,
      {
        ...defaultDialogConfig,
        maxWidth: '98vw',
        height: '100%',
        width: '100%',
        data: {
          moduleId: this.moduleId,
          refNos: this.receivedList,
        },
      },
    );

    dialogRef.closed
      .pipe(
        filter(() => this.comparisonService.statusChanged$.getValue()),
        switchMap(() => this.apiService.get(CBX_URL.lineSheetItemAcceptQuote(this.itemRef))),
        untilDestroyed(this),
      )
      .subscribe((acceptedQuotes) => {
        const changes: DocumentGridCellValueChange[] = [];
        this.comparisonService.statusChanged$.next(false);
        const lineSheetRowDataList = this.documentDataQuery.getValue().lineSheetRowDataList.map((data, rowIndex) => {
          if (data.itemRef === this.itemRef) {
            changes.push({
              sectionId: 'lineSheetRowDataList',
              rowId: data.id,
              rowIndex,
              path: 'acceptedQuotes',
              content: acceptedQuotes,
              fieldId: 'acceptedQuotes',
            });
            return { ...data, acceptedQuotes };
          }
          return data;
        });
        this.documentDataService.updateData({ lineSheetRowDataList });
        this.documentDataService.dynamicRowCellChanges$.next({ changes });
      });
  }

  openSampleEvaluation(itemRef: string) {
    const hostName = window.location.origin;
    const a = document.createElement('a');
    a.href = `${hostName}/listing/quality/sampleEvaluation/sampleEvaluationActiveView?cbxql=item~${itemRef}`;
    a.target = '_blank';
    a.click();
    a.remove();
  }

  openSpecification(itemRef: string) {
    const hostName = window.location.origin;
    const a = document.createElement('a');
    a.href = `${hostName}/listing/sourcing/rfs/rfsAllView?cbxql=rfsItemNo~${itemRef}`;
    a.target = '_blank';
    a.click();
    a.remove();
  }
}
