import { Dialog } from '@angular/cdk/dialog';
import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { untilDestroyed } from '@ngneat/until-destroy';
import { filter, map, of, take } from 'rxjs';

import { DocumentActionService } from '../../../service/document-action.service';
import { DocumentDataState, DocumentDefineService, DocumentDefineStore } from '../../../state';
import { DocumentDataActionService } from '../../../state/document-data-action.service';
import { DocumentDataService } from '../../../state/document-data.service';
import { PasswordSettingDialogComponent } from '../dialog/password-setting-dialog/password-setting-dialog.component';
import { TwoFactorAuthenticationResetDialogComponent } from '../dialog/two-factor-authentication-reset-dialog/two-factor-authentication-reset-dialog.component';
import { CBX_URL } from 'src/app/config/constant';
import { defaultDialogConfig } from 'src/app/config/dialog';
import { TokenLog } from 'src/app/entities';
import { AnyObject, ValidationError } from 'src/app/interface/model';
import { UserPreferenceService } from 'src/app/modules/container/user-preference/service/user-preference.service';
import { DocumentService } from 'src/app/modules/services/document.service';
import { ApiService } from 'src/app/services/api.service';
import { AuthService } from 'src/app/services/auth.service';
import { MessageDialogService } from 'src/app/services/message-dialog.service';
import { NotificationService } from 'src/app/services/notification.service';
import { UserService } from 'src/app/services/user.service';
import { isEmptyOrNil } from 'src/app/utils';

@Injectable({
  providedIn: 'root',
})
export class UserActionService extends DocumentActionService {
  sectionFieldButtonLogic = {
    ...this.sectionFieldButtonLogic,
    OpenPasswordSettingDialog: this.handleOpenPasswordSettingDialog.bind(this),
    OpenResetTwoFAConfirmDialog: this.handleOpenResetTwoFAConfirmDialog.bind(this),
    OpenDisableTwoFAConfirmDialog: this.handleOpenDisableTwoFAConfirmDialog.bind(this),
    AddClientSecret: this.addClientSecret.bind(this),
    RemoveClientSecret: this.removeClientSecret.bind(this),
    CopyClientSecret: this.copyClientSecret.bind(this),
  };
  canOpenInViewMode = {
    ...this.canOpenInViewMode,
    OpenPasswordSettingDialog: true,
    OpenResetTwoFAConfirmDialog: true,
    OpenDisableTwoFAConfirmDialog: true,
  };

  constructor(
    readonly documentDataService: DocumentDataService,
    readonly dialog: Dialog,
    readonly apiService: ApiService,
    readonly documentService: DocumentService,
    readonly messageDialogService: MessageDialogService,
    readonly notificationService: NotificationService,
    readonly authService: AuthService,
    readonly userService: UserService,
    readonly documentDataActionService: DocumentDataActionService,
    readonly documentDefineStore: DocumentDefineStore,
    readonly documentDefineService: DocumentDefineService,
    readonly userPreferenceService: UserPreferenceService,
  ) {
    super(documentDataService, dialog, apiService, documentService);
  }

  handleOpenPasswordSettingDialog(params?: AnyObject<any>) {
    const { documentData } = params;
    const { loginId, id, firstName, lastName } = documentData;
    let isNewDoc = false;
    if (isEmptyOrNil(id)) {
      isNewDoc = true;
    }
    this.userPreferenceService.fetchUserPreference().subscribe((userPreference) => {
      const { isAdminUser } = userPreference;
      const isNotAdminUserItSelf = loginId !== userPreference.loginId;
      this.dialog
        .open<any, any, PasswordSettingDialogComponent>(PasswordSettingDialogComponent, {
          ...defaultDialogConfig,
          width: '480px',
          data: {
            loginId,
            isNewDoc,
            isAdminUser: isAdminUser && isNotAdminUserItSelf,
            firstName,
            lastName,
          },
        })
        .closed.pipe(
          take(1),
          filter((result) => result?.type === 'done'),
          map((result) => result.payload),
        )
        .subscribe((updatedUserPreference) => {
          if (updatedUserPreference.isNewDoc) {
            this.documentDataService.updateData({
              password: updatedUserPreference.newPassword,
              confirmPassword: updatedUserPreference.newPassword,
            });
          } else {
            this.apiService.patch<object>(`${CBX_URL.updateUserPassword}`, updatedUserPreference).subscribe({
              next: () => {
                this.messageDialogService.openOkayDialog('The password has been reset.', 'information');
              },
              error: (error) => {
                this.handleError(error);
              },
            });
          }
        });
    });
  }

  handleOpenResetTwoFAConfirmDialog(params?: AnyObject<any>) {
    const { documentData } = params;
    const { loginId } = documentData;
    const message = 'Are you sure to reset two-factor authentication?';
    this.dialog
      .open<any, any, TwoFactorAuthenticationResetDialogComponent>(TwoFactorAuthenticationResetDialogComponent, {
        ...defaultDialogConfig,
        width: '450px',
        height: '200px',
        data: {
          loginId,
          message,
        },
      })
      .closed.pipe(
        take(1),
        filter((result) => result?.type === 'done'),
        map((result) => result.payload),
      )
      .subscribe(({ userLoginId }) => {
        const resetParams = new HttpParams().set('userLoginId', userLoginId).set('status', 'reset');
        this.apiService.post<TokenLog>(CBX_URL.restTwoFactorAuth, null, { params: resetParams }).subscribe({
          next: () => {
            this.messageDialogService
              .openDialog('Okay', 'The Two-Factor Authentication has been reset.', 'information')
              .closed.pipe(take(1), untilDestroyed(this))
              .subscribe(() => {
                this.documentDataService.updateData({
                  twoFactorAuth: null,
                  twoFactorAuthKey: null,
                  twoFactorAuthStatus: { code: 'REQ', name: 'Required' },
                });
                if (this.authService.state.userInfo.userLoginId === userLoginId) {
                  this.authService.logout();
                }
              });
          },
          error: (error) => {
            this.handleError(error);
          },
        });
      });
  }

  handleOpenDisableTwoFAConfirmDialog(params?: AnyObject<any>) {
    const { documentData } = params;
    const { loginId } = documentData;
    const message = 'Are you sure to disable two-factor authentication?';
    this.dialog
      .open<any, any, TwoFactorAuthenticationResetDialogComponent>(TwoFactorAuthenticationResetDialogComponent, {
        ...defaultDialogConfig,
        width: '450px',
        height: '200px',
        data: {
          loginId,
          message,
        },
      })
      .closed.pipe(
        take(1),
        filter((result) => result?.type === 'done'),
        map((result) => result.payload),
      )
      .subscribe(({ userLoginId }) => {
        const resetParams = new HttpParams().set('userLoginId', userLoginId).set('status', 'disabled');
        this.apiService.post<TokenLog>(CBX_URL.restTwoFactorAuth, null, { params: resetParams }).subscribe({
          next: () => {
            this.documentDataService.updateData({
              twoFactorAuth: null,
              twoFactorAuthKey: null,
            });
            if (this.authService.state.userInfo.userLoginId === userLoginId) {
              this.authService.updateState({
                ...this.authService.state,
                userInfo: { ...this.authService.state.userInfo, mfaEnabled: false },
              });
            }
            this.messageDialogService.openOkayDialog('The Two-Factor Authentication has been disabled.', 'information');
          },
          error: (error) => {
            this.handleError(error);
          },
        });
      });
  }

  handleError(error) {
    const validationErrors = error.error.validationErrors as ValidationError[];
    if (validationErrors) {
      let message = '';
      validationErrors.forEach((validationError) => {
        message = message.concat(validationError.errorMessage).concat('\n');
      });
      this.notificationService.open({ message, type: 'warn' });
    }
  }

  addClientSecret(params?: any) {
    const { documentData } = params;
    const { field } = params;
    const { refNo, moduleId } = documentData;
    this.documentDefineStore.setLoading(true);
    this.userService
      .setClientSecretData(refNo, field.id)
      .pipe()
      .subscribe((user) => {
        this.update(user as unknown as DocumentDataState, moduleId);
        this.documentDefineStore.setLoading(false);
      });
  }

  removeClientSecret(params?: any) {
    this.addClientSecret(params);
  }

  private update(savedData: DocumentDataState, moduleId: string) {
    this.documentDataService.initStoreData({ ...savedData });
    const { refNo } = savedData;
    const { version } = savedData;
    this.documentDefineService.refetchDefine(moduleId, refNo, of(String(version)));
  }
  copyClientSecret(params?: any) {
    const { documentData } = params;
    const { field } = params;
    const { sysAccClientSecretId } = documentData;
    const { clientSecret1, clientSecret2 } = sysAccClientSecretId;
    const textarea = document.createElement('textarea');
    if (field.id === 'copyClientSecret1') {
      textarea.value = clientSecret1;
    } else if (field.id === 'copyClientSecret2') {
      textarea.value = clientSecret2;
    }
    document.body.appendChild(textarea);
    textarea.select();
    textarea.setSelectionRange(0, 99999);
    document.execCommand('copy');
    document.body.removeChild(textarea);
  }
}
