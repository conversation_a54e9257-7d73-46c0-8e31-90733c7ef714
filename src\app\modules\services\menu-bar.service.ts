import { Dialog } from '@angular/cdk/dialog';
import { HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { TranslocoService } from '@ngneat/transloco';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { isEqual } from 'lodash';
import { combineLatest, Observable } from 'rxjs';
import { filter, map, switchMap } from 'rxjs/operators';

import { UserDto } from '../container/document/cbx-module/user/model/user-dto';
import { DocumentDataState, DocumentStatus } from '../container/document/state';
import { AlertDialogComponent } from '../shared/common/warning-dialog/alert-dialog.component';
import { CBX_URL } from 'src/app/config/constant';
import { generalAlertDialogConfig } from 'src/app/config/dialog';
import { DocumentViewData } from 'src/app/entities';
import { Vpo_VpoChainOfCustodyDtlUpdateDto } from 'src/app/entities/api';
import { BatchUpdateFields, PartyTypes, Templates, Users } from 'src/app/entities/batch-update';
import { AlertDialogTypes, DialogButtonTypes } from 'src/app/entities/dialog';
import { DataListType } from 'src/app/entities/lookup';
import { SelectType } from 'src/app/entities/select-type-dto';
import { BatchUpdateNotificationData, BatchUpdateProgressBarData, Record } from 'src/app/interface/model';
import { DocStatusResponse } from 'src/app/interface/model/doc-status-response';
import { ApiService } from 'src/app/services/api.service';
import { AuthService, CustomURLEncoder } from 'src/app/services/auth.service';
import { MessageDialogService } from 'src/app/services/message-dialog.service';
import { NotificationService } from 'src/app/services/notification.service';
import { UserService } from 'src/app/services/user.service';
import { isEmptyOrNil, resolveFormat } from 'src/app/utils';

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class MenuBarService {
  currentWorkingForUserList: any[];
  constructor(
    private readonly apiService: ApiService,
    public dialog: Dialog,
    protected readonly messageDialogService: MessageDialogService,
    private readonly notificationService: NotificationService,
    private readonly translocoService: TranslocoService,
    private readonly userService: UserService,
    private readonly authService: AuthService,
  ) {
    this.userService.workingForUsers$.pipe(untilDestroyed(this)).subscribe((workingForUsers) => {
      this.currentWorkingForUserList = workingForUsers;
    });
  }

  updateDoc(
    data: DocumentDataState,
    moduleId: string,
    action: string,
    approvalTemplateId?: string,
    documentStatus?: DocumentStatus,
  ) {
    const processUpdate = () => {
      const url = CBX_URL.updateDoc({ module: moduleId, action });
      let params = new HttpParams();

      if (approvalTemplateId) {
        params = params.set('approvalTemplateId', approvalTemplateId);
      }

      if (action === 'sendToVendor') {
        const docMode = documentStatus === DocumentStatus.View ? 'readMode' : 'editMode';
        params = params.set('docMode', docMode);
      }
      return this.apiService.post<DocumentDataState>(url, data, { params });
    };

    const effectiveFrom = isEmptyOrNil(data.effectiveFrom) ? 'from' : data.effectiveFrom;
    const effectiveTo = isEmptyOrNil(data.effectiveTo) ? 'to' : data.effectiveTo;
    const loginId = isEmptyOrNil(data.loginId) ? 'loginId' : data.loginId;
    if (moduleId === 'user' && action === 'saveAndConfirm' && data.substituteList) {
      if (data.substituteList?.length > 0) {
        const idList: string[] = data.substituteList.map((substitute) => substitute.id);
        const ids: string = idList.join(',');
        const currentUser$ = this.authService.getUserByLoginIdOrRefNo(loginId);
        const currentUserSubstitution$ = this.apiService.get<UserDto[]>(
          CBX_URL.getCurrentUserSubstitution(loginId, effectiveFrom, effectiveTo),
        );
        const selectUserSubstitution$ = this.apiService.get<UserDto[]>(
          CBX_URL.getSelectUserSubstitution(ids, effectiveFrom, effectiveTo),
        );
        return combineLatest([currentUserSubstitution$, selectUserSubstitution$, currentUser$]).pipe(
          switchMap(([currentUserSubstitutionList, selectUserSubstitutionList, currentUser]) => {
            if (this.hasValueChange(currentUser, data)) {
              let message = '';
              if (currentUserSubstitutionList.length > 0) {
                currentUserSubstitutionList.forEach((user) => {
                  const tempMessage =
                    'You have already been assigned as a substitute by {otherUserName} ' +
                    'during the period {conflictStartDate} to {conflictEndDate}. Are you sure to continue?<br><br>';
                  const params = {
                    otherUserName: user.userName,
                    conflictStartDate: this.formatDate(user.effectiveFrom),
                    conflictEndDate: this.formatDate(user.effectiveTo),
                  };
                  const formatMessage = this.replaceParams(tempMessage, params);
                  message += formatMessage;
                });
              }
              if (selectUserSubstitutionList.length > 0) {
                selectUserSubstitutionList.forEach((user) => {
                  const tempMessage =
                    '{substituationUserName} has already assigned a substitute from ' +
                    '{startDate} to {endDate}. Are you sure to continue?<br><br>';
                  const params = {
                    substituationUserName: user.userName,
                    startDate: this.formatDate(user.effectiveFrom),
                    endDate: this.formatDate(user.effectiveTo),
                  };
                  const formatMessage = this.replaceParams(tempMessage, params);
                  message += formatMessage;
                });
              }
              if (message === '') {
                return processUpdate();
              }
              return this.messageDialogService.openDialog('YesNo', message, 'warning').closed.pipe(
                filter((result) => result?.type === 'done'),
                switchMap(() => processUpdate()),
              );
            }
            return processUpdate();
          }),
        );
      }
      return processUpdate();
    }
    return processUpdate();
  }

  private replaceParams(template: string, params: { [key: string]: string }): string {
    return template.replace(/\{(\w+)\}/g, (match, key) => params[key] ?? match);
  }

  private formatDate(date: string): string {
    const [year, month, day] = date.split('-');
    return `${day}.${month}.${year}`;
  }

  private hasValueChange(currentUser: any, data: DocumentDataState): boolean {
    const initialSubUsers = isEmptyOrNil(currentUser.substituteList) ? [] : [...currentUser.substituteList];
    const initialEffectiveFrom = isEmptyOrNil(currentUser.effectiveFrom) ? null : currentUser.effectiveFrom;
    const initialEffectiveTo = isEmptyOrNil(currentUser.effectiveTo) ? null : currentUser.effectiveTo;
    const currentSubUsers = data.substituteList;
    const currentEffectiveFrom = isEmptyOrNil(data.effectiveFrom) ? null : data.effectiveFrom;
    const currenteffectiveTo = isEmptyOrNil(data.effectiveTo) ? null : data.effectiveTo;
    const isSubUsersChange = !isEqual(currentSubUsers, initialSubUsers);
    return (
      isSubUsersChange || currentEffectiveFrom !== initialEffectiveFrom || currenteffectiveTo !== initialEffectiveTo
    );
  }

  setToInListing(module: string, docStatus: string, recordList: Record[]): Observable<DocStatusResponse[]> {
    if (!docStatus) {
      throw new Error('no dpcStatus');
    }

    return this.apiService.patch<any>(CBX_URL.setToInListing({ module, docStatus }), recordList);
  }

  batchSetToInListing(module: string, docStatus: string, recordList: Record[]): Observable<DocStatusResponse[]> {
    if (!docStatus) {
      throw new Error('no docStatus');
    }

    return this.apiService.patch<any>(CBX_URL.batchSetToInListing({ module, docStatus }), recordList);
  }

  VpoChainOfCustodyUpdate(params, selectedRows) {
    const vpoChainOfCustodyDtlUpdateList = selectedRows as Vpo_VpoChainOfCustodyDtlUpdateDto[];
    return this.apiService.post<any>(CBX_URL.batchUpdateChainOfCustody, vpoChainOfCustodyDtlUpdateList, {
      params,
    });
  }

  adminModuleUpload(module: string, docStatus: string, recordList: Record[]): Observable<DocStatusResponse[]> {
    return this.apiService.patch<any>(CBX_URL.setToInListing({ module, docStatus }), recordList);
  }
  searchConvertToOnline(moudleId: string, recordList: Record[]): Observable<DocStatusResponse[]> {
    this.openMessageDialog(
      'Okay',
      '230px',
      this.translocoService.translate('document.message.common.viewMarkAsPopupMessage'),
      'information',
    );
    if (moudleId === 'vendor') {
      return this.apiService.patch<any>(CBX_URL.searchConvertToOnline, recordList);
    }
    return this.apiService.patch<any>(CBX_URL.searchConvertFactoryToOnline, recordList);
  }

  vendorAgreeSearchResend(lineItemIds: string[]): Observable<DocStatusResponse[]> {
    const url = CBX_URL.vendorAgreeSearchResend();
    return this.apiService.post(url, lineItemIds);
  }

  vendorAgreeSearchSetInactive(lineItems: string[]): Observable<any> {
    const url = CBX_URL.vendorAgreeSearchSetInactive();
    return this.apiService.post(url, lineItems);
  }

  openMessageDialog(messageButtons: DialogButtonTypes, height: string, message: string, title: AlertDialogTypes) {
    return this.dialog.open(AlertDialogComponent, {
      ...generalAlertDialogConfig,
      data: {
        messageButtons,
        message,
        title,
      },
    });
  }
  markAsInListing(module: string, status: string, recordList: Record[]): Observable<DocStatusResponse[]> {
    if (!status) {
      throw new Error('no status');
    }

    return this.apiService.patch<any>(CBX_URL.markAsInListing({ module, status }), recordList);
  }

  batchMarkAsInListing(module: string, status: string, recordList: Record[]): Observable<DocStatusResponse[]> {
    if (!status) {
      throw new Error('no status');
    }

    return this.apiService.patch<any>(CBX_URL.batchMarkAsInListing({ module, status }), recordList);
  }

  customActionInListing(module: string, actionId: string, viewName: string, recordList: Record[]): Observable<any> {
    if (!actionId) {
      throw new Error('no actionId');
    }

    return this.apiService.post<any>(CBX_URL.customActionInListing({ module, actionId, viewName }), recordList);
  }

  qqAdoptAsNewItem(successMessage: string, recordList: Record[]): Observable<any> {
    const notAllowed = recordList.some((row) => row.newItemAdopted === 'true');
    if (notAllowed) {
      return this.messageDialogService
        .openDialog('Okay', 'Cannot adopt as new item because the item has been adopted.', 'warning')
        .closed.pipe(map(() => false));
    }
    this.notificationService.open({
      message: resolveFormat(successMessage, null),
      position: 'right',
    });
    return this.apiService.patch<any>(CBX_URL.qqAdoptAsNewItem, recordList);
  }

  setToInDoc(
    module: string,
    refNo: string,
    docStatus: string,
    approvalTemplateId: string,
    version: string,
  ): Observable<DocumentViewData> {
    if (!docStatus) {
      throw new Error('no docStatus');
    }

    const params = approvalTemplateId ? new HttpParams().set('approvalTemplateId', approvalTemplateId) : null;

    return this.apiService.patch<any>(CBX_URL.setToInDoc({ module, refNo, docStatus, version }), {}, { params });
  }

  markAsInDoc(
    module: string,
    refNo: string,
    status: string,
    approvalTemplateId: string,
    version: string,
    payload?: any,
  ): Observable<DocumentViewData> {
    if (!status) {
      throw new Error('no status');
    }

    const params = approvalTemplateId ? new HttpParams().set('approvalTemplateId', approvalTemplateId) : null;

    return this.apiService.patch<any>(CBX_URL.markAsInDoc({ module, refNo, status, version }), payload ?? {}, {
      params,
    });
  }

  markAsDocWithLinkedDoc(module: string, status: string, recordList: Record[]): Observable<DocStatusResponse[]> {
    if (!status) {
      throw new Error('no status');
    }
    return this.apiService.patch<any>(CBX_URL.markAsDocWithLinkedDoc({ module, status }), recordList);
  }

  customActionInDoc(module: string, refNo: string, actionId: string): Observable<any> {
    return this.apiService.post(CBX_URL.customActionInDoc({ module, refNo }), actionId);
  }

  createFollowDoc(docRefNo: string, module: string): Observable<DocumentViewData> {
    const followDocumentDto = {
      module,
      docRefNo,
    };
    return this.apiService.post(CBX_URL.followDocument, followDocumentDto);
  }

  unFollowDoc(docRefNo: string, module: string): Observable<DocumentViewData> {
    let params = new HttpParams().set('docRefNo', docRefNo);
    params = params.set('module', module);
    return this.apiService.delete(CBX_URL.followDocument, { params });
  }

  exportInListing(
    body: string[],
    {
      module,
      viewName,
      condition,
      isAllFields,
    }: { module: string; viewName: string; condition: string; isAllFields: string },
  ): Observable<HttpResponse<Blob>> {
    const url = CBX_URL.exportInListing;
    const params = new HttpParams({ fromString: condition, encoder: new CustomURLEncoder() })
      .set('module', module)
      .set('viewName', viewName)
      .set('isAllFields', isAllFields);
    return this.apiService.postWithBinaryResponse(url, body, {
      observe: 'response',
      responseType: 'blob',
      params,
    });
  }

  printInListing(module: string, customFormTemplate: string, body: { refNo: string; version: number }[]) {
    const fromString = `module=${module}&customFormTemplate=${customFormTemplate}`;

    const params = new HttpParams({ fromString });

    return this.apiService.postWithBinaryResponse(CBX_URL.printInListing, body, {
      responseType: 'blob',
      observe: 'response',
      params,
    });
  }

  apiBatchAddAction(body: any): Observable<any> {
    const url = CBX_URL.apiBatchAddAction;
    return this.apiService.post<any>(url, body);
  }

  batchUpdateFieldInListing(entityName: string, body: BatchUpdateProgressBarData, saveType: string): Observable<any> {
    return this.apiService.post(CBX_URL.batchUpdateFieldInListing(entityName, saveType), body);
  }

  getBatchUpFieldsForUser(viewName: string): Observable<any[]> {
    const url = CBX_URL.getBatchUpFieldsForUser(viewName);
    return this.apiService.get(url);
  }

  saveBatchUpFieldsForUser(viewName: string, batchUpdateFields: any[]) {
    const url = CBX_URL.saveBatchUpFieldsForUser(viewName);
    return this.apiService.post(url, batchUpdateFields);
  }

  batchUpdateSendNotification(
    entityName: string,
    data: {
      recordResults: BatchUpdateNotificationData[];
      batchUpdateFields: { fieldId: string; changeTo: string; inputType: string }[];
    },
  ): Observable<any> {
    return this.apiService.post(CBX_URL.batchUpdateSendNotification(entityName), data);
  }

  batchUpdatePartyInListing(
    entityName: string,
    body: string[],
    fromUserName: string,
    toUserName: string,
    isCheckOwner: boolean,
    selectedPartyType: string,
  ): Observable<any> {
    const params = new HttpParams({ encoder: new CustomURLEncoder() })
      .set('entityName', entityName)
      .set('fromUserName', fromUserName)
      .set('toUserName', toUserName)
      .set('isCheckOwner', isCheckOwner)
      .set('selectedPartyType', selectedPartyType);

    return this.apiService.post('api/batchupdate/responsibleparty', body, { params });
  }

  batchUpdateAgreementStatusInListing(vendorRefNo: string, agreementStatus: string, body: string[]): Observable<any> {
    const url = CBX_URL.batchUpdateAgreementStatusInListing(vendorRefNo, agreementStatus);
    return this.apiService.patch(url, body);
  }

  batchAddAgreementStatusInListing(vendorRefNo: string, body: string[]): Observable<any> {
    const url = CBX_URL.batchAddAgreementStatusInListing(vendorRefNo);
    return this.apiService.patch(url, body);
  }

  batchReinitPartyInListing(
    entityName: string,
    body: string[],
    tmplRef: string,
    isKeepUnchanged?: boolean,
  ): Observable<any> {
    const url = CBX_URL.batchReinitPartyInListing(entityName, tmplRef, isKeepUnchanged);
    return this.apiService.post(url, body);
  }

  batchUpFieldInitInListing(entityName: string): Observable<BatchUpdateFields[]> {
    const url = CBX_URL.batchUpFieldInitInListing(entityName);
    return this.apiService.get(url);
  }

  batchUpPartyTmplInitInListing(entityName: string): Observable<Templates[]> {
    const url = CBX_URL.batchUpPartyTmplInitInListing(entityName);
    return this.apiService.get(url);
  }

  batchUpPartyTypeInitInListing(entityName: string): Observable<PartyTypes[]> {
    const url = CBX_URL.batchUpPartyTypeInitInListing(entityName);
    return this.apiService.get(url);
  }

  batchUpUserInitInListing(): Observable<Users[]> {
    const url = CBX_URL.batchUpUserInitInListing;
    return this.apiService.get(url);
  }

  batchUpdateAgreementInListing(payload: Record[] | DocumentViewData, selectedRecords: string[]): Observable<any> {
    const url = CBX_URL.batchUpdateAgreementInListing;
    const body = {
      payload,
      selectedRecords,
    };
    return this.apiService.post(url, body);
  }

  exportInDoc(refNo: string, version: number, { actionId, module }: { actionId: string; module: string }) {
    const url = CBX_URL.exportInDoc({ refNo, version, module, actionId });
    const params = new HttpParams();

    return this.apiService.postWithBinaryResponse(url, null, {
      observe: 'response',
      responseType: 'blob',
      params,
    });
  }

  exportLookupExcel(refNo: string): Observable<HttpResponse<Blob>> {
    const url = CBX_URL.exportLookupExcel({ refNo });
    const params = new HttpParams();

    return this.apiService.postWithBinaryResponse(url, null, {
      observe: 'response',
      responseType: 'blob',
      params,
    });
  }

  exportCodelistExcel(refNo: string): Observable<HttpResponse<Blob>> {
    const url = CBX_URL.exportCodelistExcel({ refNo });
    const params = new HttpParams();

    return this.apiService.postWithBinaryResponse(url, null, {
      observe: 'response',
      responseType: 'blob',
      params,
    });
  }

  exportExcel(module: string, refNo: string, version: number): Observable<HttpResponse<Blob>> {
    const url = CBX_URL.exportExcel({ module, refNo, version });
    const params = new HttpParams();

    return this.apiService.postWithBinaryResponse(url, null, {
      observe: 'response',
      responseType: 'blob',
      params,
    });
  }

  printInDoc(refNo: string, version: number, { actionId, module }: { actionId: string; module: string }) {
    const url = CBX_URL.printInDoc({ refNo, version, module, actionId });
    const params = new HttpParams();

    return this.apiService.postWithBinaryResponse(url, null, {
      observe: 'response',
      responseType: 'blob',
      params,
    });
  }

  refreshItems(data: DocumentDataState, approvalTemplateId: string) {
    const url = CBX_URL.refreshItems;
    let params = new HttpParams();
    if (approvalTemplateId) {
      params = params.set('approvalTemplateId', approvalTemplateId);
    }
    return this.apiService.post<DocumentDataState>(url, data, {
      params,
    });
  }

  refreshVpoChainOfCustody(itemRefNo: string) {
    const url = CBX_URL.refreshVpoChainOfCustody(itemRefNo);
    const params = new HttpParams();
    return this.apiService.post<DocumentDataState>(url, null, {
      params,
    });
  }

  saveAfterRefresh(data: DocumentDataState, approvalTemplateId: string) {
    const url = CBX_URL.saveAfterRefresh;
    let params = new HttpParams();
    if (approvalTemplateId) {
      params = params.set('approvalTemplateId', approvalTemplateId);
    }
    return this.apiService.post<DocumentDataState>(url, data, {
      params,
    });
  }

  refreshVpoItemByRefNoAndVersion(refNo: string, version: number) {
    const url = CBX_URL.refreshVpoItemByRefNoAndVersion({ refNo, version });
    const params = new HttpParams();
    return this.apiService.post<DocumentDataState>(url, null, {
      params,
    });
  }

  getRefreshItemUpdateDoc(refNo: string, version: number) {
    const url = CBX_URL.getRefreshItemUpdateDoc({ refNo, version });
    return this.apiService.get<DocumentDataState>(url);
  }

  getFailedRecords(responses: DocStatusResponse[]) {
    return responses
      .filter((response) => response.statusCode !== 'OK')
      .map((failResponse) => failResponse.body as string[])[0]
      .join(', ');
  }

  hasFailedResponse(responses: DocStatusResponse[]) {
    return !!this.getFailedRecords(responses)?.length;
  }

  getSuccessRecords(responses: DocStatusResponse[]) {
    return responses
      .filter((response) => response.statusCode === 'OK')
      .map((successResponse) => successResponse.body as DocumentViewData[])[0]
      .map((successBody) => successBody.refNo)
      .join(', ');
  }

  getSuccessRecord(responses: DocStatusResponse[]) {
    return responses
      .filter((response) => response.statusCode === 'OK')
      .map((successResponse) => successResponse.body as DocumentViewData[])[0];
  }

  hasSuccessResponse(responses: DocStatusResponse[]) {
    return !!this.getSuccessRecords(responses).length;
  }

  crossUpdateDoc(refNo: string, module: string): Observable<any> {
    return this.apiService.post(CBX_URL.crossUpdateDoc({ module, refNo }), null);
  }

  ibDownloadQcPack(refNo: string, version: number) {
    const url = CBX_URL.ibDownloadQcPack({ refNo, version });
    const params = new HttpParams();
    return this.apiService.postWithBinaryResponse(url, null, {
      observe: 'response',
      responseType: 'blob',
      params,
    });
  }

  handleStatusResponse(statusResponse: DocStatusResponse[], successMessage: string, failedMessage: string) {
    if (this.hasFailedResponse(statusResponse)) {
      const failedRecords = this.getFailedRecords(statusResponse);
      this.openMessageDialog('Okay', null, resolveFormat(failedMessage, null, [failedRecords]), 'information');
    }

    if (!this.hasSuccessResponse(statusResponse)) {
      return false;
    }

    const successRecord = this.getSuccessRecords(statusResponse);

    this.notificationService.open({
      message: resolveFormat(successMessage, null, [successRecord]),
      position: 'right',
    });
    return true;
  }

  copyToClipboardAction(refNo: string, module: string): Observable<any> {
    return this.apiService.post(CBX_URL.copyToClipboardInDoc({ module, refNo }), null);
  }

  pasteFromClipboardAction(module: string, data: DocumentDataState): Observable<any> {
    const url = CBX_URL.pasteFromClipboardActionInDoc({ module });
    return this.apiService.post(url, data);
  }

  selectTypeInitInListing(entityName: string): Observable<SelectType[]> {
    const url = entityName === 'hcl' ? CBX_URL.hclTypeInitInListing : CBX_URL.dataListTypeInitInListing(entityName);
    return this.apiService.get(url);
  }

  getSelectedType$(module: string, id: string): Observable<SelectType> {
    const url = module === 'hcl' ? CBX_URL.getSelectedHclType({ id }) : CBX_URL.getSelectedDataListType({ id });
    return this.apiService.getCached<SelectType>(url);
  }

  lookupTypeInitInListing(entityName: string): Observable<DataListType[]> {
    const url = CBX_URL.lookupTypeInitInListing(entityName);
    return this.apiService.get(url);
  }
}
