import { DialogRef, DIALOG_DATA } from '@angular/cdk/dialog';
import { CommonModule } from '@angular/common';
import { HttpParams } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, inject, Inject, OnInit } from '@angular/core';

import { TranslocoModule } from '@ngneat/transloco';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { ColDef, GridApi, GridReadyEvent, RowDataUpdatedEvent, ValueGetterParams } from 'ag-grid-community';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  map,
  Observable,
  of,
} from 'rxjs';
import { switchMap, auditTime, shareReplay, tap } from 'rxjs/operators';

import {
  ChainOfCustodySupplier,
  ChainOfCustodySupplierSelectData,
} from '../../model/chain-of-custody-supplier-select-data';
import { CBX_URL } from 'src/app/config/constant';
import { ChainOfCustodySupplierSelectDialogData } from 'src/app/interface/model';
import { CbxDialogHeaderComponent } from 'src/app/modules/shared/common/cbx-dialog-header/cbx-dialog-header.component';
import { TableSelectionMode } from 'src/app/modules/shared/common/cbx-table/cbx-table';
import { CbxTableCellMapperService } from 'src/app/modules/shared/common/cbx-table/cbx-table-cell-mapper.service';
import { CbxTableComponent } from 'src/app/modules/shared/common/cbx-table/cbx-table.component';
import { SearchInputComponent } from 'src/app/modules/shared/common/search-input/search-input.component';
import { ApiService } from 'src/app/services/api.service';
import { autoSizeAll, genGetIdFunc, getHeaderMinWidth } from 'src/app/utils';

@UntilDestroy()
@Component({
  selector: 'app-chain-of-custody-supplier-select-dialog',
  templateUrl: './chain-of-custody-supplier-select-dialog.component.html',
  styleUrls: ['./chain-of-custody-supplier-select-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule, CbxDialogHeaderComponent, SearchInputComponent, CbxTableComponent, TranslocoModule],
})
export class ChainOfCustodySupplierSelectDialogComponent implements OnInit {
  cbxTableCellComponents = inject(CbxTableCellMapperService).components;

  gridApi: GridApi;

  searchText$ = new BehaviorSubject('');

  title = 'Select Vendor/Factory';
  rowSelection: TableSelectionMode = 'single';
  vendorRefNo$ = new BehaviorSubject<string>(null);
  factRefNos$ = new BehaviorSubject<string[]>(null);
  refNo$ = new BehaviorSubject<string>(null);

  supplier: ChainOfCustodySupplier[];
  dataKeys = ['id'];
  getKey: (data: any) => string;
  selectedRowsEntities: { [key: string]: ChainOfCustodySupplier } = {};

  protected readonly hasSelection$ = new BehaviorSubject<boolean>(false);

  supplierList$: Observable<ChainOfCustodySupplierSelectData[]> = combineLatest([
    this.vendorRefNo$,
    this.factRefNos$,
  ]).pipe(
    auditTime(0),
    switchMap(([vendorRefNo, factRefNos]) => {
      let params = new HttpParams();
      params = params.set('vendorRefNo', vendorRefNo);
      params = params.set('factRefNos', factRefNos.join(','));
      params = params.set('refNo', this.refNo$.getValue());

      return this.apiService.get<ChainOfCustodySupplierSelectData[]>(CBX_URL.ChainOfCustodySupplier(), { params });
    }),
    catchError(() => of([])),
    tap(() => this.loading$.next(false)),
    shareReplay(1),
  );

  totalSize$ = this.supplierList$.pipe(map((supplierList) => supplierList?.length));

  defaultColDef: ColDef = {
    headerClass: 'header-column',
    cellRenderer: 'textComponent',
  };

  columnDefs: ColDef[] = [
    {
      field: 'businessName',
      headerName: 'Company Name',
    },
    {
      field: 'factoryOrVendor',
      headerName: 'Factory/Vendor',
      valueGetter: (params: ValueGetterParams) => {
        const { module } = params.data;
        const moduleMap = {
          vendor: 'Vendor',
          fact: 'Factory',
        };
        return moduleMap[module];
      },
    },
    {
      field: 'refNo',
      headerName: 'ID',
    },
    {
      field: 'type',
      headerName: 'Type',
      valueGetter: (params: ValueGetterParams) => {
        const { module } = params.data;
        const typeMap = {
          vendor: 'Vendor Type',
          fact: 'Factory Type',
        };
        return typeMap[module];
      },
    },
    {
      field: 'status',
      headerName: 'Status',
    },
  ];

  loading$ = new BehaviorSubject<boolean>(true);

  constructor(
    protected readonly dialogRef: DialogRef<any, ChainOfCustodySupplierSelectDialogComponent>,
    private readonly apiService: ApiService,
    @Inject(DIALOG_DATA) public data: ChainOfCustodySupplierSelectDialogData,
  ) {
    this.vendorRefNo$.next(data.vendorRefNo);
    this.factRefNos$.next(data.factRefNos);
    this.refNo$.next(data.refNo);
    this.supplier = data.supplier as ChainOfCustodySupplier[];
    this.getKey = genGetIdFunc(this.dataKeys);

    if (this.dataKeys.length && this.supplier.length) {
      this.selectedRowsEntities = this.supplier.reduce((acc, record) => {
        const key = this.getKey(record);
        acc[key] = record;
        return acc;
      }, {});
    }

    if (this.supplier.length) {
      this.hasSelection$.next(true);
    }
  }

  ngOnInit() {
    this.onFilterInputChanged();
  }

  gridReady(gridReadyEvent: GridReadyEvent) {
    this.gridApi = gridReadyEvent.api;

    this.columnDefs = this.columnDefs.map((colDef) => ({ ...colDef, minWidth: getHeaderMinWidth(colDef.headerName) }));
  }

  onFirstDataRendered() {
    autoSizeAll(this.gridApi);
  }

  checkSelectedRow(rowDataUpdated: RowDataUpdatedEvent) {
    const originalSelectedLength = Object.keys(this.selectedRowsEntities).length;
    if (originalSelectedLength && this.dataKeys.length) {
      rowDataUpdated.api.forEachNode((rowNode) => {
        const key = this.getKey(rowNode.data);

        if (this.selectedRowsEntities[key]) {
          delete this.selectedRowsEntities[key];
          rowNode.setSelected(true);
        }
      });
    }
  }

  handleRowClicked() {
    this.hasSelection$.next(true);
  }

  onCancel() {
    this.dialogRef.close({ type: 'cancel' });
  }

  onDone(selectedRows: ChainOfCustodySupplierSelectData[] = this.gridApi.getSelectedRows()) {
    this.dialogRef.close({ type: 'done', payload: selectedRows });
  }

  onFilterInputChanged() {
    this.searchText$.pipe(debounceTime(300), distinctUntilChanged(), untilDestroyed(this)).subscribe((searchText) => {
      this.gridApi.setGridOption('quickFilterText', searchText);
      this.gridApi.deselectAll();
      this.gridApi.selectAllFiltered();
    });
  }
}
