import { Injectable } from '@angular/core';

import { isNil } from '@datorama/akita';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import equal from 'fast-deep-equal';
import * as R from 'ramda';
import { BehaviorSubject, combineLatest, from, merge, Observable, of } from 'rxjs';
import {
  concatMap,
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  mergeMap,
  pairwise,
  shareReplay,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs/operators';

import { CodelistItem } from '../../../../entities/codelist';
import { FieldDefine, SectionType } from '../../../../entities/form-field';
import { FormFieldTypes } from '../../../../entities/form-field-types';
import { AnyObject, Record } from '../../../../interface/model';
import { deepEqual, dotPath, isArray, isEmptyOrNil, isNotEmptyOrNil, sliceBrackets } from '../../../../utils';
import {
  DocumentDataQuery,
  DocumentDataService,
  DocumentStatus,
  DocumentStatusService,
  SubSection,
  SubSectionsQuery,
} from '../state';
import { DocumentService } from 'src/app/modules/services/document.service';

interface CascadeField {
  type:
    | 'form'
    | 'grid'
    | 'groupParentGrid'
    | 'groupChildGrid'
    | 'attachment'
    | 'powerbi'
    | 'viewGrid'
    | 'vpoTraceabilityMap'
    | 'htmlEditor';
  sectionId: string;
  field: FieldDefine;
}

@UntilDestroy()
@Injectable()
export class CascadeDropdownService {
  dataInitializing$ = this.documentDataQuery
    .selectDataInitialized$()
    .pipe(filter((dataInitialized) => !dataInitialized));

  cascadeDropdownStore: {
    [sectionId: string]: {
      [rowId: string]: {
        [fieldId: string]: {
          parentCodelistValue: CodelistItem[];
          options: BehaviorSubject<CodelistItem[]>;
        };
      };
    };
  } = {};
  cascadeDropdownStore$ = new BehaviorSubject<any>({});
  cascadeFormDropdownStore$ = new BehaviorSubject<AnyObject<any>>({});

  cascadeFieldsByTypeMap$: Observable<Map<CascadeField['type'], CascadeField[]>>;

  nestedFieldTypeMap = {
    [FormFieldTypes.fieldGroup]: true,
    [FormFieldTypes.expansionGroup]: true,
  };

  private readonly filterCascadeExprStrategy = (field: FieldDefine) => field.cascadeExpr;

  constructor(
    private readonly documentDataService: DocumentDataService,
    private readonly documentDataQuery: DocumentDataQuery,
    private readonly subSectionsQuery: SubSectionsQuery,
    private readonly documentService: DocumentService,
    private readonly documentStatusService: DocumentStatusService,
  ) {}

  registerCascadeLogic() {
    this.cascadeFieldsByTypeMap$ = this.subSectionsQuery.subSections$.pipe(
      filter(isNotEmptyOrNil),
      debounceTime(200),
      map((section: SubSection[]) => {
        // TODO: Some sections should not have cascade fields.
        const types: CascadeField['type'][] = [
          'form',
          'grid',
          'groupParentGrid',
          'groupChildGrid',
          'attachment',
          'powerbi',
          'htmlEditor',
        ];
        const cascadeFieldsByTypeMap = new Map<CascadeField['type'], CascadeField[]>(types.map((type) => [type, []]));

        const typePatternMap: {
          [key in SectionType]: { searchScope: { scope: keyof SubSection; type: CascadeField['type'] }[] };
        } = {
          form: { searchScope: [{ scope: 'fields', type: 'form' }] },
          grid: { searchScope: [{ scope: 'fields', type: 'grid' }] },
          attachment: { searchScope: [{ scope: 'fields', type: 'attachment' }] },
          viewGrid: { searchScope: [{ scope: 'fields', type: 'viewGrid' }] },
          powerbi: { searchScope: [{ scope: 'fields', type: 'powerbi' }] },
          groupGrid: {
            searchScope: [
              { scope: 'fields', type: 'groupChildGrid' },
              { scope: 'groupFields', type: 'groupParentGrid' },
            ],
          },
          sectionList: { searchScope: [{ scope: 'fields', type: 'grid' }] },
          vpoTraceabilityMap: { searchScope: [{ scope: 'fields', type: 'vpoTraceabilityMap' }] },
          htmlEditor: { searchScope: [{ scope: 'fields', type: 'htmlEditor' }] },
        };

        // cascade expr for drop-down
        section.forEach((subSection) => {
          typePatternMap[subSection.parentType].searchScope.forEach(({ scope, type }) => {
            const cascadeFields: FieldDefine[] = this.findCascadeFields(subSection[scope] as FieldDefine[]);

            if (cascadeFields.length === 0) {
              return;
            }

            const cascadeSectionFields = cascadeFieldsByTypeMap.get(type);
            cascadeSectionFields.push(
              ...cascadeFields.map((field) => ({ type, sectionId: subSection.parentId, field })),
            );
          });
        });
        return cascadeFieldsByTypeMap;
      }),
      untilDestroyed(this),
      takeUntil(this.dataInitializing$),
      shareReplay(1),
    );

    this.registerTableSelectCascade();
    this.registerFormCascade();
    this.registerGridCascade();
    this.registerGroupParentGridCascade();
  }

  private findCascadeFields(fields: FieldDefine[]): FieldDefine[] {
    const nestedFields = fields
      .filter((field) => this.nestedFieldTypeMap[field.type] && field.fields)
      .map((field) => this.findCascadeFields(field.fields))
      .flat();

    return [...fields.filter(this.filterCascadeExprStrategy), ...nestedFields];
  }

  private getGridCascadeValueChange$(sectionType: CascadeField['type'], checkIdIsExist: (data: any) => boolean) {
    return this.cascadeFieldsByTypeMap$.pipe(
      concatMap((cascadeFieldsByTypeMap) => from(cascadeFieldsByTypeMap.get(sectionType))),
      mergeMap((cascadeField: CascadeField) => {
        const { field } = cascadeField;
        const [, targetPath] = field.cascadeExpr.split('==').map(sliceBrackets);
        return combineLatest([
          of(cascadeField),
          this.documentDataQuery
            .select(cascadeField.sectionId)
            .pipe(filter(checkIdIsExist), distinctUntilChanged(deepEqual)),
          targetPath?.startsWith('cascadeByHeaderField=')
            ? this.documentDataQuery.select(targetPath?.substring('cascadeByHeaderField='.length))
            : of(null),
        ]);
      }),
      mergeMap(([cascadeField, gridData, headerFieldValue]) => {
        if (!gridData || (gridData.length === 0 && isEmptyOrNil(headerFieldValue))) {
          return of([]);
        }

        const { field } = cascadeField;
        const showUnspecifiedOptionIfNoMatch = field.cascadeShowUnspecifiedOptionIfNoMatch;

        const [sourcePath, originalTargetPath] = field.cascadeExpr.split('==').map(sliceBrackets);
        const targetPath = originalTargetPath?.startsWith('cascadeByHeaderField=')
          ? originalTargetPath.substring('cascadeByHeaderField='.length)
          : originalTargetPath ?? '';

        return combineLatest(
          (gridData as Record[]).map((rowData, rowIndex) => {
            const rowId = rowData.id;
            if (
              targetPath.indexOf('customFields') !== -1 ||
              (isEmptyOrNil(dotPath(targetPath, rowData)) && !targetPath.startsWith('module='))
            ) {
              const data = this.documentDataQuery.getValue();
              const parentCodelistValue =
                dotPath(targetPath, data) ??
                dotPath(
                  `${targetPath.substring(0, targetPath.indexOf('.'))}.${rowIndex}${targetPath.substring(
                    targetPath.indexOf('.'),
                  )}`,
                  data,
                ) ??
                dotPath(`originalDocData.${targetPath}`, data);
              const selfOptions$ = this.documentService.getSelectItem$(field);
              return selfOptions$.pipe(
                map((options) => ({
                  parentCodelistValue,
                  cascadeField,
                  options: this.documentService.filterCascadeCodelist(
                    options,
                    parentCodelistValue,
                    sourcePath,
                    showUnspecifiedOptionIfNoMatch,
                  ),
                  rowId,
                  rowIndex,
                  rowData,
                })),
              );
            }
            let parentCodelistValue = targetPath.startsWith('module=')
              ? targetPath.substring(7)
              : dotPath(targetPath, rowData);
            if (field.uiId === 'ui.tabProductionStatus.inspectReportProductionStatusList.color') {
              parentCodelistValue = R.path(targetPath.split('.'), rowData);
            }
            const selfOptions$ = this.constructOption(field);
            return selfOptions$.pipe(
              map((options) =>
                this.documentService.filterCascadeCodelist(
                  options,
                  parentCodelistValue,
                  sourcePath,
                  showUnspecifiedOptionIfNoMatch,
                ),
              ),
              map((options) => ({
                parentCodelistValue,
                cascadeField,
                options,
                rowId,
                rowIndex,
                rowData,
              })),
            );
          }),
        );
      }),
      distinctUntilChanged(deepEqual),
      takeUntil(this.dataInitializing$),
      untilDestroyed(this),
    );
  }

  private constructOption(field: FieldDefine) {
    if (field.uiId === 'ui.tabProductionStatus.inspectReportProductionStatusList.color') {
      const { inspectReportItemsList } = this.documentDataQuery.getValue();
      const newColorOption = [];
      inspectReportItemsList.forEach((item) => {
        if (item?.item?.itemColorList) {
          item.item.itemColorList.forEach((color) => {
            if (!newColorOption.some((m) => m.code === color?.colorCode)) {
              newColorOption.push({ name: color.shortName, code: color.colorCode, itemNo: item.item.itemNo });
            }
          });
        }
      });
      return of(newColorOption);
    }
    return this.documentService.getSelectItem$(field);
  }

  private getGroupGridCascadeValueChange$(sectionType: CascadeField['type'], checkIdIsExist: (data: any) => boolean) {
    return this.cascadeFieldsByTypeMap$.pipe(
      concatMap((cascadeFieldsMapByType) => from(cascadeFieldsMapByType.get(sectionType))),
      mergeMap((cascadeField: CascadeField) => {
        const { field } = cascadeField;
        const showUnspecifiedOptionIfNoMatch = field.cascadeShowUnspecifiedOptionIfNoMatch;
        const [sourcePath, targetPath] = field.cascadeExpr.split('==').map(sliceBrackets);
        const options$ = this.documentService.getSelectItem$(cascadeField.field);

        return options$.pipe(
          mergeMap((options) =>
            this.documentDataQuery.select(cascadeField.sectionId).pipe(
              filter(checkIdIsExist),
              map((groupDataList) =>
                groupDataList?.flatMap((groupData, groupIndex) =>
                  groupData?.map((row, rowIndex) => {
                    const parentCodelistValue = dotPath(targetPath, row);
                    return {
                      parentCodelistValue,
                      cascadeField,
                      options: this.documentService.filterCascadeCodelist(
                        options,
                        parentCodelistValue,
                        sourcePath,
                        showUnspecifiedOptionIfNoMatch,
                      ),
                      rowId: row.id,
                      groupIndex,
                      rowIndex,
                      rowData: row,
                    };
                  }),
                ),
              ),
              distinctUntilChanged(deepEqual),
            ),
          ),
        );
      }),
      takeUntil(this.dataInitializing$),
      untilDestroyed(this),
    );
  }

  private registerTableSelectCascade() {
    // cascade by for form table-select
    this.subSectionsQuery.subSections$
      .pipe(
        filter(isNotEmptyOrNil),
        map((section: SubSection[]) => {
          const fieldByCascadeByMap = new Map<string, FieldDefine[]>();
          section
            .filter((subSection: SubSection) => subSection.parentType === 'form')
            .forEach((subSection: SubSection) => {
              subSection.fields
                .filter((field) => field.cascadeBy && field.type === FormFieldTypes.tableSelect)
                .forEach((field) => {
                  if (fieldByCascadeByMap.has(field.cascadeBy)) {
                    fieldByCascadeByMap.get(field.cascadeBy).push(field);
                    return;
                  }
                  fieldByCascadeByMap.set(field.cascadeBy, [field]);
                });
            });
          return fieldByCascadeByMap;
        }),
        switchMap((cascadeTableSelect) =>
          merge(
            ...[...cascadeTableSelect.entries()].map(([cascadeBy, influenceFields]) =>
              this.documentDataQuery.selectWithFetchInit([cascadeBy]).pipe(
                debounceTime(300),
                pairwise(),
                filter(
                  ([{ dataInitialized: previousInit }, { dataInitialized: currentInit }]) =>
                    !!(previousInit && currentInit),
                ),
                filter(([{ [cascadeBy]: prev }, { [cascadeBy]: curr }]) => {
                  if (isArray(prev) && isArray(curr)) {
                    return !equal(
                      prev.map((row) => R.omit(['id'], row)),
                      curr.map((row) => R.omit(['id'], row)),
                    );
                  }
                  if (isNotEmptyOrNil(prev?.id) && isNotEmptyOrNil(curr?.id)) {
                    return !equal(prev.id, curr.id);
                  }
                  return !equal(prev, curr);
                }),
                map(() => influenceFields),
              ),
            ),
          ),
        ),
        takeUntil(this.dataInitializing$),
        untilDestroyed(this),
      )
      .subscribe((fields: FieldDefine[]) => {
        fields.forEach((field) => this.documentDataService.updateData({ [field.id]: null }));
      });
  }

  private registerFormCascade() {
    this.cascadeFieldsByTypeMap$
      .pipe(
        tap(() => this.cascadeFormDropdownStore$.next(null)),
        map((cascadeFieldsMapByType: Map<string, CascadeField[]>) => cascadeFieldsMapByType.get('form')),
        map((cascadeFields: CascadeField[]) => {
          const cascadeFormDropdownStore = {};

          cascadeFields.forEach(({ field }) => {
            const showUnspecifiedOptionIfNoMatch = field.cascadeShowUnspecifiedOptionIfNoMatch;
            const [sourcePath, targetPath] = field.cascadeExpr.split('==').map(sliceBrackets);
            const parentCodelistValue$ = this.documentDataQuery.select((state) => dotPath(targetPath, state));
            const selfOptions$ = this.documentService.getSelectItem$(field).pipe(distinctUntilChanged(deepEqual));

            const key = `${field.id}-${field.cascadeBy}`;
            let clearFieldValueFlag = false;
            const options$ = of('').pipe(
              tap(() => {
                clearFieldValueFlag = false;
              }),
              mergeMap(() => combineLatest([parentCodelistValue$, selfOptions$])),
              map(([parentCodelistValue, options]) =>
                this.documentService.filterCascadeCodelist(
                  options,
                  targetPath.startsWith('module=') ? targetPath.substring(7) : parentCodelistValue,
                  sourcePath,
                  showUnspecifiedOptionIfNoMatch,
                ),
              ),
              tap((options) => {
                const documentStatus = this.documentStatusService.getDocumentStatus();
                if (clearFieldValueFlag && documentStatus !== DocumentStatus.View) {
                  if (field.id.startsWith('custCodelist')) {
                    const customField = this.documentDataQuery.getValue().customFields;
                    const custFieldId = field.id.replace('custCodelist', 'customFieldCodeList');
                    if (customField) {
                      if (!options.find((newOption) => newOption.code === customField[custFieldId]?.code)) {
                        this.documentDataService.updateTieredData(null, field.mapping.split('.'));
                      }
                    }
                  } else {
                    const formData = this.documentDataQuery.getValue();
                    const fieldValue = field?.mapping ? dotPath(field.mapping, formData) : formData[field.id];
                    if (fieldValue && !options.find((option) => option?.code === fieldValue?.code)) {
                      this.documentDataService.setIsFetched(false);
                      this.documentDataService.updateData({ [field.id]: null });
                      this.documentDataService.setIsFetched(true);
                    }
                  }
                } else {
                  clearFieldValueFlag = true;
                }
              }),
              takeUntil(this.dataInitializing$),
            );

            cascadeFormDropdownStore[key] = options$;
          });

          return cascadeFormDropdownStore;
        }),
        tap((cascadeFormDropdownStore: AnyObject<any>) =>
          this.cascadeFormDropdownStore$.next(cascadeFormDropdownStore),
        ),
        untilDestroyed(this),
        takeUntil(this.dataInitializing$),
      )
      .subscribe();
  }

  private registerGridCascade() {
    // filter id is null
    const checkGridIdExist = (data) => !data?.some((row) => isNil(row.id));
    this.getGridCascadeValueChange$('grid', checkGridIdExist)
      .pipe(untilDestroyed(this))
      .subscribe({
        next: (optionsDetails) => this.registerGridDropDownStore(optionsDetails),
      });
  }

  private registerGroupParentGridCascade() {
    // filter id is null
    const checkGroupGridIdExist = (data) => !data?.[0]?.some((row) => isNil(row.id));
    this.getGroupGridCascadeValueChange$('groupParentGrid', checkGroupGridIdExist).subscribe({
      next: (optionsDetails) => this.registerGroupGridDropDownStore(optionsDetails),
    });
  }

  private registerGridDropDownStore(
    optionsDetails: {
      options: CodelistItem[];
      rowId: string;
      rowIndex: number;
      cascadeField: CascadeField;
      parentCodelistValue: CodelistItem[];
      rowData: any;
    }[],
  ) {
    const updateOptionsDetails = [];
    optionsDetails.forEach((optionsDetail) => {
      const {
        cascadeField: { sectionId, field },
        rowIndex,
        rowId,
        options,
        parentCodelistValue,
        rowData,
      } = optionsDetail;

      // find existing cascadeRow
      const cascadeRowOptions: {
        parentCodelistValue: CodelistItem[];
        options: BehaviorSubject<CodelistItem[]>;
      } = R.path([sectionId, rowId, field.cascadeBy], this.cascadeDropdownStore);

      // update existing dropdown option
      if (cascadeRowOptions) {
        if (
          this.checkParentValueUpdate(cascadeRowOptions.parentCodelistValue, parentCodelistValue) &&
          this.checkCellValueNeedUpdate(rowData, field.id, options)
        ) {
          this.documentDataService.updateCellValueByFieldId(null, sectionId, rowIndex, field.id, rowId);
        }
        cascadeRowOptions.parentCodelistValue = parentCodelistValue;
        cascadeRowOptions.options.next(options);
        this.cascadeDropdownStore$.next(this.cascadeDropdownStore);
        return;
      }

      // add new cascade dropdown option
      this.cascadeDropdownStore = R.assocPath(
        [sectionId, rowId, field.cascadeBy],
        {
          parentCodelistValue,
          options: new BehaviorSubject(options),
        },
        this.cascadeDropdownStore,
      );
      this.cascadeDropdownStore$.next(this.cascadeDropdownStore);
    });
    updateOptionsDetails.forEach((optionsDetail) => {
      const {
        cascadeField: { sectionId, field },
        rowIndex,
        rowId,
      } = optionsDetail;
      this.documentDataService.updateCellValueByFieldId(null, sectionId, rowIndex, field.id, rowId);
    });
  }

  checkParentValueUpdate(cascadeRowParentCodelistValue: any, parentCodelistValue: any) {
    const cascadeParentCodeListCodes = [];
    const parentCodelistCodes = [];
    if (isArray(cascadeRowParentCodelistValue)) {
      cascadeRowParentCodelistValue?.forEach((parentCodelist) => {
        cascadeParentCodeListCodes.push(parentCodelist?.code);
      });
    } else {
      cascadeParentCodeListCodes.push(cascadeRowParentCodelistValue);
    }

    if (isArray(parentCodelistValue)) {
      parentCodelistValue?.forEach((parentCodelist) => {
        parentCodelistCodes.push(parentCodelist?.code);
      });
    } else {
      parentCodelistCodes.push(parentCodelistValue);
    }
    return !deepEqual(cascadeParentCodeListCodes, parentCodelistCodes);
  }

  checkCellValueNeedUpdate(rowData: any, fieldId: string, options: CodelistItem[]) {
    const fieldValue = rowData[fieldId];
    let needUpdateCellValue = false;
    if (isEmptyOrNil(fieldValue)) {
      return false;
    }
    if (isArray(fieldValue)) {
      fieldValue?.forEach((value) => {
        if (!needUpdateCellValue) {
          needUpdateCellValue = !options.find((option) => option?.code === value?.code);
        }
      });
    } else {
      return !options.find((option) => option?.code === fieldValue?.code);
    }
    return needUpdateCellValue;
  }

  private registerGroupGridDropDownStore(
    optionsDetails: {
      options: CodelistItem[];
      rowId: string;
      rowIndex: number;
      cascadeField: CascadeField;
      parentCodelistValue: CodelistItem[];
    }[],
  ) {
    const updateOptionsDetails = [];
    optionsDetails.forEach((optionsDetail) => {
      const {
        cascadeField: { sectionId, field },
        rowId,
        options,
        parentCodelistValue,
      } = optionsDetail;

      // find existing cascadeRow
      const cascadeRowOptions: {
        parentCodelistValue: CodelistItem[];
        options: BehaviorSubject<CodelistItem[]>;
      } = R.path([sectionId, rowId, field.cascadeBy], this.cascadeDropdownStore);

      // update existing dropdown option
      if (cascadeRowOptions) {
        if (cascadeRowOptions.parentCodelistValue !== parentCodelistValue) {
          updateOptionsDetails.push(optionsDetail);
        }
        cascadeRowOptions.parentCodelistValue = parentCodelistValue;
        cascadeRowOptions.options.next(options);
        this.cascadeDropdownStore$.next(this.cascadeDropdownStore);
        return;
      }

      // add new cascade dropdown option
      this.cascadeDropdownStore = R.assocPath(
        [sectionId, rowId, field.cascadeBy],
        {
          parentCodelistValue,
          options: new BehaviorSubject(options),
        },
        this.cascadeDropdownStore,
      );
      this.cascadeDropdownStore$.next(this.cascadeDropdownStore);
    });
    updateOptionsDetails.forEach((optionsDetail) => {
      const {
        cascadeField: { sectionId, field },
        rowIndex,
        groupIndex,
      } = optionsDetail;
      this.documentDataService.updateGroupGridCell(sectionId, groupIndex, rowIndex, field, null);
    });
  }
}
