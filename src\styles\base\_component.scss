@import '/src/styles/mixin/media-query-mixin.scss';

.high-progress-bar {
  --progress-bar-height: 8px;
}

.divider {
  border-top: 1px solid #0000001f;
}

.divider-vertical {
  border-right: 1px solid #0000001f;
}

.dark-divider {
  border-color: #c4c7ca;
}

.white-divider {
  border-color: var(--color-primary-white);
}

.radio-size-15 {
  --radio-size: 15px;
}

.checkbox-size-16 {
  --checkbox-size: 16px;
}

.checkbox-border-width-2 {
  --checkbox-border-width: 2px;
}

.checkbox-checked-color-accent {
  --checkbox-checked-color: #35c699;
}

.checkbox-ripple-scale-2 {
  --checkbox-ripple-scale: 2;
}

.icon-size-12 {
  --icon-size: 12px;
}

.icon-size-14 {
  --icon-size: 14px;
}

.icon-size-16 {
  --icon-size: 16px;
}

.icon-size-18 {
  --icon-size: 18px;
}

.icon-size-20 {
  --icon-size: 20px;
}

.icon-size-28 {
  --icon-size: 28px;
}

.reset-button-style {
  color: var(--button-color, var(--black-03));
  padding: 0;
  text-align: left;
  background-color: inherit;
  border: 0;
  cursor: pointer;
  &:focus-visible {
    outline: auto;
  }
  &:disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

.button-primary-color {
  --button-color: #4288f5;
}

.icon-button {
  cursor: pointer;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  display: inline-grid;
  place-items: center;
  border-radius: var(--icon-button-radius, 50%);
  width: var(--icon-button-size, 40px);
  height: var(--icon-button-size, 40px);
  border: 0;
  outline: 0;
  padding: 0;
  background-color: transparent;

  &:hover {
    background-color: rgba($color: #000, $alpha: 0.04);
  }
  &:focus {
    background-color: rgba($color: #000, $alpha: 0.12);
  }
  &:active {
    background-color: rgba($color: #000, $alpha: 0.2);
  }

  &.gray-color {
    color: #3a464d;
    background-color: #f5f5f5;

    &:hover {
      background-color: #e1e1e1;
    }
    &:focus {
      background-color: #cdcdcd;
    }
    &:active {
      background-color: #b9b9b9;
    }
  }

  &.primary-color {
    color: #4288f5;

    &:hover {
      background-color: rgba($color: #4288f5, $alpha: 0.04);
    }
    &:focus {
      background-color: rgba($color: #4288f5, $alpha: 0.12);
    }
    &:active {
      background-color: rgba($color: #4288f5, $alpha: 0.2);
    }
  }

  &.primary-background {
    &:hover {
      background-color: rgba($color: #4288f5, $alpha: 0.1);
    }
    &:focus {
      background-color: rgba($color: #4288f5, $alpha: 0.18);
    }
    &:active {
      background-color: rgba($color: #4288f5, $alpha: 0.26);
    }
  }

  &.primary-text {
    color: var(--color-primary-blue-02);

    &:hover,
    &:focus,
    &:active {
      color: var(--color-primary-blue-01);
    }
  }

  &:disabled {
    pointer-events: none;
    color: var(--icon-button-disabled-icon-color, rgba(0, 0, 0, 0.38));
  }
}

.icon-button-square {
  --icon-button-radius: 2px;
}

.icon-button-size-32 {
  --icon-button-size: 32px;
}

.icon-button-size-28 {
  --icon-button-size: 28px;
}

.icon-button-size-24 {
  --icon-button-size: 24px;
}

.icon-button-size-16 {
  --icon-button-size: 16px;
}

.icon-button-size-14 {
  --icon-button-size: 14px;
}

.icon-button-size-12 {
  --icon-button-size: 12px;
}

.basic-button {
  cursor: pointer;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  border: 0;
  outline: 0;
  padding: 0 8px;
  border-radius: var(--basic-button-radius, 4px);
  height: var(--basic-button-height, 36px);
  background-color: transparent;
  font-weight: var(--basic-button-font-weight, 500);
  display: inline-flex;
  justify-content: center;
  align-items: center;

  &:hover {
    background-color: rgba($color: #000, $alpha: 0.04);
  }
  &:focus {
    background-color: rgba($color: #000, $alpha: 0.12);
  }
  &:active {
    background-color: rgba($color: #000, $alpha: 0.2);
  }

  &.primary-color {
    color: #4288f5;

    &:hover {
      background-color: rgba($color: #4288f5, $alpha: 0.04);
    }
    &:focus {
      background-color: rgba($color: #4288f5, $alpha: 0.12);
    }
    &:active {
      background-color: rgba($color: #4288f5, $alpha: 0.2);
    }
  }

  &.primary-text {
    color: var(--color-primary-blue-02);

    &:hover,
    &:focus,
    &:active {
      color: var(--color-primary-blue-01);
    }
  }

  &:disabled {
    pointer-events: none;
    color: var(--basic-button-disabled-text-color, rgba(0, 0, 0, 0.38));
  }
}

.basic-button-radius-0 {
  --basic-button-radius: 0;
}

.basic-button-font-weight-700 {
  --basic-button-font-weight: 700;
}

.basic-button-height-28 {
  --basic-button-height: 28px;
}

.basic-button-height-auto {
  --basic-button-height: auto;
}

.flat-button {
  cursor: pointer;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  border: 0;
  outline: 0;
  padding: 0 16px;
  border-radius: var(--flat-button-radius, 2px);
  height: var(--flat-button-height, 36px);
  background-color: var(--flat-button-background-color, transparent);
  font-weight: var(--flat-button-font-weight, 500);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  &:hover {
    background-color: rgba($color: #000, $alpha: 0.04);
  }
  &:focus {
    background-color: rgba($color: #000, $alpha: 0.12);
  }
  &:active {
    background-color: rgba($color: #000, $alpha: 0.2);
  }

  &.primary-color {
    color: #fff;
    background-color: #4288f5;

    &:hover {
      background-color: rgba($color: #4288f5, $alpha: 0.96);
    }
    &:focus {
      background-color: rgba($color: #4288f5, $alpha: 0.88);
    }
    &:active {
      background-color: rgba($color: #4288f5, $alpha: 0.8);
    }
    &:disabled {
      color: var(--color-text-inverse);
      background-color: var(--legacy-primary-blue-01);
      opacity: 0.2;
    }
  }

  &.warn-color {
    color: #fff;
    background-color: #eb5858;

    &:hover {
      background-color: rgba($color: #eb5858, $alpha: 0.96);
    }
    &:focus {
      background-color: rgba($color: #eb5858, $alpha: 0.88);
    }
    &:active {
      background-color: rgba($color: #eb5858, $alpha: 0.8);
    }
  }

  &.accent-color {
    color: #fff;
    background-color: #35c699;

    &:hover {
      background-color: rgba($color: #35c699, $alpha: 0.96);
    }
    &:focus {
      background-color: rgba($color: #35c699, $alpha: 0.88);
    }
    &:active {
      background-color: rgba($color: #35c699, $alpha: 0.8);
    }
  }

  &.yellow-color {
    color: #fff;
    background-color: #fac639;

    &:hover {
      background-color: rgba($color: #fac639, $alpha: 0.96);
    }
    &:focus {
      background-color: rgba($color: #fac639, $alpha: 0.88);
    }
    &:active {
      background-color: rgba($color: #fac639, $alpha: 0.8);
    }
  }

  &.lightgray-color {
    color: #fff;
    background-color: #bfbfbf;

    &:hover {
      background-color: rgba($color: #bfbfbf, $alpha: 0.96);
    }
    &:focus {
      background-color: rgba($color: #bfbfbf, $alpha: 0.88);
    }
    &:active {
      background-color: rgba($color: #bfbfbf, $alpha: 0.8);
    }
  }

  &.gray-color {
    color: #fff;
    background-color: #3a464d;

    &:hover {
      background-color: rgba($color: #3a464d, $alpha: 0.96);
    }
    &:focus {
      background-color: rgba($color: #3a464d, $alpha: 0.88);
    }
    &:active {
      background-color: rgba($color: #3a464d, $alpha: 0.8);
    }
  }

  &.primary-text {
    color: var(--color-primary-blue-02);

    &:hover,
    &:focus,
    &:active {
      color: var(--color-primary-blue-01);
    }
  }

  &:disabled {
    pointer-events: none;
    color: var(--flat-button-disabled-text-color, rgba(0, 0, 0, 0.38));
    background-color: var(--flat-button-disabled-container-color, rgba(0, 0, 0, 0.12));
  }
}

.flat-button-height-32 {
  --flat-button-height: 32px;
}

.flat-button-height-24 {
  --flat-button-height: 24px;
}

.flat-button-height-16 {
  --flat-button-height: 16px;
}

.flat-button-height-auto {
  --flat-button-height: auto;
}

.stroked-button {
  cursor: pointer;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  border: 1px solid var(--stroked-button-border-color, rgba(0, 0, 0, 0.12));
  outline: 0;
  padding: 0 16px;
  border-radius: var(--stroked-button-radius, 4px);
  height: var(--stroked-button-height, 36px);
  background-color: transparent;
  font-weight: var(--stroked-button-font-weight, 500);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  &:hover {
    background-color: rgba($color: #000, $alpha: 0.04);
  }
  &:focus {
    background-color: rgba($color: #000, $alpha: 0.12);
  }
  &:active {
    background-color: rgba($color: #000, $alpha: 0.2);
  }

  &.primary-color {
    color: #4288f5;
    --stroked-button-border-color: #4288f5;

    &:hover {
      background-color: rgba($color: #4288f5, $alpha: 0.04);
    }
    &:focus {
      background-color: rgba($color: #4288f5, $alpha: 0.12);
    }
    &:active {
      background-color: rgba($color: #4288f5, $alpha: 0.2);
    }
  }

  &:disabled {
    pointer-events: none;
    color: var(--stroked-button-disabled-text-color, rgba(0, 0, 0, 0.38));
    border-color: var(--stroked-button-disabled-border-color, rgba(0, 0, 0, 0.12));
  }
}

.fab-button {
  cursor: pointer;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  display: inline-grid;
  place-items: center;
  border-radius: var(--fab-button-radius, 50%);
  width: var(--fab-button-size, 40px);
  height: var(--fab-button-size, 40px);
  border: 0;
  outline: 0;
  padding: 0;
  color: var(--fab-button-color, #fff);
  background-color: #3a464d;
  &:hover {
    background-color: rgba($color: #3a464d, $alpha: 0.96);
  }
  &:focus {
    background-color: rgba($color: #3a464d, $alpha: 0.88);
  }
  &:active {
    background-color: rgba($color: #3a464d, $alpha: 0.8);
  }

  &.primary-color {
    background-color: #4288f5;

    &:hover {
      background-color: rgba($color: #4288f5, $alpha: 0.96);
    }
    &:focus {
      background-color: rgba($color: #4288f5, $alpha: 0.88);
    }
    &:active {
      background-color: rgba($color: #4288f5, $alpha: 0.8);
    }
  }

  &:disabled {
    pointer-events: none;
    color: var(--fab-button-disabled-color, rgba(0, 0, 0, 0.38));
  }
}

.fab-button-size-20 {
  --fab-button-size: 20px;
}

.fab-button-square {
  --fab-button-radius: 3px;
}

.select-text-size-14 {
  --select-text-size: 14px;
}

.select-text-size-12 {
  --select-text-size: 12px;
}

.select-option-height-24 {
  --select-option-height: 24px;
}

.select-text-align-center {
  --select-text-align: center;
}

.menu-panel-container {
  display: grid;
  background-color: #fff;
  border-radius: 4px;
  overflow: auto;
  box-shadow: 0 5px 5px -3px #0003, 0 8px 10px 1px #00000024, 0 3px 14px 2px #0000001f;
  max-height: calc(100vh - 48px);
  max-width: 280px;
}

.menu-panel-button {
  --flat-button-radius: 0;
  --flat-button-font-weight: normal;
  justify-content: start;

  &[aria-expanded='true'] {
    background-color: rgba($color: #4288f5, $alpha: 0.08);
  }

  &:hover {
    background-color: rgba($color: #4288f5, $alpha: 0.04);
  }
  &:focus {
    background-color: rgba($color: #4288f5, $alpha: 0.12);
  }
  &:active {
    background-color: rgba($color: #4288f5, $alpha: 0.2);
  }
}

.circle-loading {
  display: inline-flex;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #a9caeb;
  border-top-color: #4288f5;
  animation: rotate-loading 1s infinite;
}

@keyframes rotate-loading {
  0% {
    transform: rotateZ(0deg);
  }
  100% {
    transform: rotateZ(360deg);
  }
}

.hyperlink-text,
a:any-link {
  color: var(--color-link-default);
  font-weight: 400;
  text-decoration: none;
  letter-spacing: 0.18px;

  &:hover {
    font-weight: 500;
    text-decoration: underline;
    letter-spacing: 0.08px;
  }
}
