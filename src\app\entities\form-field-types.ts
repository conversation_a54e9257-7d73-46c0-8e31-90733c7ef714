export enum FormFieldTypes {
  empty = 'Empty',
  text = 'Text',
  textBlur = 'TextBlur',
  sparkline = 'Sparkline',
  email = 'Email',
  tel = 'Tel',
  url = 'Url',
  hidden = 'Hidden',
  number = 'Number',
  decimal = 'Decimal',
  textarea = 'TextArea',
  dropdown = 'Dropdown',
  hclGroup = 'HclGroup',
  selection = 'Selection',
  tableSelect = 'TableSelect',
  checkbox = 'Checkbox',
  radio = 'Radio',
  radioGroup = 'RadioGroup',
  date = 'Date',
  time = 'Time',
  multipleDate = 'MultipleDate',
  datetime = 'Datetime',
  year = 'Year',
  image = 'Image',
  moreImage = 'MoreImage',
  color = 'Color',
  imageGallery = 'ImageGallery',
  actionIcon = 'Icon',
  label = 'Label',
  attach = 'Attach',
  attachList = 'AttachList',
  attachTable = 'AttachTable',
  fileTable = 'FileTable',
  dynamic = 'Dynamic',
  hyperlink = 'Hyperlink',
  hyperlinkText = 'HyperlinkText',
  hyperlinkButton = 'HyperlinkButton',
  treePathHyperlink = 'TreePathHyperlink',
  colorSizeQty = 'ColorSizeQty',
  inputSelect = 'InputSelect',
  seq = 'Seq',
  composite = 'Composite',
  button = 'Button',
  multiple = 'Multiple',
  groupSection = 'GroupSection',
  fieldGroup = 'FieldGroup',
  itemCheckbox = 'ItemCheckbox',
  commonCheckbox = 'commonCheckbox',
  moreAction = 'MoreAction',
  rowDrag = 'RowDrag',
  separator = 'Separator',
  numero = 'Numero',
  vendorAgreementTextArea = 'VendorAgreementTextArea',
  VendorAgreementDocStatus = 'VendorAgreementDocStatus',
  vendorAcceptanceStatus = 'VendorAcceptanceStatus',
  factAcceptanceStatus = 'FactAcceptanceStatus',
  productSelect = 'ProductSelect',
  VPOShipmentDetail = 'VPOShipmentDetail',
  IRMeasDetailColorsize = 'IRMeasDetailColorsize',
  IRMeasDetailSize = 'IRMeasDetailSize',
  groupRowDrag = 'GroupRowDrag',
  expansionGroup = 'ExpansionGroup',
  autoGroup = 'AutoGroup',
  sourceDropdown = 'SourceDropdown',
  OldUIHyperlink = 'OldUIHyperlink',
  FactoryAuditTemplateChecklistDefaultValue = 'FactoryAuditTemplateChecklistDefaultValue',
  FactoryAuditCheckList = 'FactoryAuditCheckList',
  password = 'Password',
  photo = 'Photo',
  tooltip = 'ToolTip',
  colorlist = 'Colorlist',
  deleteRow = 'deleteRow',
  footer = 'Footer',
  checklistField = 'ChecklistField',
  shareFileMix = 'shareFileMix',
  shareFileAttachTransform = 'shareFileAttachTransform',
  costSheetNo = 'VqCostSheetNo',
  NumberLimitless = 'NumberLimitless',
  sampleEvaluationMeasurement = 'SampleEvaluationMeasurement',
  hclNav = 'HclNav',
  breakdownYear = 'BreakdownYear',
  MapSelect = 'MapSelect',
  percentage = 'Percentage',
  vpoShipmentStatus = 'VpoShipmentStatus',
  counter = 'Counter',
  timeZoneDropdown = 'TimeZoneDropdown',
  textDropdown = 'TextDropdown',
  CustomTableSelect = 'CustomTableSelect',
  popupEntity = 'PopupEntity',
  columnLabel = 'ColumnLabel',
  dynamicSelection = 'DynamicSelection',
  requirementField = 'RequirementField',
  aggFunc = 'aggFunc',
  dynamicDropdown = 'DynamicDropdown',
  shareFileSelectList = 'ShareFileSelectList',
  shareFileList = 'shareFileList',
  DnbOrKharonSearch = 'DnbOrKharonSearch',
  ShowAdditionalInfo = 'ShowAdditionalInfo',
  factoryAuditWeighting = 'FactoryAuditWeighting',
}

export enum FormFieldFilterTypes {
  text = 'text',
  number = 'number',
  date = 'date',
  value = 'value',
  percentage = 'percentage',
  vpoShipmentStatus = 'VpoShipmentStatus',
  radio = 'Radio',
  requirementField = 'RequirementField',
  tags = 'Tags',
}

export const ENABLE_FILTER_TYPES = [
  FormFieldTypes.text,
  FormFieldTypes.email,
  FormFieldTypes.label,
  FormFieldTypes.tel,
  FormFieldTypes.url,
  FormFieldTypes.number,
  FormFieldTypes.decimal,
  FormFieldTypes.textarea,
  FormFieldTypes.dropdown,
  FormFieldTypes.hclGroup,
  FormFieldTypes.selection,
  FormFieldTypes.date,
  FormFieldTypes.datetime,
  FormFieldTypes.year,
  FormFieldTypes.hyperlink,
  // FormFieldTypes.actionLink,
  FormFieldTypes.hyperlinkText,
  FormFieldTypes.seq,
  FormFieldTypes.numero,
  FormFieldTypes.tableSelect,
  FormFieldTypes.photo,
  FormFieldTypes.checkbox,
  FormFieldTypes.tooltip,
  FormFieldTypes.requirementField,
  FormFieldTypes.radio,
];

export type FormFieldTypeMap<T> = Partial<{ [type in FormFieldTypes]: T }>;
