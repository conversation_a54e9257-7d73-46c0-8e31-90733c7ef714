import { Dialog } from '@angular/cdk/dialog';
import { Async<PERSON>ipe, NgIf } from '@angular/common';
import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';

import { TranslocoService, TRANSLOCO_SCOPE } from '@ngneat/transloco';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { AgGridModule } from 'ag-grid-angular';
import {
  ColDef,
  ExcelStyle,
  GridApi,
  GridOptions,
  GridReadyEvent,
  ICellRendererParams,
  IDetailCellRendererParams,
  IsRowMaster,
} from 'ag-grid-community';
import * as R from 'ramda';
import { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';
import { distinctUntilChanged, filter, first, map, shareReplay, switchMap, tap, take } from 'rxjs/operators';

import { defaultDialogConfig } from '../../../../config/dialog';
import { DocToolPanel } from '../../../../entities/form-field';
import { NavigationService } from '../../../../services/navigation.service';
import { TableSelectDialogComponent } from '../../../shared/common/table-select-dialog/table-select-dialog.component';
import { CascadeDropdownService } from '../../document/service/cascade-dropdown.service';
import { DocumentValueChangeService } from '../../document/service/document-value-change.service';
import { documentDatabaseProviders } from '../../document/state';
import { ToolPanelComponent } from '../../document/tool-panel/tool-panel.component';
import { ComparisonLabelRendererComponent } from '../cell-renderer/comparison-label-renderer/comparison-label-renderer.component';
import { SideBySideCellRendererComponent } from '../cell-renderer/side-by-side-cell-renderer/side-by-side-cell-renderer.component';
import { ComparisonHeaderComponent } from '../header/comparison-header/comparison-header.component';
import { ComparisonService } from '../service/comparison.service';
import { ComparisonDataQuery } from '../state/comparison-data.query';
import { ComparisonDataService } from '../state/comparison-data.service';
import { FinancialSummaryFieldDefine } from '../tool/financial-summary-tool/comparison-financial-summary-tool.component';
import { DocumentViewData } from 'src/app/entities';
import { AnyObject, NaviView, TableSelectDialogData, TableSelectDialogResult } from 'src/app/interface/model';
import { ComparisonSideBar } from 'src/app/interface/model/comparison';
import { GridComponents } from 'src/app/interface/model/grid';
import { AgGridLicenseLoaderService } from 'src/app/services/ag-grid-license/ag-grid-license-loader.service';
import { ConfigurationService } from 'src/app/services/configuration.service';
import { NotificationService } from 'src/app/services/notification.service';
import { deepClone } from 'src/app/utils';
import { isEmptyOrNil, isNotEmptyOrNil } from 'src/app/utils/general';
import { DomainAttributeService } from 'src/app/workspace/service/domain-attribute.service';
import { DOMAIN_ATTRIBUTES } from 'src/app/workspace/service/domain-attributes';

export enum LABEL_STYLE {
  SECTION = 'sectionLabel',
  SUB_SECTION = 'subSectionLabel',
  ITEM = 'itemLabel',
  SUB_ITEM = 'subItemLabel',
}

@UntilDestroy()
@Component({
  selector: 'app-what-if-comparison',
  templateUrl: './what-if-comparison.component.html',
  styleUrls: ['./what-if-comparison.component.scss'],
  providers: [
    documentDatabaseProviders,
    CascadeDropdownService,
    DocumentValueChangeService,
    { provide: [TRANSLOCO_SCOPE], useValue: 'comparison' },
  ],
  standalone: true,
  imports: [NgIf, AsyncPipe, AgGridModule, ToolPanelComponent],
})
export class WhatIfComparisonComponent implements OnInit {
  agGridLicenseLoader = inject(AgGridLicenseLoaderService);

  components: GridComponents = {
    cellRenderer: SideBySideCellRendererComponent,
    labelRenderer: ComparisonLabelRendererComponent,
    comparisonHeaderComponent: ComparisonHeaderComponent,
  };

  COLUMN_NAME_LABELS = 'label';

  panels$ = new BehaviorSubject<DocToolPanel[]>(null);

  // DEFAULT_WIDTH = 513;
  DEFAULT_WIDTH = 212;

  gridApi: GridApi;

  isProcessing$ = new BehaviorSubject(false);

  gridOptions: GridOptions = {
    context: {
      componentParent: this,
    },
  };

  private readonly _whatIfParams = new Map<string, boolean>();

  comCostDtlGridIdx: number[] = [];

  navigationService = inject(NavigationService);

  @Input()
  moduleId: string;

  @Input()
  refNos: string[];

  refNos$ = new BehaviorSubject<string[]>([]); // store orignal refNos or updated refNos
  isSigleVqMode: boolean;
  addRefNos = []; // just used when isSigleVqMode
  removeRefNos = []; // just used when isSigleVqMode
  needRefresh$ = new BehaviorSubject(true);

  @Input()
  costSheetRef: string;

  @Input()
  itemRefNo: string;

  @Input()
  formDialog: boolean;

  @Output() gridReady = new EventEmitter<GridReadyEvent>();
  @Output() documents = new EventEmitter<DocumentViewData[]>();

  hyperlinkFieldTypes = {
    vqNo: true,
    vendor: true,
    factory: true,
    itemNo: true,
    'costSheet.refNo': true,
  };

  defaultColDef: ColDef = {
    width: this.DEFAULT_WIDTH,
    resizable: true,
    autoHeight: true,
    cellClass: 'cbx-comparison-field-col',
    headerComponent: 'comparisonHeaderComponent',
    cellClassRules: {
      gray01Background: (params) => !params.data.isField,
      hyperlink: (params) =>
        params.colDef.headerName && this.hyperlinkFieldTypes[params.data.field?.replace('entity.', '')],
    },
  };

  moduleLabel = 'Quotation';

  naviView$ = new BehaviorSubject<NaviView>(null);
  searchCondition$ = new BehaviorSubject<any>(null);

  naviTabId$ = this.navigationService.tab$.pipe(
    filter((tab) => !!tab),
    map(({ id }) => id),
  );

  documents$: Observable<DocumentViewData[]>;

  selectedTemplate$: Observable<DocumentViewData>;

  selectedTemplate: DocumentViewData;

  fieldLabelsMap = new Map();

  colDefs$: Observable<ColDef[]>;

  sideBar$: Observable<ComparisonSideBar[]>;

  pinnedTopSideBar$: Observable<ComparisonSideBar[]>;

  pinnedTopSideBarData: ComparisonSideBar[];

  pinnedBottomSideBar$: Observable<ComparisonSideBar[]>;

  rowClassRules = {
    'cbx-comparison-section-background': (params: any) => !params.data.isField,
    'cbx-comparison-summary-background': (params: any) => params.data.category === 'summary',
  };

  headerFields = ['', 'Original', 'What-if', 'Difference'];

  // merge LIDL-15442 to product
  // Since these calculated fields belong to our current project,product does not have these fields, so the product is hard coded
  fields = [
    {
      label: 'Total Supplier Turnover (USD)',
      valueOriginal: 6,
      valueWhatIf: 6,
      valueDiff: '-',
      decimalplaces: 0,
    } as FinancialSummaryFieldDefine,
    {
      label: 'Total Supplier Turnover (EUR)',
      valueOriginal: 2294751,
      valueWhatIf: 2289932,
      valueDiff: -4819,
      decimalplaces: 0,
    } as FinancialSummaryFieldDefine,
    {
      label: 'Total Customer Turnover (EUR)',
      valueOriginal: 2150739,
      valueWhatIf: 2146232,
      valueDiff: -4507,
      decimalplaces: 0,
    } as FinancialSummaryFieldDefine,
    {
      label: 'Total Margin %',
      valueOriginal: 2331267.61,
      valueWhatIf: 2331267.61,
      valueDiff: '-',
      decimalplaces: 2,
    } as FinancialSummaryFieldDefine,
    {
      label: 'Total Gross Margin',
      valueOriginal: 2180384,
      valueWhatIf: 2180384,
      valueDiff: '-',
      decimalplaces: 0,
    } as FinancialSummaryFieldDefine,
    {
      label: 'Total Order Quantity',
      valueOriginal: 4600,
      valueWhatIf: 3200,
      valueDiff: 1400,
    } as FinancialSummaryFieldDefine,
  ];

  constructor(
    private readonly comparisonService: ComparisonService,
    private readonly comparisonDataService: ComparisonDataService,
    private readonly comparisonDataQuery: ComparisonDataQuery,
    private readonly configurationService: ConfigurationService,
    private readonly translocoService: TranslocoService,
    private readonly notificationService: NotificationService,
    private readonly dialog: Dialog,
    private readonly domainAttributeService: DomainAttributeService,
  ) {}

  ngOnInit() {
    this.init();
  }

  private readonly booleanValueMap = new Set(['yes', 'true']);

  showPanels: boolean = this.handlePanelsVisible();

  private handlePanelsVisible(): boolean {
    const enabled = this.domainAttributeService.state[DOMAIN_ATTRIBUTES.UI_COMPARE_QUOTATIONS_FINANCIALSUMMARY_ENABLED];
    if (isEmptyOrNil(enabled)) {
      return false;
    }

    return this.booleanValueMap.has(enabled.toLowerCase());
  }

  init() {
    this.isProcessing$.next(true);
    this.refNos$.next(Array.from(new Set(this.refNos)));
    this.isSigleVqMode = this.refNos?.length === 1;
    if (this.refNos?.length >= 2) {
      this.panels$.next([
        {
          id: 'comparisonFinancialSummary',
          label: 'Financial Summary',
        } as DocToolPanel,
      ]);
    }

    this.documents$ = this.needRefresh$.pipe(
      filter((needRefresh) => needRefresh),
      switchMap(() => {
        const refNos = this.isSigleVqMode ? Array.from(new Set(this.refNos)) : this.refNos$.getValue();
        const addRefNos = this.isSigleVqMode ? this.addRefNos : [];
        const removeRefNos = this.isSigleVqMode ? this.removeRefNos : [];
        return this.comparisonService
          .loadDocuments(this.moduleId, refNos, this.itemRefNo, this.isSigleVqMode, addRefNos, removeRefNos)
          .pipe(
            tap((documents) => {
              let index = 0;
              documents.forEach((each) => {
                // hardcode fake AI data for demo
                // const unitCost = each.unitCost ? each.unitCost : 0;
                if (index === 1) {
                  each.ai = '92.33%Rejected to Buy';
                } else if (index === 2) {
                  each.ai = '93.58%Rejected to Buy';
                } else if (index === 3) {
                  each.ai = '93.89%Confirmed to Buy';
                } else {
                  each.ai = '95.14%Confirmed to Buy';
                }
                index += 1;
              });
              this.buildSearchCondition(documents);
              this.buildBookmarkName(documents);
            }),
          );
      }),
      untilDestroyed(this),
      shareReplay(1),
    );

    this.selectedTemplate$ = this.comparisonService.loadTemplates(this.moduleId).pipe(
      distinctUntilChanged(),
      map((templates) => templates.find((template) => template.isDefault) || templates[0]),
      tap((selectedTemplate) => {
        this.selectedTemplate = selectedTemplate;
      }),
      untilDestroyed(this),
      shareReplay(1),
    );

    this.colDefs$ = combineLatest([this.documents$, this.selectedTemplate$]).pipe(
      map(([documents, selectedTemplate]) => {
        let defaultCellRenderer = 'labelRenderer';

        if (
          selectedTemplate.compTemplSectionDetailList.some((templDtl) =>
            ['entity.componentCost', 'entity.additionalCost'].includes(templDtl.field),
          )
        ) {
          defaultCellRenderer = 'agGroupCellRenderer';
        }

        const columnDefs = [];
        const labelCol = {
          field: this.COLUMN_NAME_LABELS,
          width: 180,
          cellRenderer: defaultCellRenderer,
          headerName: '',
          cellClass: 'cbx-comparison-label-col',
          pinned: 'left',
          lockPosition: true,
        };
        columnDefs.push(labelCol);

        this.comparisonDataService.reset();

        this.setDocuments(documents, selectedTemplate);

        documents.forEach((document) => {
          const docCol = { field: document.id, headerName: document.refNo, cellRenderer: 'cellRenderer' };
          const emptyCol = {
            headerClass: 'invisible',
            cellClass: 'invisible',
            hide: true,
          };
          columnDefs.push(docCol);
          columnDefs.push(emptyCol);
          columnDefs.push(emptyCol);
          columnDefs.push(emptyCol);
          columnDefs.push(emptyCol);
          columnDefs.push(emptyCol);
        });
        this.documents.emit(documents);
        return columnDefs;
      }),
      untilDestroyed(this),
      shareReplay(1),
    );

    this.sideBar$ = combineLatest([this.colDefs$, this.selectedTemplate$]).pipe(
      map(([, selectedTemplate]) => this.getFieldTemplates(selectedTemplate)),
    );

    this.pinnedTopSideBar$ = combineLatest([this.colDefs$, this.selectedTemplate$]).pipe(
      map(([, selectedTemplate]) => {
        this.pinnedTopSideBarData = this.getFieldTemplates(selectedTemplate, true);
        return this.pinnedTopSideBarData;
      }),
      tap(() => this.isProcessing$.next(false)),
    );

    this.pinnedBottomSideBar$ = combineLatest([
      this.translocoService.selectTranslate('comparison.aIRecommendation'),
      this.selectedTemplate$,
    ]).pipe(
      map(([aiRecommendationLabel, { enableAi }]) => {
        const pinnedBottomSideBar: ComparisonSideBar[] = [
          {
            label: aiRecommendationLabel,
            seq: 0,
            sectionSequence: 0,
            isField: true,
            labelStyle: LABEL_STYLE.SUB_SECTION,
            isWhatIfFields: true,
            field: 'entity.ai',
            category: 'ai',
          },
        ];
        return enableAi ? pinnedBottomSideBar : [];
      }),
    );
  }

  // loadDocuments() {
  //   this.documents$ = this.comparisonService.loadDocuments(this.moduleId, this.refNos, this.itemRefNo).pipe(
  //     tap((documents) => {
  //       let index = 0;
  //       documents.forEach((each) => {
  //         // hardcode fake AI data for demo
  //         // const unitCost = each.unitCost ? each.unitCost : 0;
  //         if (index === 1) {
  //           each.ai = '92.33%Rejected to Buy';
  //         } else if (index === 2) {
  //           each.ai = '93.58%Rejected to Buy';
  //         } else if (index === 3) {
  //           each.ai = '93.89%Confirmed to Buy';
  //         } else {
  //           each.ai = '95.14%Confirmed to Buy';
  //         }
  //         index += 1;
  //       });
  //     }),
  //     untilDestroyed(this),
  //     shareReplay(1),
  //   );
  // }

  reLoadDocument(refNo: string) {
    this.comparisonService
      .loadDocuments(this.moduleId, Array.from(new Set([refNo])))
      .pipe(untilDestroyed(this))
      .subscribe({
        next: (documents) => {
          documents.forEach((document) => {
            // fake AI data
            document.ai = '92.33%Rejected to Buy';
            if (document.refNo === refNo) {
              this.setDocument(document, this.selectedTemplate);
            } else {
              this.getDefaultCostSheet(document, this.selectedTemplate, this.costSheetRef);
              if (document.vqUnitCostDetailList && document.vqUnitCostDetailList.length > 0) {
                [document.vqUnitCostDetail] = document.vqUnitCostDetailList;
              }
              this.setDefaultVqCarton(document, this.selectedTemplate);
              this.comparisonDataService.resetByDoc(document);
            }
          });
          this.isProcessing$.next(false);
        },
        error: (error) => {
          this.showError('Unexpected Error!');
          console.error(error || 'Error happened without message.');
          this.isProcessing$.next(false);
        },
      });
  }

  setDocuments(documents: DocumentViewData[], selectedTemplate: any) {
    this.comparisonDataService.reset();
    documents.forEach((document) => {
      this.setDocument(document, selectedTemplate);
    });
  }

  setDocument(document: DocumentViewData, selectedTemplate?: any) {
    const template = selectedTemplate;
    this.comparisonDataService.remove(document.id);
    const defaultCostSheet = this.getDefaultCostSheet(document, template, this.costSheetRef);
    if (document.vqUnitCostDetailList && document.vqUnitCostDetailList.length > 0) {
      [document.vqUnitCostDetails] = document.vqUnitCostDetailList;
    }
    this.setDefaultVqCarton(document, template);
    this.comparisonDataService.initStoreData({ ...document } as DocumentViewData);
    if (defaultCostSheet) {
      this.loadDefaultWhatIfData(document.refNo, defaultCostSheet.refNo, `${document.id}-wi`);
      // only set the params for the vq that has costSheet.
      this.setWhatIfParams(document.id, false);
    }

    const updateData = this.handleComponentAndAdditionalDataTransform(document);

    this.comparisonDataService.updateData(document.id, updateData);
  }

  private handleComponentAndAdditionalDataTransform(document: DocumentViewData) {
    // For CBX9-3746 enrich logic

    let customComponentCostListData = {};
    let customAdditionalCostListData = {};
    let customCompCostDtlData = {};
    let customAddiCostDtlData = {};

    if (isNotEmptyOrNil(document.vqComponentCostList)) {
      customComponentCostListData = this.comparisonService.handleComponentCostListData(document.vqComponentCostList);
      customCompCostDtlData = this.comparisonService.handleCompCostListDataForDtlGrid(document.vqComponentCostList);
    }

    if (isNotEmptyOrNil(document.vqAdditionalCostList)) {
      customAdditionalCostListData = this.comparisonService.handleAdditionalCostListData(document.vqAdditionalCostList);
      customAddiCostDtlData = this.comparisonService.handleAddiCostListDataForDtlGrid(document.vqAdditionalCostList);
    }

    return {
      ...document,
      ...customComponentCostListData,
      ...customAdditionalCostListData,
      ...customCompCostDtlData,
      ...customAddiCostDtlData,
    };
  }

  getFieldTemplates(selectedTemplate: DocumentViewData, frozenOnTop: boolean = false) {
    const fieldTemplates: AnyObject<any>[] = [];
    if (frozenOnTop) {
      fieldTemplates.push(
        {
          label: '',
          seq: 0,
          sectionSequence: 0,
          isField: true,
          field: 'entity.vqNo',
          category: 'summary',
        },
        {
          label: '',
          seq: 0,
          sectionSequence: 0,
          isField: true,
          field: 'toggle',
          category: 'whatIfToggle',
        },
      );
    }

    const colDefHandlerMap = {
      componentCost: this.handleCompCostColDefForDtlGrid.bind(this),
      additionalCost: this.handleAddiCostColDefForDtlGrid.bind(this),
      totalComponentCosts: this.handleComponentCostListColDef.bind(this),
      totalAdditionalCosts: this.handleAdditionalCostListColDef.bind(this),
      totalCost: this.handleTotalUnitCostColDef.bind(this),
    };

    let maxSectionSequence = 0;
    selectedTemplate.compTemplSectionList.forEach((section) => {
      if (frozenOnTop === section.frozenOnTop) {
        const sequence = +section.sequence;
        if (maxSectionSequence < sequence) {
          maxSectionSequence = sequence;
        }
        fieldTemplates.push({
          label: section.sectionName,
          seq: 0,
          sectionSequence: sequence,
          isField: false,
          labelStyle: LABEL_STYLE.SECTION,
          frozenOnTop,
        });

        selectedTemplate.compTemplSectionDetailList
          .filter((fieldDetail) => fieldDetail.compTemplSection.sectionName === section.sectionName)
          .forEach((fieldDetail) => {
            if (
              !R.startsWith('entity.additionalField', fieldDetail.field) &&
              fieldDetail.compTemplSection.sequence === sequence
            ) {
              fieldDetail.sectionSequence = sequence;
              fieldDetail.isField = true;
              fieldDetail.frozenOnTop = frozenOnTop;

              let handleColDef;
              if (fieldDetail.field === 'entity.componentCost') {
                handleColDef = colDefHandlerMap.componentCost;
              } else if (fieldDetail.field === 'entity.additionalCost') {
                handleColDef = colDefHandlerMap.additionalCost;
              } else {
                handleColDef = colDefHandlerMap[fieldDetail.fieldPath?.fieldId];
              }

              if (handleColDef) {
                fieldTemplates.push(...handleColDef(fieldDetail, sequence, frozenOnTop, selectedTemplate));
              } else {
                fieldTemplates.push(fieldDetail);
              }

              this.fieldLabelsMap.set(fieldDetail.field, fieldDetail.label);
            }
          });
      }
    });

    fieldTemplates.sort((fieldTemplate1, fieldTemplate2) => {
      if (fieldTemplate1.sectionSequence === fieldTemplate2.sectionSequence) {
        return fieldTemplate1.seq - fieldTemplate2.seq;
      }
      return fieldTemplate1.sectionSequence - fieldTemplate2.sectionSequence;
    });

    return fieldTemplates as ComparisonSideBar[];
  }

  private handleComponentCostListColDef(fieldDetail: any, sequence: number, frozenOnTop: boolean) {
    if (fieldDetail.compTemplSection.sectionName !== 'Open Costing') {
      return [fieldDetail];
    }

    const data = Object.values(this.comparisonDataQuery.getValue().entities);
    const colDefList = this.comparisonService.getCustomComponentCostDefList(data);
    const generalInfo = {
      sectionSequence: sequence,
      isField: true,
      mapping: 'vqComponentCostList',
      labelStyle: LABEL_STYLE.SUB_ITEM,
      frozenOnTop,
    };

    const defList: any[] = [];

    const totalComponentCostsColDef = {
      ...fieldDetail,
      label: 'Main Material',
      labelStyle: LABEL_STYLE.SUB_SECTION,
    };

    defList.push(totalComponentCostsColDef);

    colDefList.forEach((_, index) => {
      const seq = index + 1;

      defList.push({
        ...generalInfo,
        label: `Material ${seq}`,
        field: `material${seq}`,
        labelStyle: LABEL_STYLE.ITEM,
      });

      const materialInfoArray = [
        { ...generalInfo, label: 'Name', field: `name${seq}` },
        { ...generalInfo, label: 'Type', field: `type${seq}` },
        { ...generalInfo, label: 'Cost', field: `cost${seq}` },
      ];

      defList.push(...materialInfoArray);
    });

    return defList;
  }

  private handleAdditionalCostListColDef(fieldDetail: any, sequence: number, frozenOnTop: boolean) {
    if (fieldDetail.compTemplSection.sectionName !== 'Open Costing') {
      return [fieldDetail];
    }

    const vqAdditionalCostList = 'vqAdditionalCostList';
    const colDefList = this.getCustomAdditionalCostDefList();

    const defList: any[] = [];

    const totalAdditionalCostsColDef = {
      ...fieldDetail,
      label: 'Additional Cost',
      labelStyle: LABEL_STYLE.SUB_SECTION,
    };

    defList.push(totalAdditionalCostsColDef);

    colDefList.forEach((subSection) => {
      const additionCostType = Object.keys(subSection)[0];

      defList.push({
        label: additionCostType,
        field: additionCostType,
        sectionSequence: sequence,
        isField: true,
        mapping: vqAdditionalCostList,
        labelStyle: LABEL_STYLE.SUB_ITEM,
        frozenOnTop,
      });
    });

    return defList;
  }

  private getCustomAdditionalCostDefList() {
    const colDefList: any[] = [];

    Object.values(this.comparisonDataQuery.getValue().entities).forEach((entity) =>
      (entity.vqAdditionalCostList as any[]).map((additionalCost) => {
        const { type, description } = additionalCost;
        const typeName = type?.name || '';

        const sameTypeIndex = colDefList.findIndex((colDef) => colDef[typeName]);
        const hasSameType = sameTypeIndex >= 0;
        const hasSameDescription = colDefList[sameTypeIndex]?.[typeName]?.some((desc) => desc === description);

        if (hasSameType && hasSameDescription) {
          return null;
        }

        if (hasSameType) {
          colDefList[sameTypeIndex][typeName]?.push(description);
          return colDefList;
        }

        colDefList.push({ [typeName]: [description] });
        return colDefList;
      }),
    );

    return colDefList;
  }

  private handleTotalUnitCostColDef(fieldDetail: any) {
    return [{ ...fieldDetail, labelStyle: LABEL_STYLE.SUB_SECTION }];
  }

  getDefaultCostSheet(document: DocumentViewData, selectedTemplate: DocumentViewData, costSheetRef: string) {
    const { countryOfDestination } = selectedTemplate;
    const { portOfDischarge } = selectedTemplate;
    let defaultCostSheet;
    if (document.vqCostSummaryList && document.vqCostSummaryList.length > 0) {
      document.vqCostSummaryList.forEach((costSheet) => {
        if (
          (costSheetRef && costSheet.refNo === costSheetRef) ||
          (!costSheetRef &&
            !defaultCostSheet &&
            costSheet.countryOfDestination &&
            costSheet.portOfDischarge &&
            costSheet.countryOfDestination.code === countryOfDestination &&
            costSheet.portOfDischarge.code === portOfDischarge)
        ) {
          defaultCostSheet = costSheet;
        }
      });
      if (!defaultCostSheet) {
        [defaultCostSheet] = document.vqCostSummaryList;
      }
    }
    if (defaultCostSheet) {
      document.costSheet = defaultCostSheet;
      document.costId = defaultCostSheet.id;
      document.costRefNo = defaultCostSheet.refNo;
    }
    return defaultCostSheet;
  }

  setDefaultVqCarton(document: DocumentViewData, selectedTemplate: DocumentViewData) {
    const { cartonType } = selectedTemplate;
    let defaultCarton;
    if (document.vqCartonList && document.vqCartonList.length > 0) {
      document.vqCartonList.forEach((carton) => {
        if (carton.cartonType && carton.cartonType.code === cartonType) {
          defaultCarton = carton;
        }
      });
      if (!defaultCarton) {
        [defaultCarton] = document.vqCartonList;
      }
    }
    if (defaultCarton) {
      document.defaultCarton = defaultCarton;
    }
    return defaultCarton;
  }

  onOpenWhatIf(params: any): void {
    this.isProcessing$.next(true);
    setTimeout(() => {
      const targetColId = params.colDef.field;
      this.setWhatIfParams(targetColId, params.enableWhatIf);

      this.gridApi.setGridOption('pinnedTopRowData', this.pinnedTopSideBarData);
      this.gridApi.refreshCells({ columns: [targetColId], force: true });
      this.gridApi.resetRowHeights();
      this.isProcessing$.next(false);
    }, 10);
  }

  setWhatIfParams(docId: string, enable: boolean): void {
    this._whatIfParams.set(docId, enable);
  }

  getWhatIfParams(docId: string): boolean {
    return this._whatIfParams.get(docId);
  }

  calculateWhatIf(params: any) {
    this.isProcessing$.next(true);
    const docId = params.colDef.field;
    const wiDocId = `${docId}-wi`;
    const whatIfDoc$ = this.comparisonDataQuery.selectEntity(wiDocId);
    const originalDoc$ = this.comparisonDataQuery.selectEntity(docId);
    combineLatest([whatIfDoc$, originalDoc$])
      .pipe(
        first(),
        switchMap(([whatIfDoc, originalDoc]) => {
          const data: any = {};
          data.id = originalDoc.id;
          data.refNo = originalDoc.refNo;
          const originalCostSheet = originalDoc.costSheet;
          const whatIfCostSheet = whatIfDoc.costSheet;
          const csData: any = {};
          csData.id = originalCostSheet.id;
          csData.refNo = originalCostSheet.refNo;
          const originalCarton = originalDoc.defaultCarton;
          const whatIfCarton = whatIfDoc.defaultCarton;
          const cartonData: any = {};
          cartonData.id = originalCarton?.id;
          cartonData.refNo = originalCarton?.refNo;
          const { elementList } = originalCostSheet;
          const csEleList = [];
          elementList.forEach((costElement) => {
            csEleList.push({ ...costElement });
          });
          csData.elementList = csEleList;

          this.setNotNullCostElementValue(csData, whatIfCostSheet, 'elementList.UNITCOST.exRateFixed');
          this.selectedTemplate.compTemplSectionDetailList.forEach((fieldTemplate) => {
            if (fieldTemplate.isWhatIfFields) {
              const fieldPath: string = fieldTemplate.field;
              const fieldId = fieldPath.substring(7);
              if (R.startsWith('costSheet.', fieldId)) {
                const costFieldId = fieldId.substring(10);
                if (fieldTemplate.editable) {
                  this.setNotNullValue(csData, whatIfCostSheet, costFieldId);
                } else {
                  this.setNotNullValue(csData, originalCostSheet, costFieldId);
                }
              } else if (originalCarton && R.startsWith('defaultCarton.', fieldId)) {
                const ctFieldId = fieldId.substring(14);
                if (fieldTemplate.editable) {
                  this.setNotNullValue(cartonData, whatIfCarton, ctFieldId);
                } else {
                  this.setNotNullValue(cartonData, originalCarton, ctFieldId);
                }
              } else if (fieldTemplate.editable) {
                this.setNotNullValue(data, whatIfDoc, fieldId);
              } else {
                this.setNotNullValue(data, originalDoc, fieldId);
              }
            }
          });
          let unitCost: number = whatIfDoc.unitCost ? whatIfDoc.unitCost : originalDoc.unitCost;
          let totalOtherCharges: number = whatIfDoc.totalOtherCharges
            ? whatIfDoc.totalOtherCharges
            : originalDoc.totalOtherCharges;
          if (!unitCost) {
            unitCost = 0;
          }
          if (!totalOtherCharges) {
            totalOtherCharges = 0;
          }
          const orgTotalCost = originalDoc.totalCost;
          const wiTotalCost = whatIfDoc.totalCost;
          if (orgTotalCost === wiTotalCost) {
            const totalCost = unitCost + totalOtherCharges;
            data.totalCost = totalCost;
          }
          data.vqCostSummaryList = [csData];
          if (originalCarton) {
            data.vqCartonList = [cartonData];
          }

          return this.comparisonService.calculateWhatIf(data, 'vq').pipe(
            map((returnData) => {
              const originalUnitCost = originalDoc.unitCost ? originalDoc.unitCost + 1 : 0;
              returnData.ai = this.calculateAiStatusScore(unitCost, originalUnitCost);
              return returnData;
            }),
          );
        }),
      )
      .subscribe({
        next: (calculatedDoc) => {
          const costSheet = calculatedDoc.vqCostSummaryList[0];
          calculatedDoc.costSheet = costSheet;
          const defaultCarton = calculatedDoc.vqCartonList[0];
          calculatedDoc.defaultCarton = defaultCarton;
          this.comparisonDataService.updateData(wiDocId, { ...calculatedDoc, id: wiDocId });
          this.isProcessing$.next(false);
          this.showSuccessMsg('Document successfully calculate!');
        },
        error: (error) => {
          this.showError('Unexpected Error!');
          console.warn(error || 'Error happened without message.');
          this.isProcessing$.next(false);
        },
      });
  }

  calculateAiStatusScore(unitCost: number, originalUnitCostIn?: number): string {
    const originalUnitCost = originalUnitCostIn || unitCost;
    const offset = 5;
    if (unitCost <= originalUnitCost) {
      const score = 100 - offset + ((originalUnitCost - unitCost) / originalUnitCost) * offset;
      return `${score.toFixed(2)}%Confirmed to Buy`;
    }
    if (unitCost > originalUnitCost) {
      const score = 100 - offset + ((unitCost - originalUnitCost) / unitCost) * offset;
      return `${score.toFixed(2)}%Rejected to Buy`;
    }
    return null;
  }

  private setNotNullValue(tagetDoc: any, srcDoc: any, key: string) {
    if (R.startsWith('cust', key)) {
      let custFieldId = key.replace('cust', 'customField');
      custFieldId = custFieldId.replace('Codelist', 'CodeList');
      const keys = ['customFields', custFieldId];
      if (!tagetDoc.customFields) {
        tagetDoc.customFields = {};
      }
      if (!R.isNil(R.path(keys)(srcDoc))) {
        tagetDoc.customFields[custFieldId] = R.path(keys)(srcDoc);
      }
    } else if (!R.isNil(srcDoc[key])) {
      tagetDoc[key] = srcDoc[key];
    }
  }

  private setNotNullCostElementValue(tagetCostSheet: any, srcCostSheet: any, key: string) {
    let elementDesCode = key.substring(12);
    let eleFieldId = 'cost';
    if (R.includes('.', elementDesCode)) {
      eleFieldId = elementDesCode.substring(elementDesCode.indexOf('.') + 1);
      elementDesCode = elementDesCode.substring(0, elementDesCode.indexOf('.'));
    }
    const srcValue = this.getCostElementValue(key, srcCostSheet);
    if (srcValue) {
      let targetEle = null;
      tagetCostSheet.elementList.forEach((costElement) => {
        if (costElement.description.code === elementDesCode) {
          targetEle = costElement;
        }
      });
      targetEle[eleFieldId] = srcValue;
    }
  }

  resetWhatIf(params: any) {
    const docId = params.colDef.field;
    const wiDocId = `${docId}-wi`;
    const originalDoc$ = this.comparisonDataQuery.selectEntity(docId);
    originalDoc$.pipe(first(), untilDestroyed(this)).subscribe((originalDoc) => {
      this.comparisonDataService.resetByDoc(deepClone({ ...originalDoc, id: wiDocId }));
    });
    this.showSuccessMsg('What if data reset!');
  }

  loadDefaultWhatIfData(refNo: string, costRefNo: string, wiDocId: string) {
    this.comparisonService
      .loadWhatIfData(refNo, costRefNo)
      .pipe(
        filter((whatIfData) => isNotEmptyOrNil(whatIfData?.refNo)),
        untilDestroyed(this),
      )
      .subscribe((whatIfData) => {
        const whatIfCostSheet = whatIfData.vqCostSummaryList[0];
        let defaultCarton;
        if (whatIfData?.vqCartonList?.length > 0) {
          [defaultCarton] = whatIfData.vqCartonList;
        }

        this.selectedTemplate.compTemplSectionDetailList.forEach((fieldTemplate) => {
          const fieldPath: string = fieldTemplate.field;
          const fieldId = fieldPath.substring(7);
          let value;
          if (R.includes('.elementList.', fieldId)) {
            value = this.getCostElementValue(fieldId, whatIfCostSheet);
          } else if (fieldTemplate.isWhatIfFields) {
            if (R.startsWith('costSheet.', fieldId) || (defaultCarton && R.startsWith('defaultCarton.', fieldId))) {
              const isCostSheet = R.startsWith('costSheet.', fieldId);
              const doc = isCostSheet ? whatIfCostSheet : defaultCarton;
              value = this.getValue(fieldId.split('.')[1], doc);
            } else {
              value = this.getValue(fieldId, whatIfData);
            }
          }
          this.comparisonDataService.updateFieldValue(wiDocId, fieldId, value);
        });
      });
  }

  getValue(fieldId: string, doc: any) {
    if (R.startsWith('cust', fieldId)) {
      let custFieldId = fieldId.replace('cust', 'customField');
      custFieldId = custFieldId.replace('Codelist', 'CodeList');
      const keys = ['customFields', custFieldId];
      return R.path(keys)(doc);
    }
    return doc[fieldId];
  }

  private getCostElementValue(fieldId: string, whatIfCostSheet: any): any {
    let elementDesCode = fieldId.substring(fieldId.indexOf('elementList.') + 12);
    let eleFieldId = 'cost';
    if (R.includes('.', elementDesCode)) {
      eleFieldId = elementDesCode.substring(elementDesCode.indexOf('.') + 1);
      elementDesCode = elementDesCode.substring(0, elementDesCode.indexOf('.'));
    }
    const { elementList } = whatIfCostSheet;
    let value = null;
    if (elementList) {
      elementList.forEach((costElement) => {
        if (costElement?.description?.code === elementDesCode) {
          value = costElement[eleFieldId];
        }
      });
    }
    return value;
  }

  addQuotation() {
    this.isProcessing$.next(true);
    this.dialog
      .open<TableSelectDialogResult, TableSelectDialogData, TableSelectDialogComponent>(TableSelectDialogComponent, {
        ...defaultDialogConfig,
        maxWidth: '960px',
        width: '80vw',
        data: {
          rowSelection: 'multiple',
          viewId: 'popMoreQuotationView',
          title: 'Quotation',
          moduleId: 'vq',
        },
      })
      .closed.pipe(
        tap(() => this.isProcessing$.next(false)),
        take(1),
        filter((result) => result?.type === 'done'),
        filter((result) => !!result.payload?.length),
      )
      .subscribe((data) => {
        const addedVqRefNos = [];
        data.payload.forEach((vq) => {
          const addedVqRefNo = vq?.vqNo;
          addedVqRefNos.push(addedVqRefNo);
        });
        this.gridApi.setGridOption('loading', true);
        // refresh page
        if (this.isSigleVqMode) {
          this.addRefNos.push(...addedVqRefNos);

          const addRefNoSet = new Set(addedVqRefNos);
          const removeRefNoSet = new Set(this.removeRefNos);

          this.removeRefNos = this.removeRefNos.filter((refNo) => !addRefNoSet.has(refNo));
          this.addRefNos = this.addRefNos.filter((refNo) => !removeRefNoSet.has(refNo));
          console.info(`addQuotation -> removeRefNos: ${this.removeRefNos}`);
          console.info(`addQuotation -> addRefNos: ${this.addRefNos}`);
        } else {
          this.refNos$.next(Array.from(new Set([...this.refNos$.getValue(), ...addedVqRefNos])));
        }
        this.needRefresh$.next(true);
      });
  }

  removeDocument(removeRefNo: string) {
    this.isProcessing$.next(true);
    this.gridApi.setGridOption('loading', true);
    // refresh page
    if (this.isSigleVqMode) {
      this.removeRefNos = [...this.removeRefNos, removeRefNo];

      const removeRefNoSet = new Set(this.removeRefNos);
      const addRefNoSet = new Set(this.addRefNos);

      this.removeRefNos = this.removeRefNos.filter((refNo) => !addRefNoSet.has(refNo));
      this.addRefNos = this.addRefNos.filter((refNo) => !removeRefNoSet.has(refNo));
    } else {
      this.refNos$.next(Array.from(new Set(this.refNos$.getValue().filter((refNo) => refNo !== removeRefNo))));
    }
    this.needRefresh$.next(true);
    this.isProcessing$.next(false);
  }

  xhrCall$(url: string) {
    return new Observable((observer) => {
      const xhr = new XMLHttpRequest();
      xhr.onreadystatechange = () => {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            observer.next(xhr.response);
          } else {
            observer.error(xhr.status);
          }
        }
      };
      xhr.onerror = () => {
        observer.error(xhr.response);
      };
      xhr.withCredentials = true;
      xhr.open('GET', url, true);
      xhr.send();
    });
  }

  onGridReady(params: GridReadyEvent) {
    this.gridApi = params.api;
    this.gridReady.emit(params);
  }

  getRowHeight(params: any) {
    const whatIfParams = params.context.componentParent._whatIfParams;
    let isAnyOpenWhatIf = false;
    if (whatIfParams) {
      whatIfParams.forEach((value) => {
        if (value) {
          isAnyOpenWhatIf = true;
        }
      });
    }
    if (params.data && (params.data.fieldType === 'Image' || params.data.fieldType === 'Attach')) {
      return 150;
    }
    if (params.data && params.data.category === 'whatIfToggle') {
      return isAnyOpenWhatIf ? 60 : 40;
    }
    if (params.data && params.data.category === 'summary') {
      return 107;
    }
    if (params.data.frozenOnTop) {
      return 40;
    }
    if (params.data.category === 'ai') {
      return 50;
    }
    return null;
  }

  updateProcessing(isProcessing: boolean) {
    this.isProcessing$.next(isProcessing);
  }

  changeTemplate($event: any) {
    this.selectedTemplate$ = of($event);
    this.init();
  }

  getDocumentSize(colDefs: ColDef[]): number {
    const currentColNum = colDefs?.filter((colDef) => !colDef.hide)?.length ?? 0;
    return currentColNum > 0 ? currentColNum - 1 : 0;
  }

  showSuccessMsg(msg: string) {
    this.notifyErrorMessage(msg);
  }

  showError(error: string) {
    this.notifyErrorMessage(error, 'warn');
  }

  private notifyErrorMessage(resultmessage: string, type?: 'primary' | 'accent' | 'warn' | 'hint') {
    this.notificationService.open({
      message: resultmessage,
      position: 'right',
      type,
    });
  }

  isRowMaster: IsRowMaster = (params: any) => (params ? params?.details?.length > 0 : false);

  detailCellRendererParams: any = (params: ICellRendererParams) => {
    const res = {} as IDetailCellRendererParams;

    res.getDetailRowData = function getDtl(masterDtl) {
      masterDtl.successCallback(params.data.details);
    };

    const columnDefs: any[] = [];

    const nameMatch = params.data.mapping === 'vqComponentCostList';
    if (nameMatch) {
      for (let i = this.refNos.length; i > 0; i -= 1) {
        columnDefs.unshift({
          headerName: 'Details',
          children: [
            { field: `Name${i}`, headerName: 'Name' },
            { field: `Cost${i}`, headerName: 'Cost' },
            { field: `SubType${i}`, headerName: 'Type', columnGroupShow: 'open' },
            { field: `Consumption${i}`, headerName: 'Consumption', columnGroupShow: 'open' },
            { field: `UOM${i}`, headerName: 'UOM', columnGroupShow: 'open' },
            { field: `UnitCost${i}`, headerName: 'Unit Cost', columnGroupShow: 'open' },
            { field: `blockA${i}`, headerName: '', columnGroupShow: 'closed' },
            { field: `blockB${i}`, headerName: '', columnGroupShow: 'closed' },
            { field: `blockC${i}`, headerName: '', columnGroupShow: 'closed' },
            { field: `blockD${i}`, headerName: '', columnGroupShow: 'closed' },
          ],
        });
      }
    } else {
      for (let i = this.refNos.length; i > 0; i -= 1) {
        columnDefs.unshift(
          { field: `Description${i}`, headerName: 'Description' },
          { field: `Cost${i}`, headerName: 'Cost' },
          { field: `blockA${i}`, headerName: '' },
          { field: `blockB${i}`, headerName: '' },
          { field: `blockC${i}`, headerName: '' },
          { field: `blockD${i}`, headerName: '' },
        );
      }
    }

    res.detailGridOptions = {
      columnDefs,
      defaultColDef: {
        sortable: true,
        lockPosition: true,
        flex: 1,
      },
    };

    const detailGridWidth = this.refNos.length * 515 + 16;

    res.template =
      `<div ref="eDetailGrid" style="width: ${detailGridWidth}px; box-sizing: border-box; font-size: 10px; ">` +
      `</div>`;
    return res;
  };

  private handleCompCostColDefForDtlGrid(
    fieldDetail: any,
    sequence: number,
    frozenOnTop: boolean,
    selectedTemplate: any,
  ) {
    if (fieldDetail.compTemplSection.sectionName !== 'Open Costing') {
      return [fieldDetail];
    }

    const data = Object.values(this.comparisonDataQuery.getValue().entities);
    const colDefList = this.comparisonService.getCompCostColDefForDtlGrid(data);
    const generalInfo = {
      sectionSequence: sequence,
      isField: true,
      mapping: 'vqComponentCostList',
      labelStyle: LABEL_STYLE.SUB_ITEM,
      frozenOnTop,
    };

    const defList: any[] = [];

    const totalComponentCostsColDef = {
      ...fieldDetail,
      label: 'Main Material',
      labelStyle: LABEL_STYLE.SUB_SECTION,
    };

    defList.push(totalComponentCostsColDef);

    colDefList.forEach((colDef, index) => {
      const seq = index + 1;

      const typeName = colDef[`type${seq}`];
      const isExistDef = defList.some((param) => param[typeName] === typeName);

      if (!isExistDef) {
        defList.push({
          ...generalInfo,
          label: typeName,
          field: typeName,
          labelStyle: LABEL_STYLE.ITEM,
          details: colDef[`details${seq}`],
        });
        this.comCostDtlGridIdx.push(selectedTemplate.compTemplSectionDetailList.length + 2 + seq);
      }
    });

    return defList;
  }

  private handleAddiCostColDefForDtlGrid(
    fieldDetail: any,
    sequence: number,
    frozenOnTop: boolean,
    selectedTemplate: any,
  ) {
    if (fieldDetail.compTemplSection.sectionName !== 'Open Costing') {
      return [fieldDetail];
    }

    const data = Object.values(this.comparisonDataQuery.getValue().entities);
    const colDefList = this.comparisonService.getAddiCostColDefForDtlGrid(data);
    const generalInfo = {
      sectionSequence: sequence,
      isField: true,
      mapping: 'vqAdditionalCostList',
      labelStyle: LABEL_STYLE.SUB_ITEM,
      frozenOnTop,
    };

    const defList: any[] = [];

    const totalAdditionalCostsColDef = {
      ...fieldDetail,
      label: 'Additional Cost',
      labelStyle: LABEL_STYLE.SUB_SECTION,
    };

    defList.push(totalAdditionalCostsColDef);

    colDefList.forEach((colDef, index) => {
      const seq = index + 1;

      const typeName = colDef[`type${seq}`];
      const isExistDef = defList.some((param) => param[typeName] === typeName);

      if (!isExistDef) {
        defList.push({
          ...generalInfo,
          label: typeName,
          field: typeName,
          labelStyle: LABEL_STYLE.ITEM,
          details: colDef[`details${seq}`],
        });
        this.comCostDtlGridIdx.push(selectedTemplate.compTemplSectionDetailList.length + 2 + seq);
      }
    });

    return defList;
  }

  excelStyles: ExcelStyle[] = [
    {
      id: 'gray01Background',
      interior: {
        color: '#f8f8f8',
        pattern: 'Solid',
      },
    },
    {
      id: 'header',
      alignment: {
        vertical: 'Top',
        horizontal: 'Left',
        wrapText: true,
      },
      font: {
        bold: true,
        family: 'Roboto, "Helvetica Neue", sans-serif, Arial, Helvetica',
        size: 12,
        underline: 'Single',
        color: '#046dc9',
      },
      interior: {
        color: '#bbbbbb',
        pattern: 'Solid',
        patternColor: '#bbbbbb',
      },
      borders: {
        borderBottom: { weight: 1 },
        borderLeft: { weight: 1 },
        borderRight: { weight: 1 },
        borderTop: { weight: 1 },
      },
    },

    {
      id: 'cell',
      alignment: {
        vertical: 'Top',
        horizontal: 'Left',
        wrapText: true,
      },
      font: {
        family: 'Roboto, "Helvetica Neue", sans-serif, Arial, Helvetica',
        size: 12,
      },
      borders: {
        borderBottom: { weight: 1 },
        borderLeft: { weight: 1 },
        borderRight: { weight: 1 },
        borderTop: { weight: 1 },
      },
    },

    {
      id: 'detailHeader',
      alignment: {
        vertical: 'Top',
        horizontal: 'Left',
        wrapText: true,
      },
      font: {
        family: 'Roboto, "Helvetica Neue", sans-serif, Arial, Helvetica',
        size: 12,
      },
      interior: {
        color: '#cccccc',
        pattern: 'Solid',
        patternColor: '#cccccc',
      },
      borders: {
        borderBottom: { weight: 1 },
        borderLeft: { weight: 1 },
        borderRight: { weight: 1 },
        borderTop: { weight: 1 },
      },
    },

    {
      id: 'detailBody',
      alignment: {
        vertical: 'Top',
        horizontal: 'Left',
        wrapText: true,
      },
      font: {
        family: 'Roboto, "Helvetica Neue", sans-serif, Arial, Helvetica',
        size: 12,
      },
      interior: {
        color: '#eeeeee',
        pattern: 'Solid',
        patternColor: '#eeeeee',
      },
      borders: {
        borderBottom: { weight: 1 },
        borderLeft: { weight: 1 },
        borderRight: { weight: 1 },
        borderTop: { weight: 1 },
      },
    },

    {
      id: 'hyperlink',
      font: {
        underline: 'Single',
        color: '#046dc9',
      },
      dataType: 'Formula',
    },
  ];

  buildSearchCondition(documents: DocumentViewData[]) {
    if (!documents) {
      return;
    }
    let cbxql = '';
    documents.forEach((document) => {
      cbxql = `${cbxql}vqNo~${document.refNo};||;`;
    });
    cbxql = cbxql.substring(0, cbxql.length - 4);
    this.searchCondition$.next({ cbxql });
  }

  buildBookmarkName(documents: DocumentViewData[]) {
    this.naviView$.next(null);
    documents.forEach((document) => {
      const { itemNo } = document;
      this.buildNaviView(itemNo);
    });
    if (this.naviView$.value === null) {
      documents.forEach((document) => {
        const { itemNo } = document;
        this.buildNaviView(itemNo);
      });
    }
  }

  buildNaviView(itemNo: string) {
    if (!this.naviView$.value) {
      const bookmarkName = `${itemNo} Quote Comparison`;
      this.naviView$.next({
        id: 'vqActiveView',
        label: bookmarkName,
        naviEntryId: 'vq3Active',
      });
    }
  }
}
