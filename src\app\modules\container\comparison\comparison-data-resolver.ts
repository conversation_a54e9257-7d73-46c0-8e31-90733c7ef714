import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';

import { Observable, of } from 'rxjs';

import { ComparisonService } from './service/comparison.service';
import * as ComparisonUtil from './util/comparison-util';
import { convertFromHex } from 'src/app/utils';

export interface ComparisonDataResolved {
  moduleId: string;
  refNos: string[];
}

export const ComparisonDataResolver: ResolveFn<Observable<ComparisonDataResolved>> = (
  route: ActivatedRouteSnapshot,
): Observable<ComparisonDataResolved> => {
  const comparisonService = inject(ComparisonService);

  const params = route.paramMap.get('params');
  comparisonService.comparisonParams = params;
  const paramsStr = convertFromHex(params);
  const paramsMap: Map<string, string> = ComparisonUtil.getParamsMap(paramsStr);
  const moduleId: string = paramsMap.get('moduleId');
  const refNos: string[] = paramsMap.get('refNos').split(',');
  const cbxContextUrl: string = paramsMap.get('cbxContextUrl');
  comparisonService.cbxContextUrl = cbxContextUrl;
  return of({
    moduleId,
    refNos,
    cbxContextUrl,
  });
};
