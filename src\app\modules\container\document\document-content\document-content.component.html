<div appOverlayHost class="overlay-container position-sticky top-0 pointer-events-none"></div>

<div *ngIf="read || !edit" class="status flex">
  <!-- <h2 *ngIf="!useTag$" class="subject-view overflow-hidden text-ellipsis" [appTooltip]="subject" tooltipDetectContentOverflow="true">
    {{ subject }}
  </h2> -->

  <ng-container *ngIf="useTag && refNo$ | async as refNo">
    <app-tag-chips
      [readonly]="true"
      [refNo]="refNo"
      [version]="version$ | async"
      [moduleId]="moduleId"
      tooltipDetectContentOverflow="true"
      class="subject-view overflow-hidden text-ellipsis tag-container width-100"
    ></app-tag-chips>
  </ng-container>

  <div *ngIf="statusDescription$ | async as statusDescription" class="status-creator">{{ statusDescription }}</div>
</div>

<ng-content select="[approvalButtons]"></ng-content>

<ng-content select="[acknowledgeButton]"></ng-content>

<div *ngIf="!read && edit && useTag">
  <app-tag-chips
    class="subject-view overflow-hidden text-ellipsis tag-container"
    [appTooltip]="subject"
    tooltipDetectContentOverflow="true"
    [refNo]="refNo$ | async"
    [version]="version$ | async"
    [moduleId]="moduleId"
  ></app-tag-chips>
</div>

<div *ngIf="(view$ | async) && (warningMsgList$ | async)">
  <div class="warning-wrapper">
    <label *ngFor="let warningMsg of warningMsgList$ | async" class="warning">
      <span class="message-type-icon">⚠</span>
      {{ warningMsg }}
    </label>
  </div>
</div>

<div
  [cdkDropListDisabled]="disableDrag$ | async"
  cdkDropList
  [cdkDropListData]="filteredSequentialChapters$ | async"
  (cdkDropListDropped)="onTabDrop($event)"
>
  <div
    cdkDrag
    [cdkDragDisabled]="disableDrag$ | async"
    *ngFor="let tab of filteredSequentialChapters$ | async; trackBy: checksumTrackBy"
    class="form-group"
    appPositionContainer
    [attr.id]="tab.id | genAnchorId : sectionIdPrefix"
  >
    <div *ngIf="tab.tabGroupType === 'divider'; else content" class="tab-divider bg-white"></div>
    <ng-template #content>
      <div appPositionTabHeader class="tab-header position-sticky top-0 flex items-center">
        <h4 cdkDragHandle class="tab-header-label" (click)="onLabelClick('Tab', tab.id, tab.label)">{{ tab.label }}</h4>
        <app-icon
          *ngIf="tab.hint"
          svgIcon="icon_info_yellow"
          class="margin-right-4"
          [appHint]="tab.hint"
          hintPosition="below"
          [hintShowIcon]="true"
        ></app-icon>

        <app-document-section-header-content
          *ngIf="tab.subChapters[0]?.hideLabel"
          [section]="tab.subChapters[0]"
          [moduleId]="moduleId"
          [edit]="edit"
        ></app-document-section-header-content>
      </div>

      <div class="sharefile-wrapper" [class.edit]="edit && canAdd">
        <div *ngIf="edit && canAdd" class="upload-hint flex items-center justify-center">
          <app-icon class="upload-icon margin-right-4">add</app-icon>

          <input
            #fileInput
            [multiple]="multiple"
            type="file"
            class="cdk-visually-hidden"
            (change)="uploadFile(fileInput.files)"
          />

          <div class="upload-hint-label flex">
            Drag & drop files here,&nbsp;
            <div class="upload-action pointer" (click)="fileInput.click()">browse your files</div>
          </div>
        </div>

        <app-drop-zone value="" acceptType="*" [customStyle]="{ padding: 0 }" (dropped)="uploadFile($event)">
        </app-drop-zone>
      </div>

      <div *ngIf="edit && tab.editingHint" class="tab-description">{{ tab.editingHint }}</div>
      <div
        id="{{ tab.id }}"
        [cdkDropListConnectedTo]="dropConnectedList$ | async"
        cdkDropList
        [cdkDropListData]="tab.subChapters"
        (cdkDropListDropped)="onSectionDrop($event)"
        [cdkDropListDisabled]="disableDrag$ | async"
      >
        <div
          *ngFor="let section of tab.subChapters; trackBy: checksumTrackBy"
          cdkDrag
          [cdkDragDisabled]="disableDrag$ | async"
          appTocChapter
          appSectionState
          appAutoScrollEdge
          class="form-section contain-content padding-right-8"
          [class.cdk-visually-hidden]="section.hidden"
          [attr.id]="section.id | genAnchorId : sectionIdPrefix"
          (chapterEnter)="onChapterEnter(tab, section)"
        >
          <!-- TODO: optimize avoid invoke function -->
          <div
            *ngIf="getDynamicSectionVisibleLogical(section, true) | async"
            appPositionSectionHeader
            [sectionId]="section.id"
            class="section-header position-sticky flex items-center"
          >
            <h5
              cdkDragHandle
              class="section-header-label"
              (click)="onLabelClick('Section', tab.id, section.label, section.id)"
            >
              {{ section.label }}
            </h5>
            <app-icon
              *ngIf="section.hint"
              svgIcon="icon_info_yellow"
              class="margin-right-4"
              [appHint]="section.hint"
              hintPosition="below"
              [hintShowIcon]="true"
            ></app-icon>

            <app-document-section-header-content
              [section]="section"
              [moduleId]="moduleId"
              [edit]="edit"
            ></app-document-section-header-content>
          </div>

          <ng-container *ngIf="getDynamicSectionVisibleLogical(section, false) | async" [ngSwitch]="section.type">
            <app-form-section-container
              *ngSwitchCase="'form'"
              appViewportIndicator
              [changeDetectionTrigger]="edit"
              [section]="section"
              [edit]="edit"
              [module]="moduleId"
              [isCopyDocument]="isCopyDocument"
              [isPreview]="sectionIdPrefix === 'preview'"
            ></app-form-section-container>

            <ng-container *ngSwitchCase="'grid'">
              <app-grid-section-container
                appViewportIndicator
                [changeDetectionTrigger]="edit"
                [moduleId]="moduleId"
                [tabId]="tab.id"
                [section]="section"
              ></app-grid-section-container>
            </ng-container>

            <app-group-grid-container
              *ngSwitchCase="'groupGrid'"
              appViewportIndicator
              appSyncHorizontalScrollContainer
              [changeDetectionTrigger]="edit"
              [moduleId]="moduleId"
              [section]="section"
              [tabId]="tab.id"
            ></app-group-grid-container>

            <ng-container *ngSwitchCase="'attachment'">
              <app-attachment-section-container
                appViewportIndicator
                [changeDetectionTrigger]="edit"
                [moduleId]="moduleId"
                [section]="section"
                [edit]="edit"
              ></app-attachment-section-container>
            </ng-container>

            <ng-container *ngSwitchCase="'sectionList'">
              <app-section-list-container
                appViewportIndicator
                [changeDetectionTrigger]="edit"
                [moduleId]="moduleId"
                [section]="section"
                [edit]="edit"
                [tab]="tab"
                [isCopyDocument]="isCopyDocument"
                [sectionIdPrefix]="sectionIdPrefix"
                [section]="section"
                (chapterChanged)="onChapterEnterForSectionList($event)"
              ></app-section-list-container>
            </ng-container>

            <ng-container *ngSwitchCase="'powerbi'">
              <div *ngIf="refNo$ | async">
                <app-hierarchy-chart
                  [refNo]="refNo$ | async"
                  [version]="version$ | async"
                  [moduleId]="moduleId"
                ></app-hierarchy-chart>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'viewGrid'">
              <app-viewgrid-section-container
                [moduleId]="moduleId"
                [section]="section"
              ></app-viewgrid-section-container>
            </ng-container>

            <ng-container *ngSwitchCase="'htmlEditor'">
              <app-html-editor-section-container [edit]="edit" [section]="section"></app-html-editor-section-container>
            </ng-container>

            <app-dynamic-section *ngSwitchDefault [section]="section" [moduleId]="moduleId"></app-dynamic-section>
          </ng-container>
        </div>
      </div>
    </ng-template>
  </div>
</div>
<div *ngIf="onlyChapterLoading$ | async" class="flex-center">
  <span class="bouncing-loading"><span class="bouncing-point"></span></span>
</div>

<div appVisibleIndicator class="spacer" (elReady)="verticalSpacerReady.emit($event)"></div>

<ng-content select="[cpmBar]"></ng-content>
