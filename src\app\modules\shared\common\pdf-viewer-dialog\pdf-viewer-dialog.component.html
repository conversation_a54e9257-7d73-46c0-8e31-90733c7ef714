<app-cbx-dialog-header> {{ title }}</app-cbx-dialog-header>

<div class="control-pdf-page-wrapper flex justify-end">
  <div class="control-pdf-page-text">Page {{ currentPage$ | async }} of {{ totalPage$ | async }}</div>

  <button
    class="icon-button icon-button-size-24 color-primary-gray"
    aria-label="button for navigate to previous page"
    [disabled]="(currentPage$ | async) === 1"
    (click)="incrementPage(-1)"
  >
    <app-icon>navigate_before</app-icon>
  </button>

  <button
    class="icon-button icon-button-size-24 color-primary-gray"
    aria-label="button for navigate to next page"
    [disabled]="(currentPage$ | async) === (totalPage$ | async)"
    (click)="incrementPage(1)"
  >
    <app-icon>navigate_next</app-icon>
  </button>
</div>

<div class="content flex">
  <app-base-pdf
    class="height-100 width-100"
    [value]="attachment.url"
    [page]="currentPage$ | async"
    [zoom]="currentZoom$ | async"
    zoomScale="page-fit"
    [originalSize]="false"
    [showAll]="false"
    [showBorders]="false"
    [renderText]="false"
    [stickToPage]="false"
    (pdfNumPages)="totalPage$.next($event)"
  ></app-base-pdf>

  <div class="control-container grid">
    <div class="tools-wrapper margin-top-auto">
      <a
        class="flex-center tool-button"
        target="_blank"
        [href]="attachment.url"
        [download]="attachment.file?.fileName"
        appHint="Download"
        hintPosition="left"
      >
        <app-icon>save_alt</app-icon>
      </a>

      <button class="tool-button" appHint="Zoom in" hintPosition="left" (click)="incrementZoom(0.1)">
        <app-icon>add</app-icon>
      </button>

      <button class="tool-button" appHint="Zoom out" hintPosition="left" (click)="incrementZoom(-0.1)">
        <app-icon>remove</app-icon>
      </button>
    </div>
  </div>
</div>
