import { DIALOG_DATA } from '@angular/cdk/dialog';
import { AsyncPipe, NgIf } from '@angular/common';
import { Component, EventEmitter, Inject, Output } from '@angular/core';

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { GridApi, GridReadyEvent, ExcelCell, ProcessRowGroupForExportParams, ExcelImage } from 'ag-grid-community';
import { ReplaySubject, distinctUntilChanged, Subject, switchMap, of, BehaviorSubject, catchError } from 'rxjs';

import { CbxDialogHeaderComponent } from '../../../shared/common/cbx-dialog-header/cbx-dialog-header.component';
import { genFindCostElementFunc } from '../../comparison/util/comparison-util';
import { WhatIfComparisonComponent } from '../../comparison/what-if-comparison/what-if-comparison.component';
import { RightHandPanelStateService } from '../../document/right-hand-panel/right-hand-panel-state.service';
import { DocumentViewData } from 'src/app/entities';
import { QuoteCompareDialogData, View } from 'src/app/interface/model';
import { FileService } from 'src/app/modules/services/file.service';
import { isEmptyOrNil, isNotEmptyOrNil } from 'src/app/utils';
import { DomainAttributeService } from 'src/app/workspace/service/domain-attribute.service';
import { DOMAIN_ATTRIBUTES } from 'src/app/workspace/service/domain-attributes';

@UntilDestroy()
@Component({
  selector: 'app-compare-quotations-dialog',
  templateUrl: './compare-quotations-dialog.component.html',
  styleUrls: ['./compare-quotations-dialog.component.scss'],
  standalone: true,
  imports: [NgIf, AsyncPipe, CbxDialogHeaderComponent, WhatIfComparisonComponent],
  providers: [RightHandPanelStateService],
})
export class CompareQuotationsDialogComponent {
  moduleId = 'vq';

  itemRefNo: string;

  refNos: string[];

  costSheetRef: string;

  imageExp = /\/(?<filename>[^/]*).(?<extension>jpg|jpeg|png|gif)/;

  @Output() openExportDialog = new EventEmitter<boolean>();

  constructor(
    @Inject(DIALOG_DATA) public data: QuoteCompareDialogData,
    private readonly fileService: FileService,
    private readonly domainAttributeService: DomainAttributeService,
  ) {
    const { moduleId, refNos, costSheetRef, refNo } = data;
    if (moduleId === 'vq') {
      this.refNos = refNos;
      if (costSheetRef) {
        this.costSheetRef = costSheetRef;
      }
    } else {
      this.itemRefNo = refNo;
    }
  }

  private readonly booleanValueMap = new Set(['yes', 'true']);
  showExportButton: boolean = this.handleExportVisible();
  private handleExportVisible(): boolean {
    const enabled = this.domainAttributeService.state[DOMAIN_ATTRIBUTES.UI_COMPARE_QUOTATIONS_EXPORT_ENABLED];
    if (isEmptyOrNil(enabled)) {
      return false;
    }

    return this.booleanValueMap.has(enabled.toLowerCase());
  }

  viewSubject = new ReplaySubject<View>(1);
  view$ = this.viewSubject.pipe(distinctUntilChanged());

  gridApi: GridApi;
  exportReady$ = new Subject<void>();
  exporting$ = new BehaviorSubject<boolean>(false);
  tabId: string;
  documents: DocumentViewData[] = [];

  gridReady({ api }: GridReadyEvent) {
    this.gridApi = api;
  }

  imageMap = new Map();

  onDocuments(documents: DocumentViewData[]) {
    documents.forEach((doc) => {
      this.createBase64Flags(doc);
    });
    this.documents = documents;
  }

  createBase64Flags = (doc: DocumentViewData) => {
    if (!doc?.fileId?.original?.url || this.imageMap.has(doc?.fileId?.original?.url)) {
      return;
    }

    this.fileService.getImageBlobById$(doc.fileId.original.url).subscribe((blob) => {
      this.fileService.fromBlobToBase64(blob).then((base64) => {
        this.imageMap.set(doc.fileId.original.url, base64);
      });
    });
  };

  onExport() {
    if (this.exporting$.getValue()) {
      return;
    }
    this.exporting$.next(true);
    this.exportReady$.next();
  }

  cell(ref: string, text: string, styleId?: string, mergeAcross?: number): ExcelCell {
    return {
      ref,
      styleId,
      data: {
        type: /^\d+$/.test(text) ? 'Number' : 'String',
        value: String(text),
      },
      mergeAcross,
    };
  }

  getRows(params: ProcessRowGroupForExportParams) {
    if (params.node.data.mapping === 'vqComponentCostList') {
      const cellsHeader: ExcelCell[] = [];
      for (let i = params.context.componentParent.refNos?.length; i > 0; i -= 1) {
        cellsHeader.unshift(this.cell(`UnitCost${i}`, 'UnitCost', 'detailHeader'));
        cellsHeader.unshift(this.cell(`UOM${i}`, 'UOM', 'detailHeader'));
        cellsHeader.unshift(this.cell(`Consumption${i}`, 'Consumption', 'detailHeader'));
        cellsHeader.unshift(this.cell(`Type${i}`, 'Type', 'detailHeader'));
        cellsHeader.unshift(this.cell(`Cost${i}`, 'Cost', 'detailHeader'));
        cellsHeader.unshift(this.cell(`Name${i}`, 'Name', 'detailHeader'));
      }
      cellsHeader.unshift(this.cell('blankHolder', '', 'detailHeaderHolder'));
      const rows = [
        {
          outlineLevel: 1,
          cells: cellsHeader,
        },
      ].concat(
        params.node.data.details?.filter(isNotEmptyOrNil).map((record: any) => {
          const cellsData: ExcelCell[] = [];
          for (let i = params.context.componentParent.refNos.length; i > 0; i -= 1) {
            cellsData.unshift(
              this.cell(`UnitCost${i}`, record[`UnitCost${i}`] ? record[`UnitCost${i}`] : '', 'detailBody'),
            );
            cellsData.unshift(this.cell(`UOM${i}`, record[`UOM${i}`] ? record[`UOM${i}`] : '', 'detailBody'));
            cellsData.unshift(
              this.cell(`Consumption${i}`, record[`Consumption${i}`] ? record[`Consumption${i}`] : '', 'detailBody'),
            );
            cellsData.unshift(
              this.cell(`SubType${i}`, record[`SubType${i}`] ? record[`SubType${i}`] : '', 'detailBody'),
            );
            cellsData.unshift(this.cell(`Cost${i}`, record[`Cost${i}`] ? record[`Cost${i}`] : '', 'detailBody'));
            cellsData.unshift(this.cell(`Name${i}`, record[`Name${i}`] ? record[`Name${i}`] : '', 'detailBody'));
          }
          cellsData.unshift(this.cell('blankHolder', '', 'detailBodyHolder'));
          return [
            {
              outlineLevel: 1,
              cells: cellsData,
            },
          ];
        }),
      );
      return rows;
    }
    if (params.node.data.mapping === 'vqAdditionalCostList') {
      const cellsHeader: ExcelCell[] = [];
      for (let i = params.context.componentParent.refNos?.length; i > 0; i -= 1) {
        cellsHeader.unshift(this.cell(`blankHolderA${i}`, '', 'detailHeader'));
        cellsHeader.unshift(this.cell(`blankHolderB${i}`, '', 'detailHeader'));
        cellsHeader.unshift(this.cell(`blankHolderC${i}`, '', 'detailHeader'));
        cellsHeader.unshift(this.cell(`blankHolderD${i}`, '', 'detailHeader'));
        cellsHeader.unshift(this.cell(`Cost${i}`, 'Cost', 'detailHeader'));
        cellsHeader.unshift(this.cell(`Description${i}`, 'Description', 'detailHeader'));
      }
      cellsHeader.unshift(this.cell('blankHolder', '', 'detailHeaderHolder'));
      const rows = [
        {
          cells: cellsHeader,
        },
      ].concat(
        params.node.data.details?.filter(isNotEmptyOrNil).map((record: any) => {
          const cellsData: ExcelCell[] = [];
          for (let i = params.context.componentParent.refNos.length; i > 0; i -= 1) {
            cellsData.unshift(this.cell(`blankHolderA${i}`, '', 'detailBody'));
            cellsData.unshift(this.cell(`blankHolderB${i}`, '', 'detailBody'));
            cellsData.unshift(this.cell(`blankHolderC${i}`, '', 'detailBody'));
            cellsData.unshift(this.cell(`blankHolderD${i}`, '', 'detailBody'));
            cellsData.unshift(this.cell(`Cost${i}`, record[`Cost${i}`] ? record[`Cost${i}`] : '', 'detailBody'));
            cellsData.unshift(
              this.cell(`Description${i}`, record[`Description${i}`] ? record[`Description${i}`] : '', 'detailBody'),
            );
          }
          cellsData.unshift(this.cell('blankHolder', '', 'detailBodyHolder'));
          return [{ cells: cellsData }];
        }),
      );
      return rows;
    }
    return null;
  }

  exportReadyCompleted$ = this.exportReady$
    .pipe(
      switchMap(() => {
        const imagePaths = this.gridApi
          .getRenderedNodes()
          .filter((node) => node.data.fieldType === 'Image')
          .map((node) => node.data.field)
          .map((field) => field?.split('.').filter((path) => path !== 'entity') ?? [])
          .filter((paths) => !!paths.length);
        const docIds = this.gridApi
          .getColumnDefs()
          .map((colDef) => colDef.headerName)
          .filter((headerName) => headerName);

        const headerNameToDocumentMap = this.documents.reduce((acc, doc) => {
          acc[doc.refNo] = doc;
          return acc;
        }, {}) as { [headerName: string]: DocumentViewData };

        const urls = docIds.flatMap((docId) => {
          const document = headerNameToDocumentMap[docId];
          if (document) {
            return imagePaths
              .map((paths) => {
                const value = paths.reduce((acc, path) => acc[path], document);
                return value?.original?.thumbnailUrl;
              })
              .filter((url) => !!url);
          }
          return [];
        });

        const imageMap$ = urls.length
          ? this.fileService.downloadToExcel$(urls.map((url) => ({ value: url, url }))).pipe(catchError(() => of({})))
          : of({});

        return imageMap$;
      }),
      untilDestroyed(this),
    )
    .subscribe({
      next: (imageMap) => {
        const headerNameToDocumentMap = this.documents.reduce((acc, doc) => {
          acc[doc.refNo] = doc;
          return acc;
        }, {}) as { [headerName: string]: DocumentViewData };

        const imageExp =
          /\/(?<filename>[^/]*).(?<extension>jpg|JPG|webp|WEBP|jpeg|JPEG|png|PNG|gif|GIF|jfif|JFIF|bmp|BMP|tif|TIF)/;

        const hasImage = Object.keys(imageMap).length > 0;

        const imageRowIndexSet = new Set<number>();
        if (hasImage) {
          this.gridApi.getRenderedNodes().forEach((node, index) => {
            if (node.data.fieldType === 'Image') {
              imageRowIndexSet.add(index + 1);
            }
          });
        }

        this.gridApi.exportDataAsExcel({
          fileName: 'Quotation Comparison',
          sheetName: 'Quotation Comparison',
          headerRowHeight: 32,
          rowHeight: hasImage ? (params) => (imageRowIndexSet.has(params.rowIndex) ? 180 : 32) : 32,
          columnWidth: 180,
          skipColumnHeaders: true,

          processCellCallback: (params) => {
            const colDef = params.column.getColDef();
            const document = headerNameToDocumentMap[colDef?.headerName];
            const { data } = params.node;
            const { field } = data;
            if (!params.value && document) {
              const paths = field?.split('.').filter((path) => path !== 'entity') ?? [];
              if (paths.length > 0) {
                if (field.includes('costSheet.elementList.')) {
                  const findCostElementFunc = genFindCostElementFunc(field);
                  return findCostElementFunc(document);
                }
                // START: handle hardcoded hyperlinks
                if (field.endsWith('vqNo')) {
                  const path = `/sourcing/vq/${document.refNo}`;
                  const displayValue = document.vqNo;

                  return `=HYPERLINK("${window.location.protocol}//${window.location.host}${path}", "${displayValue}")`;
                }
                if (field.endsWith('itemNo')) {
                  if (document.item?.refNo) {
                    const path = `/product/item/${document.item.refNo}`;
                    const displayValue = document.item.itemNo;

                    return `=HYPERLINK("${window.location.protocol}//${window.location.host}${path}", "${displayValue}")`;
                  }
                  return '';
                }
                if (field.endsWith('vendor')) {
                  if (document.vendor?.refNo) {
                    const path = `/master/vendor/${document.vendor.refNo}`;
                    const displayValue = document.vendor.businessName;

                    return `=HYPERLINK("${window.location.protocol}//${window.location.host}${path}", "${displayValue}")`;
                  }
                  return '';
                }
                if (field.endsWith('costSheet.refNo')) {
                  if (document.costSheet?.refNo) {
                    const path = `/sourcing/cost/${document.costSheet.refNo}`;
                    const displayValue = document.costSheet.refNo;

                    return `=HYPERLINK("${window.location.protocol}//${window.location.host}${path}", "${displayValue}")`;
                  }
                  return '';
                }
                if (field.endsWith('factory')) {
                  if (document.factory?.refNo) {
                    const path = `/master/factory/${document.factory.refNo}`;
                    const displayValue = document.factory.businessName;

                    return `=HYPERLINK("${window.location.protocol}//${window.location.host}${path}", "${displayValue}")`;
                  }
                  return '';
                }
                // END: handle hardcoded hyperlinks

                const value = paths.reduce((acc, path) => acc?.[path], document);
                if (data.fieldType === 'Selection') {
                  return value?.businessName;
                }
                if (data.fieldType === 'Hyperlink') {
                  return value;
                }
                if (data.fieldType === 'Label') {
                  if (value === true) return 'Yes';
                  if (value === false) return 'No';
                  return value;
                }
                if (data.fieldType === 'Codelist') {
                  const [key] = paths;
                  return key.indexOf('custCodelist') === -1
                    ? value.name
                    : document?.customFields?.[`customFieldCodeList${key.split('custCodelist')[1]}`]?.name;
                }
                if (data.fieldType === 'Dropdown') {
                  return value?.name;
                }
                if (data.fieldType === 'Image') {
                  return value;
                }
                if (data.fieldType === 'TextArea') {
                  return value;
                }
                if (data.fieldType === 'Checkbox') {
                  return value ? 'Yes' : 'No';
                }
              }

              if (data.mapping === 'vqComponentCostList' || data.mapping === 'vqAdditionalCostList') {
                return document[data.mapping]
                  .filter((compCost) => compCost.materialType?.name === data.label)
                  .map((compCost) => compCost.calculatedCost ?? 0)
                  .reduce((cost, result) => cost + result, 0);
              }

              return paths.length ? paths.reduce((acc, path) => acc?.[path], document) : '';
            }
            return params.value ?? '';
          },

          getCustomContentBelowRow: (params) => {
            if (
              params.node.data.mapping === 'vqComponentCostList' ||
              params.node.data.mapping === 'vqAdditionalCostList'
            ) {
              const rows = this.getRows(params);
              return rows.map((row) => (row ? row.cells : [this.cell('', ''), this.cell('', '', '', 5)])) as any;
            }
            return null;
          },
          shouldRowBeSkipped: (params) => !params.node.data.label,

          addImageToCell: (rowIndex, column, value) => {
            if ((value as any)?.original?.thumbnailUrl) {
              const url = (value as any).original.thumbnailUrl;
              if (imageMap[url]) {
                const { filename, extension } = url.match(imageExp)?.groups ?? {};
                if (extension) {
                  return {
                    image: {
                      id: filename,
                      base64: imageMap[url],
                      imageType: extension.toLowerCase() as
                        | 'jpg'
                        | 'jpeg'
                        | 'png'
                        | 'gif'
                        | 'jfif'
                        | 'tif'
                        | 'bmp'
                        | 'webp',
                      fitCell: true,
                    } as ExcelImage,
                  };
                }
              }
            }
            return undefined;
          },
        });

        this.exporting$.next(false);
      },
    });
}
