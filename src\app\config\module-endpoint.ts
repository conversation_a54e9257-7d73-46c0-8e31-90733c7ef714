export const MODULE_ENDPOINT = {
  color: 'colors',
  fact: 'factories',
  inspectBooking: 'inspect_bookings',
  item: 'items',
  import: 'imports',
  note: 'notes',
  pattern: 'patterns',
  rfq: 'request_for_quotations',
  vendor: 'vendors',
  vq: 'quotations',
  vqce: 'quotations_ce',
  codelist: 'codelists',
  cust: 'customers',
  offersheet: 'offersheets',
  cpm: '',
  activities: '',
  apiRepresentation: '',
  commandLog: '',
  shipmentAdvice: 'shipmentAdvices',
  component: 'components',
  shipmentCompliance: '',
  packagingArtwork: 'packagingArtworks',
  hclType: 'hclTypes',
  colorPalette: 'color_palettes',
  sizeTemplate: 'sizeTemplates',
  termsAndConditions: 'termsAndConditionss',
  domain: 'domains',
  openCostingTemplate: 'openCostingTemplates',
  queryCountCache: '',
  partyTemplate: 'partyTemplates',
  defaultProfile: 'defaultProfiles',
  registrationRequest: '',
  materialAttributeTemplate: 'materialAttributeTemplates',
  claim: 'claims',
  inspectcheck: '',
  navi: 'naviEntries',
  factAudit: 'factory_audits',
  personalizeView: '',
  program: '',
  entityConfig: '',
  vendorCompliance: '',
  mpo: 'vendorMasterOrders',
  dataListType: 'dataListTypes',
  contractFile: 'contractFiles',
  rfi: 'rfis',
  companyProfile: 'companyProfile',
  inspectReportTemplate: 'inspectReportTemplates',
  inspectTemplate: 'inspectTemplates',
  commit: '',
  actionMap: '',
  notificationProfile: 'notificationProfiles',
  printFormExportTemplate: 'printFormExportTemplates',
  project: 'projects',
  materialPalette: 'materialPalettes',
  evaluationTemplate: 'evaluationTemplates',
  sourcingRecord: 'sourcing_records',
  follow: '',
  domainGroup: '',
  lookup: 'lookupLists',
  homepageTemplate: '',
  recentAccess: '',
  cpmTempl: 'cpmTempls',
  docLock: '',
  userCacheData: '',
  sampleRequest: 'sample_requests',
  favorite: 'favorites',
  artworkPalette: 'artworkPalettes',
  advancedSearch: '',
  fileLibrary: '',
  importProgress: '',
  factoryCompliance: '',
  correctiveActionPlans: 'corrective_action_plans',
  vendorInvoice: 'vendorInvoices',
  user: 'users',
  relatedDoc: '',
  defaultSearch: '',
  custFieldDef: 'custFieldDefs',
  apiRepresentationOption: '',
  dataInheritanceProfile: '',
  aprvType: '',
  scheduler: '',
  attachment: '',
  system: 'system',
  cpo: 'cpos',
  productCompliance: '',
  sampleEvaluation: 'sample_evaluations',
  passwordPolicy: '',
  commandListener: '',
  formatProfile: '',
  aprvTempl: 'aprvTempls',
  vpo: 'purchase_orders',
  externalDocument: '',
  custInv: 'custInvs',
  view: 'views',
  cpmTaskFtl: '',
  naviEntry: '',
  gridColsVisibleOrder: '',
  validationProfile: 'validationProfiles',
  systemMessage: 'systemMessages',
  factoryAuditTemplate: 'factoryAuditTemplates',
  checksumCacheData: '',
  artwork: 'artworks',
  trigger: 'triggers',
  documentRequestTemplate: 'documentRequestTemplates',
  role: 'roles',
  cpmNotification: '',
  invitationRequest: 'invitations',
  registerToken: '',
  letterOfCredit: '',
  measurementTemplate: 'measurementTemplates',
  forwarder: 'forwarders',
  aprvProfile: '',
  reqTempl: 'reqTempls',
  vpoAck: 'vpoAcks',
  docTag: 'doctags',
  compTempl: 'compTempls',
  condition: 'conditions',
  vq2: '',
  costTempl: 'costTempls',
  accessObject: 'accessObjects',
  selection: '',
  event: '',
  packaging: 'packagings',
  complianceTemplate: '',
  hotDeployError: '',
  personalizedNavi: '',
  hcl: 'hcls',
  viewAdministration: '',
  basket: '',
  sampleTracker: 'sample_trackers',
  cpmTask: '',
  cost: 'costsheets',
  patternPalette: 'patternPalettes',
  resetPassword: '',
  qc: '',
  labelProfile: 'labelProfiles',
  externalAccessRequest: '',
  catalog: '',
  inspectReport: 'inspect_reports',
  validationConfig: '',
  cpmTaskTempl: 'cpmTaskTempls',
  rfs: 'request_for_specifications',
  specification: 'specifications',
  label: '',
  sampleRequestTemplate: 'sampleRequestTemplates',
  forum: '',
  dataMappingRule: '',
  materialRequestTemplate: 'materialRequestTemplates',
  group: 'groups',
  history: '',
  moduleGroup: '',
  docTransaction: '',
  systemFile: 'systemFiles',
  codelistType: '',
  validator: '',
  userAcsRule: '',
  agreementTemplate: 'agreementTemplates',
  vendorChargesTemplate: 'vendorChargesTemplates',
  shipmentBooking: 'shipment_bookings',
  qq: 'qqs',
  domainMasterMapping: '',
  auditLog: '',
  qcchecklisttemplate: '',
  dataAbstractConfig: 'data_abstract_config',
  tag: '',
  notification: 'notifications',
  shareFile: 'shareFiles',
  lineSheet: 'lineSheets',
  ingredient: 'ingredients',
  cso: 'sales_orders',
  careInstruction: 'careInstructions',
  packingList: 'packingLists',
  nutritionTemplate: 'nutritionTemplates',
  testMethod: 'testMethods',
  testProtocolTemplate: 'testProtocolTemplates',
  testAccreditation: 'test_accreditations',
  testReport: 'testReports',
  qualityPlanTemplate: 'qualityPlanTemplates',
  capaTemplate: 'capaTemplates',
  inlineComparisonTemplate: 'in_line_comparison_template',
  workflow: 'workflows',
  formTemplate: 'formTemplates',
  basedNotification: 'basedNotis',
  communicationDoc: 'communicationDocs',
  communication: 'communications',
} as const;
