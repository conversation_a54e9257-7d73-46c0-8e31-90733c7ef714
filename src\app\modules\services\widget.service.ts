import { HttpParams } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';

import { Constructor } from '@datorama/akita';
import { add, endOfWeek, format, isValid, startOfWeek, sub } from 'date-fns';
import { is, isNil } from 'ramda';
import { BehaviorSubject, Observable, distinctUntilChanged, map, of, switchMap } from 'rxjs';
import { catchError, filter, take, tap } from 'rxjs/operators';

import { BaseBarChartWidgetComponent } from '../../components/widget/widgetTypes/base-bar-chart-widget/base-bar-chart-widget.component';
import { BaseNumberWidgetComponent } from '../../components/widget/widgetTypes/base-number-widget/base-number-widget.component';
import { DateTrackingChartWidgetComponent } from '../../components/widget/widgetTypes/date-tracking-chart/date-tracking-chart-widget.component';
import { EmbeddedBiWidgetComponent } from '../../components/widget/widgetTypes/shared/embedded-bi/embedded-bi-widget.component';
import { FilterWidgetComponent } from '../../components/widget/widgetTypes/shared/filter-widget/filter-widget.component';
import { ShareKanbanComponent } from '../../components/widget/widgetTypes/shared/kanban/share-kanban.component';
import {
  Widget_DefectQuantityByQualityIssueDto,
  Widget_OderQuantityByCountryDto,
  Widget_OrderAmountByFactAuditResultDto,
  Widget_OrderQuantityBySupplierDataDto,
  Widget_WidgetType,
} from '../../entities/api';
import { DomainAttributeService } from '../../workspace/service/domain-attribute.service';
import {
  WidgetComponent,
  WidgetType,
  FilterCondition,
  WidgetBoard,
  WidgetFilterDefine,
  WidgetFilterDefineParams,
} from '../container/document/cbx-module/widget/model/widget-dto';
import { FieldBackgroundColorfulService } from './field-background-colorful.service';
import {
  OrderCocStatusNumberByWeekStackedChartComponent,
  OrderAmountByFactAuditResultPieChartComponent,
  OrderQuantityByCountryBarChartComponent,
  OrderNumberForApprovalComponent,
  OrderNumberByStatusDonutChartComponent,
  OrderQuantityByStatusAndWeekStackedChartComponent,
  OrderSummaryNumberComponent,
  OrderRequireInspectionByWeekBarChartComponent,
  ApparelInspectionDefectRateLineChartComponent,
  DocumentIssueRateByWeekAreaChartComponent,
  AlertValidationStatusByWeekStackedChartComponent,
  AlertDetectedNumberComponent,
  DocumentSubmissionByWeekLineChartComponent,
  FactAuditsSummaryByWeekStackedChartComponent,
  PendingProductSpecificationsByWeekBarChartComponent,
  OrderQuantityBySupplierBarChartComponent,
  PendingSampleEvaluationsByWeekBarChartComponent,
  QuotesNumberByWeekBarChartComponent,
  PlannedQuantityByCountryAndStatusBarChartComponent,
  QuotesNumberWithoutResponseByWeekBarChartComponent,
  PendingApprovalDetailComponent,
  CriticalPathManagementDetailComponent,
  SmartAlertDetailComponent,
  OrderShipmentQuantityByWeekStackedChartComponent,
  OrderShipmentAtRiskByWeekStackedChartComponent,
  ProductQuantityByDesignStatusDonutChartComponent,
  ProductQuantityByLineTypePieChartComponent,
  FactorySocialAuditRatingsByCountryStackedChartComponent,
  FactoryTechnicalAuditRatingsByCountryStackedChartComponent,
  VendorAuditRatingsByCountryStackedChartComponent,
  FactoryCertificationsBarChartComponent,
  VendorCertificationsBarChartComponent,
  DefectQuantityByQualityIssueBarChartComponent,
  OutstandingCapActionsNumberComponent,
  TraceabilityAlertsValidationByWeekStackedChartComponent,
  UpcomingAuditExpirationsByWeekBarChartComponent,
  CommunicationDetailComponent,
  CommunicationDocDetailComponent,
} from 'src/app/components/widget/widgetTypes';
import { FacilityComparedComponent } from 'src/app/components/widget/widgetTypes/facility-compared/facility-compared.component';
import { IrItemDateCalculateComponent } from 'src/app/components/widget/widgetTypes/ir-item-date-calculate/ir-item-date-calculate.component';
import { CBX_URL } from 'src/app/config/constant';
import { AnyObject } from 'src/app/interface/model';
import { ApiService } from 'src/app/services/api.service';
import { ConfigurationService } from 'src/app/services/configuration.service';
import { CONDITION_OPERATOR, CONDITION_SYMBOL } from 'src/app/services/search/search.service';
import { generateUUID, isArray } from 'src/app/utils';
import { deepClone, deepEqual, dotPath, isEmptyOrNil, isNotEmptyOrNil } from 'src/app/utils/general';

export interface WidgetComponentState {
  originalWidgetBoard: WidgetBoard;
  currentWidgetBoard: WidgetBoard;
  defaultWidgetTypeList: WidgetType[];
  highlightIndex: number;
}

export const formatDateInChart = (date: string) =>
  date === 'Overdue' ? 'Overdue' : isValid(new Date(date)) ? format(new Date(date), 'd MMM') : date;

export const getStartOfWeekDate = (targetDate: Date) =>
  format(startOfWeek(new Date(targetDate), { weekStartsOn: 1 }), 'yyyy/MM/dd');

export function getRangeOfWeek(targetDate: Date, days: number, isPrevious?: boolean) {
  if (isPrevious) {
    const subDate = sub(targetDate, { days });
    return `range(${format(subDate, 'yyyy/MM/dd')}-${format(targetDate, 'yyyy/MM/dd')})`;
  }

  const addedDate = add(targetDate, { days });
  return `range(${format(targetDate, 'yyyy/MM/dd')}-${format(addedDate, 'yyyy/MM/dd')})`;
}

@Injectable({
  providedIn: 'root',
})
export class WidgetService {
  router = inject(Router);
  private readonly apiService = inject(ApiService);
  private readonly fieldBackgroundColorfulService = inject(FieldBackgroundColorfulService);
  private readonly configurationService = inject(ConfigurationService);
  private readonly domainAttributeService = inject(DomainAttributeService);
  edit$ = new BehaviorSubject<boolean>(false);
  userName$ = new BehaviorSubject<string>(null);
  readonly defaultColorTokenSet = [
    'color-status-pale-blue',
    'color-status-sky',
    'color-status-green',
    'color-status-yellow',
    'color-status-purple',
    'color-status-orange',
    'color-status-salmon',
    'color-status-red',
    'color-status-mint',
    'color-status-magenta',
    'color-status-grey',
    'color-status-neutral',
  ];
  readonly widgetTypeComponentMap: {
    [type: string]: Constructor;
  } = {
    number: BaseNumberWidgetComponent,
    barCharts: BaseBarChartWidgetComponent,
  };

  readonly widgetComponentMap: {
    [key in Widget_WidgetType]: Constructor;
  } = {
    OrderChainOfCustodyStatusNumberByWeek: OrderCocStatusNumberByWeekStackedChartComponent, // 1
    OrderAmountByFactAuditResult: OrderAmountByFactAuditResultPieChartComponent, // 2
    OderQuantityByCountry: OrderQuantityByCountryBarChartComponent, // 3
    OrderNumberForApproval: OrderNumberForApprovalComponent, // 4
    OrderNumberByStatus: OrderNumberByStatusDonutChartComponent, // 5
    OrderQuantityByStatusAndWeek: OrderQuantityByStatusAndWeekStackedChartComponent, // 6
    OrderSummaryNumber: OrderSummaryNumberComponent, // 7
    OrderRequireInspectionByWeek: OrderRequireInspectionByWeekBarChartComponent, // 8
    ApparelInspectionDefectRate: ApparelInspectionDefectRateLineChartComponent, // 9

    DocumentIssueRateByWeek: DocumentIssueRateByWeekAreaChartComponent, // 15
    DocumentSubmissionByWeek: DocumentSubmissionByWeekLineChartComponent, // 16
    AlertValidationStatusByWeek: AlertValidationStatusByWeekStackedChartComponent, // 17
    AlertDetectedNumber: AlertDetectedNumberComponent, // 18
    FactAuditsSummaryByWeek: FactAuditsSummaryByWeekStackedChartComponent, // 19

    OrderQuantityBySupplier: OrderQuantityBySupplierBarChartComponent, // 24
    OrderShipmentQuantityByWeek: OrderShipmentQuantityByWeekStackedChartComponent, // 25
    OrderShipmentAtRiskByWeek: OrderShipmentAtRiskByWeekStackedChartComponent, // 26
    ProductQuantityByDesignStatus: ProductQuantityByDesignStatusDonutChartComponent, // 27
    ProductQuantityByLineType: ProductQuantityByLineTypePieChartComponent, // 28
    PendingProductSpecificationsByWeek: PendingProductSpecificationsByWeekBarChartComponent, // 29
    PendingSampleEvaluationsByWeek: PendingSampleEvaluationsByWeekBarChartComponent, // 31
    PlannedQuantityByCountryAndStatus: PlannedQuantityByCountryAndStatusBarChartComponent, // 32
    QuotesNumberByWeek: QuotesNumberByWeekBarChartComponent, // 33
    QuotesNumberWithoutResponseByWeek: QuotesNumberWithoutResponseByWeekBarChartComponent, // 34

    CriticalPathManagementDetail: CriticalPathManagementDetailComponent,
    PendingApprovalDetail: PendingApprovalDetailComponent,
    SmartAlertDetail: SmartAlertDetailComponent,

    FactorySocialAuditRatingsByCountry: FactorySocialAuditRatingsByCountryStackedChartComponent,
    FactoryTechnicalAuditRatingsByCountry: FactoryTechnicalAuditRatingsByCountryStackedChartComponent,

    VendorAuditRatingsByCountry: VendorAuditRatingsByCountryStackedChartComponent,

    FactoryCertifications: FactoryCertificationsBarChartComponent,
    VendorCertifications: VendorCertificationsBarChartComponent,
    DefectQuantityByQualityIssue: DefectQuantityByQualityIssueBarChartComponent,

    OutstandingCapActionsNumber: OutstandingCapActionsNumberComponent,
    TraceabilityAlertsValidationByWeek: TraceabilityAlertsValidationByWeekStackedChartComponent,
    UpcomingAuditExpirationsByWeek: UpcomingAuditExpirationsByWeekBarChartComponent,
    ShareKanban: ShareKanbanComponent,
    SharedStackBar: BaseBarChartWidgetComponent,
    SharedDateTracking: DateTrackingChartWidgetComponent,
    FilterWidget: FilterWidgetComponent,
    EmbeddedBI: EmbeddedBiWidgetComponent,

    IrItemDateCalculate: IrItemDateCalculateComponent,
    IB: FacilityComparedComponent,

    CommunicationDetail: CommunicationDetailComponent,
    CommunicationDocDetail: CommunicationDocDetailComponent,
  };
  monthsMap: { [key: string]: number } = {
    Jan: 1,
    Feb: 2,
    Mar: 3,
    Apr: 4,
    May: 5,
    Jun: 6,
    Jul: 7,
    Aug: 8,
    Sep: 9,
    Oct: 10,
    Nov: 11,
    Dec: 12,
  };
  private readonly customDataFilterMap: {
    [key in Widget_WidgetType]?: (data: any) => any;
  } = {
    ProductQuantityByLineType: this.customPieAndDonutChartDataFilter,
    OrderAmountByFactAuditResult: this.customDataFilterForOrderAmountByFactAuditResult,
    OrderNumberByStatus: this.customPieAndDonutChartDataFilter,
    ProductQuantityByDesignStatus: this.customPieAndDonutChartDataFilter,
    DefectQuantityByQualityIssue: this.customDataFilterForDefectQuantityByQualityIssue,
    OrderQuantityBySupplier: this.customDataFilterForOrderQuantityBySupplier,
    OderQuantityByCountry: this.customDataFilterForOderQuantityByCountry,
  };

  private readonly initState = {
    originalWidgetBoard: {} as WidgetBoard,
    currentWidgetBoard: {} as WidgetBoard,
    defaultWidgetTypeList: [] as WidgetType[],
    highlightIndex: null,
  };

  readonly store = new BehaviorSubject<WidgetComponentState>(this.initState);
  readonly state$ = this.store.asObservable();

  get state(): WidgetComponentState {
    return this.store.getValue();
  }

  currentWidgetBoard$: Observable<WidgetBoard> = this.state$.pipe(
    map(({ currentWidgetBoard }) => currentWidgetBoard),
    filter(isNotEmptyOrNil),
    distinctUntilChanged(deepEqual),
  );

  currentWidgetBoardName$: Observable<string> = this.currentWidgetBoard$.pipe(
    map(({ name }) => name ?? ''),
    distinctUntilChanged(),
  );

  highlightIndex$: Observable<number> = this.state$.pipe(
    map(({ highlightIndex }) => highlightIndex),
    distinctUntilChanged(deepEqual),
  );

  resetState() {
    this.updateState(this.initState);
  }

  updateOriginalWidgetBoard(originalWidgetBoard: WidgetBoard) {
    this.updateState({ ...this.state, originalWidgetBoard });
  }

  updateCurrentWidgetBoard(currentWidgetBoard: WidgetBoard) {
    this.updateState({ ...this.state, currentWidgetBoard });
  }

  updateWidgetTypeList(defaultWidgetTypeList: WidgetType[]) {
    this.updateState({ ...this.state, defaultWidgetTypeList });
  }

  updateCurrentWidgetBoardIsCreate(isCreate: boolean) {
    const currentWidgetBoard = { ...this.state.currentWidgetBoard, isCreate };
    this.updateCurrentWidgetBoard(currentWidgetBoard);
  }

  updateCurrentWidgetBoardVersion(version: number) {
    const currentWidgetBoard = { ...this.state.currentWidgetBoard, version };
    this.updateCurrentWidgetBoard(currentWidgetBoard);
  }

  updateBothOriginalAndCurrentWidgetBoard(widgetBoard: WidgetBoard) {
    this.updateOriginalWidgetBoard(deepClone(widgetBoard));
    this.updateCurrentWidgetBoard(deepClone(widgetBoard));
  }

  updateCurrentWidgetBoardName(name: string) {
    const currentWidgetBoard = { ...this.state.currentWidgetBoard, name };
    this.updateCurrentWidgetBoard(currentWidgetBoard);
  }

  updateCurrentWidgetComponentList(widgetComponentList: WidgetComponent[]) {
    const currentWidgetBoard = { ...this.state.currentWidgetBoard, widgetComponentList };
    this.updateCurrentWidgetBoard(currentWidgetBoard);
  }

  updateHighlightIndex(index: number, isHighlighted: boolean) {
    const highlightIndex = isHighlighted ? index : null;
    this.updateState({ ...this.state, highlightIndex });
  }

  updateCurrentWidgetHideWidgets(hideWidgets: boolean) {
    const currentWidgetBoard = { ...this.state.currentWidgetBoard, hideWidgets };
    this.updateCurrentWidgetBoard(currentWidgetBoard);
  }

  private updateState(group: WidgetComponentState) {
    this.store.next(group);
  }

  fetchWidgetBoard(boardId: string): Observable<WidgetBoard> {
    return this.apiService.get(CBX_URL.widgetBoardByBoardId({ boardId })).pipe(
      map((widgetBoard: WidgetBoard) => widgetBoard ?? ({ id: boardId, widgetComponentList: [] } as WidgetBoard)),
      catchError(() => of({ id: boardId, widgetComponentList: [] } as WidgetBoard)),
    );
  }

  fetchWidgetBoardByName(boardId: string, dashboardName: string): Observable<WidgetBoard> {
    const params = new HttpParams().set('dashboardName', dashboardName);
    return this.apiService.get(CBX_URL.widgetBoardByBoardIdAndName({ boardId }), { params }).pipe(
      map((widgetBoard: WidgetBoard) => widgetBoard ?? ({ id: boardId, widgetComponentList: [] } as WidgetBoard)),
      catchError(() => of({ id: boardId, widgetComponentList: [] } as WidgetBoard)),
    );
  }

  fetchDashboards(boardId: string): Observable<string[]> {
    return this.apiService.get<string[]>(CBX_URL.widgetDashBoardsByBoardId({ boardId }));
  }

  fetchDefaultWidgetBoardByName(boardId: string, dashboardName: string): Observable<WidgetBoard> {
    const params = new HttpParams().set('dashboardName', dashboardName);
    return this.apiService.get(CBX_URL.widgetBoardTemplatesByBoardIdAndName({ boardId }), { params }).pipe(
      map((widgetBoard: WidgetBoard) => widgetBoard ?? ({ id: boardId, widgetComponentList: [] } as WidgetBoard)),
      catchError(() => of({ id: boardId, widgetComponentList: [] } as WidgetBoard)),
    );
  }

  fetchDefaultWidgetBoard(boardId: string): Observable<WidgetBoard> {
    return this.apiService.get(CBX_URL.widgetBoardTemplatesByBoardId({ boardId })).pipe(
      map((widgetBoard: WidgetBoard) => widgetBoard ?? ({ id: boardId, widgetComponentList: [] } as WidgetBoard)),
      catchError(() => of({ id: boardId, widgetComponentList: [] } as WidgetBoard)),
    );
  }

  fetchWidgetDataByTypeAndFilter(type: string, filterData: AnyObject<any>) {
    const { dateRange, ...filters } = filterData;
    let payload = {};
    if (type === 'FilterWidget') {
      const rawData = deepClone(filterData);
      const filterDatas = Object.values(rawData);
      payload = { filterData: filterDatas };
    } else {
      Object.entries(filters).forEach(([key, value]) => {
        if (isArray(value)) {
          const result = value.map(({ code }) => code);
          payload = { ...payload, [key]: result };
        } else if (isNotEmptyOrNil(value)) {
          payload = { ...payload, [key]: value?.code };
        }
      });
    }

    const params = new HttpParams().set('dateRange', dateRange?.code ?? '');
    return this.domainAttributeService.useMockWidgetData$.pipe(
      take(1),
      switchMap((value) => {
        const mockTypeList = isNotEmptyOrNil(value) && value?.split(',');
        const isMockType =
          value === 'ALL' || (isArray(mockTypeList) && mockTypeList.some((mockType) => mockType === type));

        if (isMockType) {
          return this.apiService.get(`/assets/data/widget/data/${type}.json`);
        }

        return this.apiService.post(CBX_URL.widgetDataByType({ type }), payload, { params });
      }),
      map((data) => this.customFilterDataByType(type, data)),
      catchError(() => of(null)),
    );
  }

  deleteWidgetComponent(id: string) {
    return this.apiService.delete(CBX_URL.deleteWidgetComponent(id));
  }

  getWidgetDefaultView(): Observable<any> {
    return this.apiService.get(CBX_URL.getWidgetDefaultView());
  }

  saveWidgetDefaultView(viewName: string, dateRange: string): Observable<any> {
    const params = new HttpParams().set('dateRange', dateRange);
    return this.apiService.put<any>(CBX_URL.saveWidgetDefaultView({ viewName }), params);
  }

  fetchAllWidgetTypes(): Observable<WidgetType[]> {
    if (isNotEmptyOrNil(this.state.defaultWidgetTypeList)) {
      return of(this.state.defaultWidgetTypeList);
    }

    return this.apiService.get<WidgetType[]>(CBX_URL.allWidgetTypes).pipe(
      tap((widgetTypeList) => {
        this.updateWidgetTypeList(widgetTypeList);
      }),
    );
  }

  fetchWidgetTypeByTypeName(type: string): Observable<WidgetComponent> {
    return this.apiService.get(CBX_URL.widgetTypeByTypeName({ type }));
  }

  fetchWidgetColorMap(): Observable<AnyObject<AnyObject<AnyObject<string>>>> {
    if (this.configurationService.CONFIG.FLAG_APP_FIELD_BACKGROUND_COLORFUL) {
      return this.fieldBackgroundColorfulService.backgroundColorMap$.pipe(map((colorMap) => colorMap.widget ?? {}));
    }

    return of({});
  }

  fetchDefaultColor(index: number): string {
    return this.defaultColorTokenSet.map((token) =>
      window.getComputedStyle(document.body).getPropertyValue(`--${token}`),
    )[index % this.defaultColorTokenSet.length];
  }

  saveWidgetComponent(
    id: string,
    type: string,
    filterData: AnyObject<any>,
    boardId: string,
    category: string,
    title: string,
    defaultTab: string,
    defaultModule: string,
    defaultView: string,
  ) {
    const { ...filters } = filterData;
    const params = new HttpParams()
      .set('id', id ?? generateUUID())
      .set('boardId', boardId)
      .set('category', category)
      .set('title', title)
      .set('naviModuleId', defaultTab)
      .set('moduleId', defaultModule)
      .set('viewName', defaultView);
    return this.apiService.post(CBX_URL.widgetComponent({ type }), filters, { params });
  }

  fetchAllWidgetComponent(): Observable<any[]> {
    return this.apiService.get<any[]>(CBX_URL.allWidgetComponent);
  }

  handleFilterCondition(filters: AnyObject<any>, filterDefineList: WidgetFilterDefine[]): AnyObject<FilterCondition> {
    const filterDefineParamsMap = new Map<string, WidgetFilterDefineParams>();
    filterDefineList?.forEach(({ id, params }) => filterDefineParamsMap.set(id, params));

    let currentFilters: AnyObject<FilterCondition> = {};

    Object.entries(filters)
      .filter(([, value]) => isNotEmptyOrNil(value))
      .forEach(([key, value]) => {
        const defineParams = filterDefineParamsMap.get(key);
        const filterId = defineParams?.viewDefine?.id ?? key;

        if (defineParams?.ignoreInQuery) {
          return;
        }

        const result =
          key === 'dateRange'
            ? this.getDateRangeFilterValue(value?.code, defineParams)
            : this.getMappingValue(value, defineParams);

        currentFilters = { ...currentFilters, [filterId]: result };
      });

    return currentFilters;
  }

  private getDateRangeFilterValue(code: string, defineParams: WidgetFilterDefineParams): FilterCondition {
    if (isEmptyOrNil(code)) {
      return null;
    }

    if (code === 'ALL') {
      const isForwardType = defineParams?.dateRangeType === 'forward';
      const operator = isForwardType ? CONDITION_OPERATOR.greaterThan : CONDITION_OPERATOR.lessThan;
      const weekStartsOn = isForwardType ? 0 : 1;

      return {
        value: format(
          isForwardType
            ? startOfWeek(new Date(), { weekStartsOn })
            : add(endOfWeek(new Date(), { weekStartsOn }), { days: 1 }),
          'yyyy/MM/dd',
        ),
        operator,
      };
    }

    const getDateRange = (days: number, isPrevious: boolean) =>
      getRangeOfWeek(
        isPrevious ? endOfWeek(new Date(), { weekStartsOn: 1 }) : startOfWeek(new Date(), { weekStartsOn: 1 }),
        days * 7 - 1,
        isPrevious,
      );
    const dateRangeMap = {
      NXT2W: getDateRange(2, false),
      NXT4W: getDateRange(4, false),
      NXT6W: getDateRange(6, false),
      NXT8W: getDateRange(8, false),
      NXT12W: getDateRange(12, false),
      PST2W: getDateRange(2, true),
      PST4W: getDateRange(4, true),
      PST6W: getDateRange(6, true),
      PST8W: getDateRange(8, true),
      PST12W: getDateRange(12, true),
    };

    return { value: dateRangeMap[code] };
  }

  private getMappingValue(value: any, defineParams: WidgetFilterDefineParams): FilterCondition {
    const mapping = defineParams?.viewDefine?.mapping;
    const operator = CONDITION_OPERATOR[defineParams?.viewDefine?.operator ?? 'is'];
    if (isEmptyOrNil(mapping)) {
      return { value, operator };
    }

    if (isArray(value)) {
      return { value: value.map((data) => dotPath(mapping, data)), operator };
    }

    return { value: dotPath(mapping, value), operator };
  }

  directToWidgetDetailPage(
    viewUrl: string,
    filterCondition: AnyObject<FilterCondition>,
    isListingViewMode?: boolean,
    directCbxql?: string,
    dateRange?: string,
  ) {
    let cbxql = this.getCbxql(filterCondition);
    if (directCbxql && cbxql !== 'undefined=undefined') {
      cbxql = `${encodeURIComponent(directCbxql).replace(/%(?!3D)/g, '%25')}${CONDITION_SYMBOL.and}${cbxql}`;
    } else if (directCbxql && cbxql === 'undefined=undefined') {
      cbxql = `${encodeURIComponent(directCbxql).replace(/%(?!3D)/g, '%25')}`;
    }

    let link = dateRange
      ? `/listing/${viewUrl}?cbxql=${cbxql}&dateRange=${dateRange}`
      : `/listing/${viewUrl}?cbxql=${cbxql}`;
    if (cbxql === 'undefined=undefined' && dateRange) {
      link = link.replace('cbxql=undefined=undefined&', '');
    } else if (cbxql === 'undefined=undefined') {
      link = link.replace('?cbxql=undefined=undefined', '');
    }
    if (isListingViewMode) {
      this.router.navigateByUrl(link);
    } else {
      window.open(`${link}&widgetHidden=true`, '_blank');
    }
  }

  private getCbxql(filterCondition: AnyObject<FilterCondition>) {
    return Object.entries(filterCondition)
      .map(([key, { value, operator }]) => {
        const conditionOperator: CONDITION_OPERATOR = operator ?? CONDITION_OPERATOR.is;

        if (conditionOperator === CONDITION_OPERATOR.blank || conditionOperator === CONDITION_OPERATOR.notBlank) {
          return `${key} ${conditionOperator}`;
        }

        if (isArray(value)) {
          const encodeValueList = value.map((v) => encodeURIComponent(v).replace(/%/g, '%25'));

          // follow BLN-1331
          const needTransformConditionByIds = ['status', 'poStatus'];

          if (needTransformConditionByIds.includes(key)) {
            return encodeValueList.map((data) => `${key}${conditionOperator}${data}`).join(CONDITION_SYMBOL.globalOr);
          }

          return `${key}${conditionOperator}${encodeValueList.join(CONDITION_SYMBOL.or)}`;
        }

        return `${key}${conditionOperator}${encodeURIComponent(value).replace(/%/g, '%25')}`;
      })
      .join(CONDITION_SYMBOL.and);
  }

  saveWidgets(boardId: string) {
    return this.apiService.post(CBX_URL.widgetBoardByBoardId({ boardId }), this.state.currentWidgetBoard);
  }

  handleApplyAllWidgetsFilters(filters: AnyObject<any>, dateRangeType: 'previous' | 'forward') {
    const updatedWidgetBoard = this.state.currentWidgetBoard.widgetComponentList.map((component) => {
      const isSameDateRangeType =
        dateRangeType &&
        component.filterDefine?.find(({ id }) => id === 'dateRange')?.params?.dateRangeType === dateRangeType;

      const { dateRange, ...restFilters } = filters;
      const appliedFilters = isSameDateRangeType ? filters : restFilters;

      return {
        ...component,
        filters: { ...component.filters, ...appliedFilters },
      };
    });

    this.updateCurrentWidgetComponentList(updatedWidgetBoard);
  }

  private customFilterDataByType(type: string, data: any) {
    const customDataFilter = this.customDataFilterMap[type];

    if (isNil(customDataFilter)) {
      return data;
    }

    return customDataFilter(data);
  }

  private customPieAndDonutChartDataFilter(data: any[]): any[] {
    // Value cannot render in the pie chart if <= 0.
    return data?.filter((datum) => is(Number, datum?.value) && datum.value > 0);
  }

  private customDataFilterForOrderAmountByFactAuditResult(
    data: Widget_OrderAmountByFactAuditResultDto[],
  ): Widget_OrderAmountByFactAuditResultDto[] {
    // Value cannot render in the pie chart if <= 0.
    const auditResults = data[0]?.auditResults.filter(({ value }) => is(Number, value) && value > 0);

    if (auditResults.length > 0) {
      return [{ auditResults }];
    }
    return [];
  }

  private customDataFilterForDefectQuantityByQualityIssue(
    data: Widget_DefectQuantityByQualityIssueDto[],
  ): Widget_DefectQuantityByQualityIssueDto[] {
    return data?.filter(({ critical, major, minor }) => (critical ?? 0) + (major ?? 0) + (minor ?? 0) > 0);
  }

  private customDataFilterForOrderQuantityBySupplier(
    data: Widget_OrderQuantityBySupplierDataDto[],
  ): Widget_OrderQuantityBySupplierDataDto[] {
    return data?.filter(({ quantity }) => is(Number, quantity) && quantity > 0);
  }

  private customDataFilterForOderQuantityByCountry(
    data: Widget_OderQuantityByCountryDto[],
  ): Widget_OderQuantityByCountryDto[] {
    return data?.filter(({ country }) => is(Number, country?.value) && country.value > 0);
  }

  deleteWidgets() {
    return this.apiService.post(CBX_URL.deleteDashboard(), this.state.currentWidgetBoard);
  }

  saveWidgetsAndTempl(): Observable<WidgetBoard> {
    return this.apiService.post(CBX_URL.createWidgetBoardAndTemplByBoardId(), this.state.currentWidgetBoard);
  }

  getRenamedWidgets(originalWidgets: WidgetComponent[], selectedWidgets: any[]) {
    const titleMap = new Map();
    const selectedWidgetsTypeMap = new Map();

    originalWidgets.forEach((widget) => {
      titleMap.set(widget.title, true);
    });

    selectedWidgets.forEach((widget, index) => {
      selectedWidgetsTypeMap.set(widget.type, index);
    });

    originalWidgets.forEach((widget) => {
      const foundIndex = selectedWidgetsTypeMap.get(widget.type) ?? -1;

      if (foundIndex >= 0) {
        if (widget.title === selectedWidgets[foundIndex].title) {
          const uniqueTitle = this.getUniqueTitle(widget.title, titleMap);
          selectedWidgets[foundIndex].title = uniqueTitle;
          titleMap.set(uniqueTitle, true);
        } else {
          titleMap.set(selectedWidgets[foundIndex].title, true);
        }
      }
    });

    return selectedWidgets;
  }

  private getUniqueTitle(title, titleMap) {
    let uniqueTitle = title;
    let count = 0;

    while (titleMap.get(uniqueTitle)) {
      count += 1;
      uniqueTitle = `${title} (${count})`;
    }

    return uniqueTitle;
  }

  handleGroupBy(value: string, groupBy: string, type: string, from: string, to: string): any {
    if (!groupBy || groupBy === 'Day') {
      if (type === 'Date') {
        return { value: value?.replaceAll('-', '/'), operator: CONDITION_OPERATOR.is };
      }

      if (type === 'Datetime') {
        return { value: value?.split?.(' ')[0]?.replaceAll('-', '/'), operator: CONDITION_OPERATOR.is };
      }
    }

    if (groupBy === 'Week') {
      const yearSplit = value?.split('-');
      const year = yearSplit[0];
      const week = yearSplit[1]?.split('CW')?.[1];
      const { start, end } = this.getWeekStartAndEnd(Number(year), Number(week));
      value = `range(${format(start, 'yyyy/MM/dd')}-${format(end, 'yyyy/MM/dd')})`;
    }

    if (groupBy === 'Year') {
      value = `range(${value}/01/01-${value}/12/31)`;
    }

    if (groupBy === 'Month') {
      const yearSplit = value?.split('-');
      const year = yearSplit[0];
      const month = this.monthsMap[yearSplit[1]];
      const { start, end } = this.getMonthStartAndEnd(Number(year), Number(month));
      value = `range(${format(start, 'yyyy/MM/dd')}-${format(end, 'yyyy/MM/dd')})`;
    }

    if (groupBy === 'Quarter') {
      const yearSplit = value?.split('-');
      const year = yearSplit[0];
      const quarter = yearSplit[1];

      if (quarter === 'Q1') {
        value = `range(${year}/01/01-${year}/03/31)`;
      }
      if (quarter === 'Q2') {
        value = `range(${year}/04/01-${year}/06/30)`;
      }
      if (quarter === 'Q3') {
        value = `range(${year}/07/01-${year}/09/30)`;
      }
      if (quarter === 'Q4') {
        value = `range(${year}/10/01-${year}/12/31)`;
      }
    }

    if (groupBy === 'Custom' && (from || to)) {
      if (from && to) {
        value = `range(${from?.replaceAll('-', '/')}-${to?.replaceAll('-', '/')})`;
      } else if (from && !to) {
        value = `${from?.replaceAll('-', '/')}`;
        return { value, operator: CONDITION_OPERATOR.greaterThan };
      } else {
        value = `${to?.replaceAll('-', '/')}`;
        return { value, operator: CONDITION_OPERATOR.lessThan };
      }
    }

    return { value, operator: CONDITION_OPERATOR.is };
  }

  getWeekStartAndEnd(year: number, week: number): { start: Date; end: Date } {
    const firstDayOfYear = new Date(year, 0, 1);
    const dayOfWeek = firstDayOfYear.getDay() || 7;
    const firstMonday = firstDayOfYear.getTime() + (8 - dayOfWeek) * 24 * 60 * 60 * 1000;
    const start = new Date(firstMonday + (dayOfWeek <= 4 ? week - 2 : week - 1) * 7 * 24 * 60 * 60 * 1000);
    const end = new Date(start.getTime() + 6 * 24 * 60 * 60 * 1000);
    return { start, end };
  }

  getMonthStartAndEnd(year: number, month: number): { start: Date; end: Date } {
    const start = new Date(year, month - 1, 1);
    const end = new Date(year, month, 0);
    return { start, end };
  }
}
