import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { Component, ChangeDetectionStrategy, HostBinding, inject } from '@angular/core';

import { getTime } from 'date-fns';
import { DateLabelFormatPipe } from 'projects/chat/src/lib/pipe/date-label-format.pipe';
import * as R from 'ramda';
import { combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';

import { WidgetDirective } from '../../dynamic-widget/widget-message.directive';
import { Widget_CommunicationDocDetailDto } from 'src/app/entities/api';
import { DATE_TYPE } from 'src/app/entities/date-type';
import { AuthService } from 'src/app/services/auth.service';

@Component({
  selector: 'app-communication-doc-detail',
  templateUrl: './communication-doc-detail.component.html',
  styleUrls: ['./communication-doc-detail.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateLabelFormatPipe, NgIf],
})
export class CommunicationDocDetailComponent extends WidgetDirective<Widget_CommunicationDocDetailDto> {
  private readonly authService = inject(AuthService);

  @HostBinding('class') class = 'items-center justify-center';

  dateFormat$ = this.authService.dateFormat$;
  dateType = DATE_TYPE.datetime;

  sortedData$ = this.filteredData$.pipe(
    map((data) => R.sort((a, b) => getTime(new Date(b.publishedOn)) - getTime(new Date(a.publishedOn)), data)),
  );

  isViewAllButtonExist$ = combineLatest([this.rowSpan$, this.sortedData$]).pipe(
    map(([rowSpan, sortedData]) => (rowSpan === 2 ? sortedData.length > 5 : sortedData.length > 9)),
  );

  openCommunicationDoc(record: Widget_CommunicationDocDetailDto) {
    window.open(`/document/information/communicationDoc/${record.refNo}`, '_blank');
  }

  directToListing() {
    window.open(
      '/listing/information/communicationDoc/communicationDocActiveView?urlParams={creatorExclude=true}',
      '_blank',
    );
  }
}
