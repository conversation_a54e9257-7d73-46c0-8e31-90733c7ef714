import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { CdkDragEnter, CdkDropList, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';
import { Async<PERSON>ipe, NgFor, NgIf } from '@angular/common';
import { HttpResponse } from '@angular/common/http';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  HostBinding,
  Inject,
  Input,
  Renderer2,
  ViewChild,
  inject,
} from '@angular/core';

import { TranslocoModule } from '@ngneat/transloco';
import { UntilDestroy } from '@ngneat/until-destroy';
import { BehaviorSubject, Subject, map } from 'rxjs';

import { DropZoneComponent } from '../drop-zone/drop-zone.component';
import { FileViewerComponent } from '../file-viewer/file-viewer.component';
import { MovePopupIconComponent } from '../move-popup-icon.component';
import { UploadTaskComponent } from '../upload-task/upload-task.component';
import { IconComponent } from 'src/app/component/icon/icon.component';
import { UploadType } from 'src/app/entities/upload-type';
import { AttachmentPopupDialogData, AttachmentPopupDialogResult, DialogResult } from 'src/app/interface/model';
import { FileInfo, FileMetaData } from 'src/app/interface/model/file-info';
import { ConfigurationService } from 'src/app/services/configuration.service';
import { NotificationService } from 'src/app/services/notification.service';
import { immutableAddTo, immutableDelete, immutableSplice, isNotEmptyOrNil } from 'src/app/utils';
import { getFileExtension } from 'src/app/utils/file-util';

@UntilDestroy()
@Component({
  selector: 'app-attachment-popup-dialog',
  templateUrl: './attachment-popup-dialog.component.html',
  styleUrls: ['./attachment-popup-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    NgIf,
    NgFor,
    AsyncPipe,
    TranslocoModule,
    IconComponent,
    DropZoneComponent,
    DragDropModule,
    IconComponent,
    UploadTaskComponent,
    FileViewerComponent,
    MovePopupIconComponent,
  ],
})
export class AttachmentPopupDialogComponent implements AfterViewInit {
  @HostBinding('class') class = 'flex flex-column';

  private readonly configurationService = inject(ConfigurationService);
  private readonly notificationService = inject(NotificationService);
  private readonly cd = inject(ChangeDetectorRef);
  private readonly renderer = inject(Renderer2);
  private readonly dialogRef = inject(
    DialogRef<DialogResult<AttachmentPopupDialogResult>, AttachmentPopupDialogComponent>,
  );

  @ViewChild(CdkDropList) placeholder: CdkDropList;

  target: CdkDropList;
  targetIndex: number;
  source: CdkDropList;
  sourceIndex: number;

  limitationSize = this.configurationService.CONFIG.FILE_UPLOAD_LIMITATION ?? 10;
  @Input() sizeLimit = 2 ** 20 * this.limitationSize;

  fileToUpload: FileInfo[];

  attachmentList: FileInfo[];

  edit: boolean;

  uploadingCount$ = new BehaviorSubject<number>(0);
  disableDoneButton$ = this.uploadingCount$.pipe(map((count) => count > 0));

  stopUpload$: Subject<void> = new Subject<void>();

  constructor(@Inject(DIALOG_DATA) readonly data: AttachmentPopupDialogData) {
    this.edit = data.edit;

    const attachmentList = data.attachmentList ?? [];
    this.setAttachmentList(attachmentList);

    if (data.uploadFiles) {
      this.uploadFile(data.uploadFiles);
    }
  }

  private attachErrorMessage(file: File) {
    return {
      error: `${file.name} is greater than maximum upload size(${this.limitationSize}MB).`,
      file,
    };
  }

  private showError(errors: { file: File; error: string }[]) {
    const message = errors.reduce((acc, error) => `${acc}${error.error} \n\n`, '');
    this.notificationService.open({ message, type: 'warn' });
  }

  private validateFileSize(files: FileMetaData[]) {
    const invalidFiles: FileMetaData[] = [];
    const validFiles = files.filter((file) => {
      if (file.size < this.sizeLimit) {
        return true;
      }
      invalidFiles.push(file);
      return false;
    });
    if (invalidFiles.length !== 0) {
      const errors = invalidFiles.map((file) => this.attachErrorMessage(file));
      this.showError(errors);
    }
    return validFiles;
  }

  private setAttachmentList(value: FileInfo[]) {
    this.attachmentList = (value as FileInfo[])
      .filter((file) => isNotEmptyOrNil(file?.fileName))
      .map((file) => ({ ...file, fileExtension: getFileExtension(file.fileName) }));
  }

  uploadFile(fileList: ArrayLike<FileMetaData>) {
    const files = Array.from(fileList);

    const validFiles = this.validateFileSize(files);
    this.fileToUpload = validFiles?.map((file) => {
      this.uploadingCount$.next(this.uploadingCount$.value + 1);

      return {
        id: 'id',
        url: URL.createObjectURL(file),
        fileName: file.name,
        needUpload: true,
        fileToUpload: file,
        fileExtension: getFileExtension(file.name),
        updatedOn: file.lastModifiedDate,
        uploadType: UploadType.FILE,
      };
    });

    this.attachmentList = [...this.attachmentList, ...this.fileToUpload];
  }

  handleUploadSucceed(attachment: FileInfo, response: HttpResponse<any>, index: number) {
    const succeedAttachment = {
      ...attachment,
      id: response.body.id,
      file: {
        fileName: attachment.fileName,
        id: response.body.id,
        url: attachment.url,
        fileSize: response.body.fileSize,
      },
      fileSize: response.body.fileSize,
      needUpload: false,
    };

    this.attachmentList = immutableAddTo(this.attachmentList, succeedAttachment, index);

    this.uploadingCount$.next(this.uploadingCount$.value - 1);
  }

  handleUploadFailed(attachment: FileInfo, index: number) {
    const failedAttachment = {
      ...attachment,
      needUpload: false,
      uploadFailed: true,
    };

    this.attachmentList = immutableSplice(this.attachmentList, index, 1, failedAttachment);
  }

  handleRemoveFile(index: number) {
    const fileInfo = this.attachmentList[index];
    if (fileInfo?.uploadFailed) {
      this.uploadingCount$.next(this.uploadingCount$.value - 1);
    }
    this.attachmentList = immutableDelete(this.attachmentList, index);
  }

  ngAfterViewInit() {
    if (this.placeholder) {
      const placeHolderElement = this.placeholder.element.nativeElement;
      this.renderer.setStyle(placeHolderElement, 'display', 'none');
      this.renderer.removeChild(placeHolderElement.parentNode, placeHolderElement);
    }
  }

  drop() {
    if (!this.target) {
      return;
    }

    const placeHolderElement = this.placeholder.element.nativeElement;
    const parent = placeHolderElement.parentNode;

    this.renderer.setStyle(placeHolderElement, 'display', 'inline');

    this.renderer.removeChild(parent, placeHolderElement);

    this.renderer.insertBefore(parent, this.source.element.nativeElement, parent.children[this.sourceIndex]);

    if (this.sourceIndex !== this.targetIndex) {
      moveItemInArray(this.attachmentList, this.sourceIndex, this.targetIndex);
    }

    this.target = undefined;
    this.source = undefined;
    this.targetIndex = undefined;
    this.sourceIndex = undefined;

    this.cd.detectChanges();
  }

  enter(e: CdkDragEnter) {
    const drag = e.item;
    const drop = e.container;

    if (drop === this.placeholder) {
      return;
    }

    const placeHolderElement = this.placeholder.element.nativeElement;
    const dropElement = drop.element.nativeElement;

    const dragIndex =
      typeof this.sourceIndex === 'number'
        ? this.sourceIndex
        : this.getDropIndex(dropElement.parentNode.children, drag.dropContainer.element.nativeElement);

    const dropIndex = this.getDropIndex(dropElement.parentNode.children, dropElement);

    if (!this.source) {
      this.sourceIndex = dragIndex;
      this.source = drag.dropContainer;
      const sourceElement = this.source.element.nativeElement;

      this.renderer.removeChild(sourceElement.parentNode, sourceElement);
    }

    this.renderer.setStyle(placeHolderElement, 'display', '');

    if (this.sourceIndex < dropIndex) {
      if (dropIndex < this.targetIndex) {
        this.renderer.insertBefore(dropElement.parentElement!, placeHolderElement, dropElement);
      } else {
        this.renderer.insertBefore(dropElement.parentElement!, placeHolderElement, dropElement.nextSibling);
      }
    } else if (this.targetIndex < dropIndex) {
      this.renderer.insertBefore(dropElement.parentElement!, placeHolderElement, dropElement.nextSibling);
    } else {
      this.renderer.insertBefore(dropElement.parentElement!, placeHolderElement, dropElement);
    }

    this.targetIndex = dropIndex;
    this.target = drop;

    this.placeholder._dropListRef.enter(
      drag._dragRef,
      drag.element.nativeElement.offsetLeft,
      drag.element.nativeElement.offsetTop,
    );

    this.cd.detectChanges();
  }

  private getDropIndex(collection: any, node: any) {
    return Array.prototype.indexOf.call(collection, node);
  }

  onDone() {
    this.dialogRef.close({
      type: 'done',
      payload: { attachmentList: this.attachmentList },
    });
  }

  onCancel() {
    this.stopUpload$.next();

    this.dialogRef.close({
      type: 'cancel',
    });
  }
}
