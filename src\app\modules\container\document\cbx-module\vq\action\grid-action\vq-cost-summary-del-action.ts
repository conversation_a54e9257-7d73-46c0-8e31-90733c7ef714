import { Injectable } from '@angular/core';

import { untilDestroyed } from '@ngneat/until-destroy';
import { Observable, of } from 'rxjs';
import { take, tap } from 'rxjs/operators';

import { ApiService } from '../../../../../../../services/api.service';
import { AbstractGridUiAction } from '../../../../grid-section-content/grid-action/model/abstract-grid-ui-action';
import { DocumentDataService, DocumentDataState } from '../../../../state';
import { AnyObject } from 'src/app/interface/model';
import { MessageDialogService } from 'src/app/services/message-dialog.service';
import { isEmptyOrNil } from 'src/app/utils';

@Injectable()
export class VqCostSummaryDelAction extends AbstractGridUiAction {
  meesage = 'Are you sure you would like to delete the selected cost sheet(s)? Deleted cost sheets cannot be restored.';

  constructor(
    readonly documentDataService: DocumentDataService,
    protected readonly messageDialogService: MessageDialogService,
    private readonly apiService: ApiService,
  ) {
    super();
  }

  validate(): Observable<boolean> {
    return of(!!this.selectedRows.length);
  }

  process() {
    const selectedRows = this.selectedRows.reduce((acc, row) => ({ ...acc, [row.id]: true }), {});
    const rowData = this.rowData.filter((row) => !selectedRows[row.id]);
    this.messageDialogService
      .openYesNoDialog(this.meesage, 'warning')
      .closed.pipe(
        take(1),
        tap((result) => {
          if (result?.type === 'done') {
            this.documentDataService.updateData({ [this.sectionId]: rowData } as Partial<DocumentDataState>);
            this.documentDataService.rowChanges$.next({
              changes: this.selectedRows.map((row) => ({
                sectionId: this.sectionId,
                changeType: 'remove',
                rowId: row.id,
                rowIndex: this.rowData.findIndex(({ id }) => id === row.id),
              })),
            });
            return of(true);
          }
          return of(false);
        }),
        untilDestroyed(this),
      )
      .subscribe((result) => {
        if (result.payload) {
          const initCustList: AnyObject<any> = {};
          const deleteList: string[] = this.actionParams?.willDeleteList;
          if (isEmptyOrNil(deleteList)) {
            return;
          }
          deleteList.forEach((key) => {
            initCustList[key] = [];
          });
          this.documentDataService.updateData(initCustList as Partial<DocumentDataState>);
        }
      });
    return of();
  }
}
