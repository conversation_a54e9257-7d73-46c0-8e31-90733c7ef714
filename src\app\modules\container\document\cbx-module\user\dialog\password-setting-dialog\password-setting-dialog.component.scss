.info-icon {
  font-size: 18px;
  margin-left: 4px;
  color: var(--color-secondary-yellow);
  width: 18px;
  height: 18px;
}

.small-icon {
  width: 16px;
  height: 16px;
  font-size: 16px;
  object-fit: contain;
}

.section-container {
  padding-left: 12px;
}

.section-field-label {
  width: 180px;
  font-size: 12px;
  letter-spacing: 0.19px;
  color: var(--color-primary-gray);
  line-height: 18px;
  padding-bottom: 4px;

  &.require {
    &::after {
      content: '*';
      color: red;
      padding-left: 4px;
    }
  }

  &.edit {
    padding: 4px 10px;
  }
}

.section-field-value {
  flex: 0 0 55%;
  overflow: hidden;
  padding: 4px 10px;
  font-size: 12px;
  letter-spacing: 0.19px;
  color: var(--color-primary-black);

  &.edit {
    padding: 4px 10px;
  }
}

.label {
  min-height: 25px;
  margin-top: -1px;
}

.text-input {
  flex: 0 0 55%;
  overflow: hidden;
  border: solid 1px var(--color-secondary-gray-02);
  font-size: 12px;
  padding: 4px 10px;
  letter-spacing: 0.19px;
  color: #0c3b93;
  height: 26px;
}

.footer-button {
  margin: 16px 0 0 0;
  flex: 0 0 100%;

  &.right {
    margin-right: 20px;
  }
}

.action-button {
  background-color: #00a4bd;
  padding: 8px 16px;
  font-size: 16px;
  border-radius: 3px;
  margin: 12px 20px;
  cursor: pointer;
  &.disabled-button {
    background: #e0e0e0;
  }
}
