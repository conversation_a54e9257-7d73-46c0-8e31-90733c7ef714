import { BehaviorPreferenceScope } from '../interface/model/behavior-preference';
import { isEmptyOrNil, isNotNil } from '../utils';
import { FileTypes } from './file-types';
import { MODULE_ENDPOINT } from './module-endpoint';

export const DEFAULT_ROW_HEIGHT = 38;
export const DETAIL_ROW_HEIGHT = 52;

export const CBX_URL = {
  getLabels: ({ labelIds }: { labelIds: string[] }) => `api/labels/${(labelIds ?? []).join(',')}`,
  getLabelByLabelIdPrefix: (moduleId: string, labelIdPrefix: string) =>
    `api/labels/getByLabelPrefix/${moduleId}/${labelIdPrefix}`,
  getLabelsWithLocale: ({ labelIds, locale }: { labelIds: string[]; locale: string[] }) =>
    `/api/labels/${labelIds}/${locale}`,

  /**
   * concurrent */
  concurrentHistory: ({ moduleId, refNo }) => `api/concurrentEdit/documentHistory/${moduleId}/${refNo}`,

  concurrentHistoryByDateRange: ({ moduleId, refNo, dateRange }) =>
    `api/concurrentEdit/documentHistory/${moduleId}/${refNo}/${dateRange}`,

  concurrentSnapshotRecords: (refNo: string) => `api/concurrentEdit/document/snapshot/history/${refNo}`,

  concurrentSnapshotDocument: ({ moduleId, refNo, version }: { moduleId: string; refNo: string; version: string }) =>
    `api/concurrentEdit/document/snapshot/${moduleId}/${refNo}/${version}`,

  concurrentSnapshotSaveWithRemark: ({ moduleId, refNo }) =>
    `api/concurrentEdit/document/snapshot/${moduleId}/${refNo}`,

  concurrentValidate: ({ module }: { module: string }) => `/api/concurrentEdit/${module}/validate`,

  /**
   * concurrent  */

  // Module alias
  moduleAlias: ({ module }: { module: string }) => `api/${MODULE_ENDPOINT[module]}`,

  // user
  userAction: 'limitedAuth/users/unlock',
  getUserPasswordModified: 'limitedAuth/users/passwordModified',
  getPasswordPolicyByLoginId: 'limitedAuth/users/getPasswordPolicy',
  getPasswordPolicyByVc: 'api/noAuth/users/getPasswordPolicy',
  confirmAuthenticationCode: 'api/users/confirmAuthenticationCode',
  updateAuthenticationStatus: 'api/users/updateAuthenticationStatus',
  getIsNeedRegisterTwoFactorAuth: 'limitedAuth/users/IsNeedRegisterTwoFA',
  validatePasswordByLoginId: 'limitedAuth/users/validatePassword',

  restTwoFactorAuth: 'api/users/resetTwoFactorAuthentication',

  getPwdPolicyByVendorRegistToken: ({ token }) => `api/noAuth/users/getPasswordPolicy?type=registerId&code=${token}`,

  getUserById: ({ idParam }) => `api/users/id=${idParam}`,

  getWorkingForUserList: (userId) => `api/users/getWorkingForUserList/id=${userId}`,

  getUserByLoginIdOrRefNo: ({ loginIdOrRefNo }) => `api/users/${loginIdOrRefNo}`,

  getSelectUserSubstitution: (ids, effectiveFrom, effectiveTo) =>
    `api/users/getSelectUserSubstitution/ids=${ids}/effectiveFrom=${effectiveFrom}/effectiveTo=${effectiveTo}`,

  getCurrentUserSubstitution: (loginId, effectiveFrom, effectiveTo) =>
    `api/users/getCurrentUserSubstitution/loginId=${loginId}/effectiveFrom=${effectiveFrom}/effectiveTo=${effectiveTo}`,

  getDaysOfValidTimeOfPassword: ({ loginId }) => `limitedAuth/users/getDaysOfValidTimeOfPassword?loginId=${loginId}`,

  getUserNextLoginMustChangePassword: ({ loginId }) =>
    `limitedAuth/users/getUserNextLoginMustChangePassword?loginId=${loginId}`,

  getActiveSsoUsersWithSameSsoUserId: ({ ssoUserId }) =>
    `api/sso/getActiveSsoUsersWithSameSsoUserId?ssoUserId=${ssoUserId}`,

  auditSsoUser: ({ ssoUserId }) => `api/users/auditSsoUser?ssoUserId=${ssoUserId}`,

  // current user
  userCurrent: 'api/users/current',

  // unsubscribe email
  unsubscribe: ({ unsubscribeInfo }) => `api/users/unsubscribe/unsubscribeInfo=${unsubscribeInfo}`,

  getCurrentUserType: 'api/users/getCurrentUserType',

  currentUserIsResponsibleByMe: ({ isResponsibleByMe }) =>
    `api/users/currentUserIsResponsibleByMe?isResponsibleByMe=${isResponsibleByMe}`,

  currentUserShowProposedChanges: 'api/users/currentUserShowProposedChanges',

  currentUserShowTerminatedExpired: 'api/users/currentUserShowTerminatedExpired',

  /**
   * forget / reset password
   */

  forgotPassword: `api/noAuth/users/forgetPassword`,

  isVcValid: `api/noAuth/users/isVcValid`,

  resetPassword: `api/noAuth/users/resetPassword`,

  changePassword: `limitedAuth/users/changePassword`,

  getAuthCode: `api/noAuth/authCode`,
  confirmAuthCode: `limitedAuth/users/confirmAuthCode`,

  /**
   * User-Preference
   */
  userPreference: 'api/users/me',

  /**
   * switch user
   */
  switchUser: (domainId: string) => `api/users/switchUser?domainId=${domainId}`,

  /**
   * get switch user list
   */
  getSwitchUserList: (userId: string) => `api/users/getSwitchUserList/id=${userId}`,

  /**
   * check switch vendor is active or not
   */
  checkSwitchVendor: (domainId: string) => `api/users/checkSwitchVendor/domainId=${domainId}`,

  /**
   * set Client Secret Data
   */
  setClientSecretData: (refNo: string, field: string) => `api/users/setClientSecretData/${refNo}/${field}`,

  /**
   * User Behavior Preference
   */

  behaviorPreferences: (scope: BehaviorPreferenceScope) => `api/behaviorPreferences/${scope}`,

  /**
   * My Recent Items
   */
  myRecentItems: '/api/recentaccess/loadrecentitems',

  qqAdoptAsNewItem: `api/qqs/batchAdoptAsNewItem`,

  /**
   * Password Validation (CBX9-1723)
   */

  validatePasswordWithAuth: 'api/users/password?action=validate',
  validatePasswordWithLoginId: (loginId: string) => `api/noAuth/users/${loginId}/password?action=validate`,
  validatePasswordByVc: (vc: string) => `api/noAuth/users/validatePassword/${vc}`,
  validatePasswordForVendorRegistration: 'api/noAuth/users/password?action=validate',

  getFileDetailPopupMode: '/api/file/popup/getPopupMode',
  setFileDetailPopupMode: (mode: string) => `/api/file/popup/setPopupMode?mode=${mode}`,

  updateUserPassword: 'api/users/updateUserPassword',

  /**
   * Navigation
   */
  navigation: 'api/navigation',

  navigationMenu: `api/navigation/menu`,

  getAllView: 'api/navigation/getAllView',

  navigationByDomain: 'api/navigation/menuByUserDomain',

  navigationBookmarkByDomain: 'api/navigation/bookmarkMenuByUserDomain',

  navigationHomePageWidgets: `api/navigation/homePageWidgets`,

  naviEntry: 'api/naviEntries',
  getNaviEntriesByUser: 'api/naviEntries/naviEntriesByUser',
  deleteNaviEntry: 'api/naviEntries/record',
  addBookmarkBar: 'api/naviEntries/addBookmarkBar',
  removeBookmarkBar: 'api/naviEntries/removeBookmarkBar',

  jasperReportUrl: 'api/navigation/jasperReport/url',

  importResult: 'api/import/upload',

  uploadEntityIdFormatExcel: (moduleId: string) => `/api/upload_entity_id_format/upload/${moduleId}`,

  uploadUserFormatExcel: () => `/api/user_format/upload`,

  uploadConfigurationUrl: (moduleId: string) => `/api/files/uploadConfiguration/${moduleId}`,

  uploadLookupExcel: (refNo: string) => `/api/lookupLists/uploadLookup?refNo=${refNo}`,

  uploadCodelistExcel: (refNo: string) => `/api/codelists/uploadCodelist?refNo=${refNo}`,
  exportCodelistExcel: ({ refNo }: { refNo: string }) => `api/codelists/downloadCodelist/${refNo}`,

  downloadAdminModuleConfiguration: (moduleId: string, refNos: string[]) =>
    `api/files/downloadConfiguration?refNos=${refNos}&module=${moduleId}`,

  downloadAllAdminModuleConfig: () => `/api/batchexport/export`,

  clearSystemRedisCache: () => `/api/cache/evictAllCache`,

  ticUploadFile: (type: FileTypes) => `api/result/upload?type=${type}`,

  navigationForRelatedDoc: ({ moduleId, docRefNo }: { moduleId: string; docRefNo: string }) =>
    `api/navigation/related/${moduleId}?refNo=${docRefNo}`,

  relatedDocTotalAmount: ({ moduleId, viewName, docRefNo }: { moduleId: string; viewName: string; docRefNo: string }) =>
    `api/navigation/related/${moduleId}/${viewName}/total?q=${docRefNo}`,

  setAsDefaultView: ({ navigationId, menuGroupId, menuItemId }) =>
    `api/navigation/${navigationId}/menuGroups/${menuGroupId}/menuItems/${menuItemId}`,

  // careInstruction
  fetchCareInstruction: '/api/careInstructions/popup',

  /**
   * Codelist
   */
  codelistByRefNo: ({ refNo }: { refNo: string }) => `api/codelists/${refNo}`,

  codelistByName: ({ name }: { name: string }) => `api/codelists/name/${name}`,

  getDefaultModule: ({ userId }: { userId: string }) => `api/codelists/getUserDefaultModule/${userId}`,

  fetchCodelistOptions: ({ moduleId, viewName }: { moduleId: string; viewName: string }) =>
    `api/${moduleId}/view/${viewName}/options`,

  codelists: 'api/codelists',

  saveCodelist: 'api/codelists/saveCodelist',

  dynamicBuildCodelist: ({ name }: { name: string }) => `api/codelists/dynamicCodelist/${name}`,

  /**
   * Hcl
   */
  hclByRefNo: ({ refNo }: { refNo: string }) => `api/hcls/${refNo}`,

  hclByName: ({ name }: { name: string }) => `api/hcls/name/${name}`,

  hcls: 'api/hcls',

  /**
   * markerjs
   */
  markerjsById: ({ id }: { id: string }) => `api/markerjs/${id}`,

  markerjs: 'api/markerjs',

  imageLeafletCommentByImageId: ({ imageId }: { imageId: string }) => `api/imageLeafletComment/imageId/${imageId}`,
  imageLeafletCommentById: ({ id }: { id: string }) => `api/imageLeafletComment/${id}`,
  imageLeafletCommentByImageIds: 'api/imageLeafletComment/imageIds',
  deleteImageLeafletCommentByImageIds: 'api/imageLeafletComment/delete/imageIds',
  imageLeafletComment: 'api/imageLeafletComment',

  lookupListByRefNo: ({ refNo }: { refNo: string }) => `api/lookupLists/${refNo}`,

  lookupListByName: ({ name }: { name: string }) => `api/lookupLists/name/${name}`,

  lookupSystemAnnouncement: `api/lookupLists/announcement`,

  // Search by viewName
  searchByViewName: ({ module, viewName }: { module: string; viewName: string }) => `api/${module}/view/${viewName}`,

  vpoListAllIsLatest: (paramsData: any[]) => `api/purchase_orders/vpoListAllIsLatest/${paramsData}`,

  /**
   * Listing module
   */
  viewSetting: 'api/views',

  view: ({ viewName }) => `${CBX_URL.viewSetting}/v2/${viewName}`,

  bookmarkView: 'api/views/bookmark',
  editBookmarkView: 'api/views/editBookmarkView',
  updateBookmarkView: 'api/views/updateBookmark',
  loadBookMarkView: 'api/views/loadBookmarkId',

  customViewShareWithList: ({ viewName }) => `${CBX_URL.viewSetting}/getCustomViewShareWithList/${viewName}`,

  getPersonalizeViewOptionsMap: ({ viewId }) => `${CBX_URL.viewSetting}/getPersonalizeViewOptionsMap/${viewId}`,

  createOrUpdateView: 'api/views/createOrUpdateView',

  deleteView: 'api/views/deleteView',

  exportView: 'api/views/exportView',

  listingRecord: ({ module, viewName }: { module: string; viewName: string }) =>
    CBX_URL.searchByViewName({ module, viewName }),

  listingCpmRecord: `api/cpm/loadView/cpmMilestones`,

  updateCpmMilestones: 'api/cpm/update/cpmMilestones',
  updateCpmMilestonesForBatchUpdate: 'api/cpm/batchUpdate/cpmMilestones',
  batchUpdateCpmMilestones: 'api/cpm/batchUpdate/cpmDocs',

  updateCpmIsSubTaskOn: 'api/cpm/update/cpmIsSubTaskOn',
  loadCpmIsSubTaskOn: 'api/cpm/load/cpmIsSubTaskOn',

  auditSchedule: 'api/factory_audits',
  findAuditByPlannedAuditDateRange: 'api/factory_audits/findAuditByPlannedAuditDateRange',
  auditBatchConfirmSchedule: 'api/factory_audits/batchConfirmSchedule',

  auditBatchAutoSchedule: 'api/factory_audits/batchAutoSchedule',

  addFactAuditScheduleLock: 'api/factory_audits/addFactAuditScheduleLock',

  getFactAuditScheduleLock: 'api/factory_audits/getFactAuditScheduleLock',

  getAuditTemplate: 'api/factory_audits/getAuditTemplate',

  loadCpmEndToEndViewReferenceData: ({ moduleId, viewName }: { moduleId: string; viewName: string }) =>
    `api/cpm/loadCpmEndToEndViewReferenceData/${moduleId}/${viewName}`,

  loadCpmEndToEndView: ({ viewName }) => `api/cpmEndToEnd/loadCpmEndToEndView/${viewName}`,
  patchCpmEndToEndView: 'api/cpmEndToEnd/patchCpmEndToEndView',

  getIsAllowEdit: 'api/cpmEndToEnd/getIsAllowEdit',

  inspectionBookingSchedule: 'api/inspect_bookings',

  findInspectBookingByPlannedInspectDateRange: 'api/inspect_bookings/findInspectBookingByPlannedInspectDateRange',

  getUsersByGroupName: 'api/users/getUsersByGroupName',

  getSimpleUsersByGroupName: 'api/users/getSimpleUsersByGroupName',

  inspectBookingBatchConfirmSchedule: 'api/inspect_bookings/batchConfirmSchedule',

  batchUpdateInspectBooking: 'api/inspect_bookings/batchUpdateInspectBooking',

  checkFactoryLastConsecutiveVisit: 'api/inspect_bookings/checkFactoryLastConsecutiveVisit',

  inspectBookingBatchAutoSchedule: 'api/inspect_bookings/batchAutoSchedule',

  addInspectionBookingScheduleLock: 'api/inspect_bookings/addInspectionBookingScheduleLock',

  getInspectionBookingScheduleLock: 'api/inspect_bookings/getInspectionBookingScheduleLock',

  getNaviModuleMap: `api/docIntegrationHistories/naviModuleMap`,

  batchUpdateItemAttachments: 'api/items/batchUpdateItemAttachment',

  getCustomFieldTemplateByFieldId: (fieldId: string) => `api/formBuilders/customFieldTemplate/${fieldId}`,

  startImport: 'api/imports/startImport',

  validateMapping: 'api/imports/validateMapping',

  /**
   * Document module
   */
  documentForm: ({ module }: { module: string }) => `api/forms/${module}`,

  documentFormDefine: ({ module }: { module: string }) => `api/forms/${module}/define`,

  formDefineForFormBuilder: ({ module }: { module: string }) => `api/forms/formBuilder/${module}/define`,

  updateFormDefine: ({ module }: { module: string }) => `api/forms/${module}/updateDefine`,

  popupDefine: (popupId: string, params?: string) => `api/popups/${popupId}/define/${params || ''}`,

  documentFormTemplate: ({ module }: { module: string }) => `api/forms/${module}/template`,

  getDataAbstract: (module: string, refNo: string, version: number) =>
    `api/forms/getDataAbstract/${module}/${refNo}/${version}`,

  getDataAbstractByModuleAndLevel: (module: string, level: string) =>
    `api/data_abstract_config/getDataAbstract/${module}/${level}`,

  createPackingList: (refNos: string) => `/api/packingLists/createPackingList?refNos=${refNos}`,

  createFormPackingList: (vpoNos: string) => `/api/packingLists/createFormPackingList?vpoNos=${vpoNos}`,

  createVPOList: (id: string) => `/api/purchase_orders/createChainOfCustody?id=${id}`,

  batchUpdateChainOfCustody: '/api/purchase_orders/batchUpdateChainOfCustody',

  updateGridVisibleAndOrder: '/api/purchase_orders/updateGridVisibleAndOrder',

  documentData: ({ module, refNo, version = '' }: { module: string; refNo: string; version?: string }) =>
    isEmptyOrNil(version)
      ? `${CBX_URL.moduleAlias({ module })}/${refNo}`
      : `${CBX_URL.moduleAlias({ module })}/${refNo}/${version}`,

  hierarchyData: ({ module }: { module: string }) => `${CBX_URL.moduleAlias({ module })}/relatedHierarchy`,

  documentDataAccessiable: ({ module, refNo, version = '' }: { module: string; refNo: string; version?: string }) =>
    isEmptyOrNil(version)
      ? `api/dataAccessible/${module}?refNo=${refNo}`
      : `api/dataAccessible/${module}?refNo=${refNo}&version=${version}`,

  lineItem: ({ module }: { module: string }) => `api/lineItem/${module}`,

  upload: (type: FileTypes, subPath?: string, uploadLimitedType?: string, isThumbnail?: boolean) => {
    const subPathParam = subPath ? `&subPath=${subPath}` : '';
    const uploadLimitedTypeParam = uploadLimitedType ? `&uploadLimitedType=${uploadLimitedType}` : '';
    const thumbnail = isThumbnail ? `&isThumbnail=${isThumbnail}` : '';
    return `api/files/upload?type=${type}${subPathParam}${uploadLimitedTypeParam}${thumbnail}`;
  },

  uploadOriginalAndThumbnailFile: (type: FileTypes, subPath?: string) => {
    const subPathParam = subPath ? `&subPath=${subPath}` : '';
    return `api/files/uploadOriginalAndThumbnailFile?type=${type}${subPathParam}`;
  },

  download: 'api/files/download',

  loadImage: 'api/files/loadImage',

  loadFiles: 'api/files',

  loadImageByOriginalId: ({ originalId }: { originalId: string }) => `api/files/loadImageByOriginalId/${originalId}`,

  inheritance: 'api/inheritance',

  deleteGridColVisibleOrderByGridId: `/api/gridColumns/deletedGridColVisibleOrder`,

  newDocWithInheritance: ({ module }: { module: string }) => `api/newDoc/inheritance/${module}`,

  newDocWithLineItem: ({ module }: { module: string }) => `api/newDoc/lineItem/${module}`,

  newLineDefaultValue: ({ module, sectionId }: { module: string; sectionId: string }) =>
    `api/lineItem/defaultValues/${module}/${sectionId}`,

  sectionDefaultValues: ({
    module,
    sectionId,
    entityName,
  }: {
    module: string;
    sectionId: string;
    entityName: string;
  }) => `api/lineItem/defaultValues/${module}/${sectionId}/${entityName}`,

  moduleDefaultValues: ({ module }: { module: string }) => `api/lineItem/defaultValues/${module}`,

  // grid
  gridColVisibleOrderView: 'api/gridColumns/createOrUpdate',
  exportGridColsOrder: 'api/gridColumns/exportGridCols',

  gridSectionDefaultDefine: ({
    moduleId,
    refNo,
    version,
    sectionId,
  }: {
    moduleId: string;
    refNo: string;
    version: string;
    sectionId: string;
  }) => {
    const queryParams = [];

    if (isNotNil(refNo)) {
      queryParams.push(`refNo=${refNo}`);
    }

    if (isNotNil(version)) {
      queryParams.push(`version=${version}`);
    }

    return `api/gridColumns/getSectionDefine/${moduleId}/${sectionId}?${queryParams.join('&')}`;
  },

  getGridColumns: ({ formId, entityName }: { formId: string; entityName: string }) =>
    `api/gridColumns/getGridColumns/${formId}/${entityName}`,

  popupSectionDefaultDefine: (popupId: string, sectionId: string, params?: string) => {
    if (params) {
      return `api/gridColumns/getPopupSectionDefine/${popupId}/${sectionId}?params=${params}`;
    }
    return `api/gridColumns/getPopupSectionDefine/${popupId}/${sectionId}`;
  },

  newUiUrl: 'api/newUiUrl',

  /**
   *Widget
   */

  widgetBoardByBoardId: ({ boardId }: { boardId: string }) => `api/widgets/boards/${boardId}`,

  widgetBoardTemplatesByBoardId: ({ boardId }: { boardId: string }) => `api/widgets/boardTemplates/${boardId}`,

  widgetDataByType: ({ type }: { type: string }) => `api/widgets/${type}`,
  deleteWidgetComponent: (id: string) => `api/widgets/widgetComponent/delete/${id}`,
  allWidgetTypes: 'api/widgets/types',
  widgetViewCbxql: 'api/widgets/widgetViewCbxql',
  widgetTypeByTypeName: ({ type }: { type: string }) => `api/widgets/types/${type}`,
  deleteDashboard: () => `api/widgets/deleteDashboard`,

  widgetBoardByBoardIdAndName: ({ boardId }: { boardId: string }) => `api/widgets/boards/dashboardName/${boardId}`,

  createWidgetBoardAndTemplByBoardId: () => `api/widgets/saveWidgetTemplate`,
  widgetDashBoardsByBoardId: ({ boardId }: { boardId: string }) => `api/widgets/dashboards/${boardId}`,

  widgetBoardTemplatesByBoardIdAndName: ({ boardId }: { boardId: string }) =>
    `api/widgets/boardTemplates/dashboardName/${boardId}`,
  widgetComponent: ({ type }: { type: string }) => `api/widgets/widgetComponent/${type}`,
  allWidgetComponent: 'api/widgets/findAllWidgetComponent',
  saveWidgetDefaultView: ({ viewName }: { viewName: string }) => `api/widgets/widgetDefaultView/${viewName}`,
  getWidgetDefaultView: () => `api/widgets/widgetDefaultView`,

  /**
   *Alert
   */
  listingAlert: (module: string) => `api/crossModuleAlert/listing/${module}`,

  documentAlert: ({ module, refNo }: { module: string; refNo: string }) =>
    `/api/crossModuleAlert/document/${module}/${refNo}`,

  /**
   * Templates
   */
  // Vendor Template
  agreementTemplates: 'api/agreementTemplates',

  // Sample Request Template
  sampleRequestTemplates: 'api/sampleRequestTemplates',
  materialRequestTemplates: 'api/materialRequestTemplates',
  documentRequestTemplates: 'api/documentRequestTemplates',

  // Sample Evaluation Template
  evaluationTemplatesList: 'api/evaluationTemplates/byDocApplyToName',
  evaluationUpdateToItem: ({ sectionId }: { sectionId: string }) => `api/sample_evaluations/${sectionId}/updateToItem`,
  evaluationPrintTag: 'api/sample_evaluations/exportSampleTag',

  // Test Report
  testReportPrintTag: 'api/testReports/exportSampleTag',

  // Cost Calculation Template
  costTemplApplyToList: 'api/costTempls/dropdownData/applyToStore',
  costTemplBaseOnList: ({ applyCode }: { applyCode: string }) =>
    `api/costTempls/dropdownData/dateEntityFieldStore?applyTo=${applyCode}`,
  costTemplSourceListOfBasisList: ({ applyCode, applyLevelCode }: { applyCode: string; applyLevelCode: string }) =>
    `api/costTempls/dropdownData/numberEntityFieldStore?applyTo=${applyCode}&applyLevel=${applyLevelCode}`,
  costTemplSourceListOfElementList: ({
    applyCode,
    applyLevelCode,
    sourceTypeCode,
  }: {
    applyCode: string;
    applyLevelCode: string;
    sourceTypeCode: string;
  }) =>
    `api/costTempls/dropdownData/sourceStore?applyTo=${applyCode}&applyLevel=${applyLevelCode}&sourceType=${sourceTypeCode}`,

  /**
   * Right hand panel
   */
  notes: `api/notes`,

  noteById: ({ id }: { id: string }) => `${CBX_URL.notes}/${id}`,

  tags: `api/tags`,

  parties: 'api/parties',

  partyTemplate: 'api/partyTemplates',

  docStatusHistories: 'api/documentStatusHistories',

  /**
   * CPM
   */
  cpmDashboardInfo: 'api/cpm/dashboard',

  loadByDocRefNo: '/api/cpm/loadByDocRefNo',

  defaultItemSourcingRecord: '/api/sourcing_records/defaultItemSourcingRecord',

  popAssigneesView: 'api/popAssigneesView/loadView',

  popAssigneesViewWithoutParties: `api/popAssigneesView/loadView?needParties=false`,

  popAssigneesViewByUserOrGroupName: ({ userOrGroupName }: { userOrGroupName: string }) =>
    `api/popAssigneesView/search?userOrGroupName=${userOrGroupName}`,

  popAssigneesViewByUserOrGroupNameWithoutParties: ({ userOrGroupName }: { userOrGroupName: string }) =>
    `api/popAssigneesView/search?userOrGroupName=${userOrGroupName}&needParties=false`,

  // cpmDashboardInfo2: ({ page }) => `/api/cpm/dashboard/?page=${page}`,

  initializedCpmDoc: ({ entityName, refNo }: { entityName: string; refNo: string }) =>
    `api/cpm/init/${entityName}/${refNo}`,

  getRealDocRefNo: ({ module, docId }: { module: string; docId: string }) =>
    `/api/recentaccess/beforeopen/${module}/${docId}`,

  insertMyRecentItemLatest: ({ module, refNo }: { module: string; refNo: string }) =>
    `/api/recentaccess/insertmyrecentitem/${module}/${refNo}`,
  insertMyRecentItem: ({ module, refNo, version }: { module: string; refNo: string; version: string }) =>
    `/api/recentaccess/insertmyrecentitem/${module}/${refNo}/${version}`,

  reInitializedCpmDoc: ({
    module,
    refNo,
    templateId,
    isKeepExistingTasks,
    isChangeAnchoreDate,
    anchoreDate,
    isUpdateTask,
    isRegenerate,
    selectedRefDocRefNos,
  }: {
    module: string;
    refNo: string;
    templateId: string;
    isKeepExistingTasks: string;
    isChangeAnchoreDate: string;
    anchoreDate: string;
    isUpdateTask: string;
    isRegenerate: string;
    selectedRefDocRefNos: string;
  }) =>
    `api/cpm/reinitialize/${module}/${refNo}?templateId=${templateId}&isKeepExistingTasks=${isKeepExistingTasks}&isChangeAnchoreDate=${isChangeAnchoreDate}&anchoreDate=${anchoreDate}&applyPastEvents=${isUpdateTask}&generateNotification=${isRegenerate}&selectedRefDocRefNos=${selectedRefDocRefNos}`,

  reCalculatePlanDate: ({
    module,
    refNo,
    calculationType,
    selectedRefDocRefNos,
  }: {
    module: string;
    refNo: string;
    calculationType: string;
    selectedRefDocRefNos: string;
  }) =>
    `api/cpm/recalculateCpmPlanDate/${module}/${refNo}?calculationType=${calculationType}&selectedRefDocRefNos=${selectedRefDocRefNos}`,

  docCpmMilestone: ({ module, refNo }: { module: string; refNo: string }) =>
    `api/cpm/loadDoc/cpmMilestones/${module}/${refNo}`,

  loadUpstreamCpmMilestones: ({ module, refNo }: { module: string; refNo: string }) =>
    `api/cpm/loadUpstreamCpmMilestones/${module}/${refNo}`,

  docCpmMilestoneIfAssgin: ({
    module,
    refNo,
    isAssignedToME,
  }: {
    module: string;
    refNo: string;
    isAssignedToME: string;
  }) => `api/cpm/loadDoc/cpmMilestones/${module}/${refNo}?isAssignedToME=${isAssignedToME}`,

  allCpmTemplateForDoc: ({
    moduleId,
    refNo,
    searchParams,
  }: {
    moduleId: string;
    refNo: string;
    searchParams: string;
  }) => `api/cpm/loadAllCpmTemplateForDoc/${moduleId}/${refNo}?searchParams=${searchParams}`,

  /**
   * Actions
   */
  updateDoc: ({ module, action }: { module: string; action: string }) =>
    `${CBX_URL.moduleAlias({ module })}?action=${action}`,

  applyAgreement: ({ module, action }: { module: string; action: string }) =>
    `${CBX_URL.moduleAlias({ module })}/applyDefaultAgreementToVendor?action=${action}`,

  setToInDoc: ({
    module,
    refNo,
    docStatus,
    version,
  }: {
    module: string;
    refNo: string;
    docStatus: string;
    version: string;
  }) => `${CBX_URL.moduleAlias({ module })}/${refNo}?docStatus=${docStatus}&version=${version}`,

  markAsInDoc: ({
    module,
    refNo,
    status,
    version,
  }: {
    module: string;
    refNo: string;
    status: string;
    version: string;
  }) => `${CBX_URL.moduleAlias({ module })}/${refNo}?status=${status}&version=${version}`,

  markAsDocWithLinkedDoc: ({ module, status }: { module: string; status: string }) =>
    `${CBX_URL.moduleAlias({ module })}/markAsDocWithLinkedDoc?status=${status}`,

  findLinkedDocument: (module: string, refNo: string) => `/api/approvalProfiles/findLinkedDocument/${module}/${refNo}`,

  customActionInDoc: ({ module, refNo }: { module: string; refNo: string }) =>
    `${CBX_URL.moduleAlias({ module })}/customAction/${refNo}`,

  setToInListing: ({ module, docStatus }: { module: string; docStatus: string }) =>
    `${CBX_URL.moduleAlias({ module })}/docStatus?docStatus=${docStatus}`,

  batchSetToInListing: ({ module, docStatus }: { module; docStatus: string }) =>
    `api/batchMarkAs/${MODULE_ENDPOINT[module]}/docStatus?docStatus=${docStatus}`,

  markAsInListing: ({ module, status }: { module: string; status: string }) =>
    `${CBX_URL.moduleAlias({ module })}/status?status=${status}`,

  batchMarkAsInListing: ({ module, status }: { module; status: string }) =>
    `api/batchMarkAs/${MODULE_ENDPOINT[module]}/status?status=${status}`,

  customActionInListing: ({ module, actionId, viewName }: { module: string; actionId: string; viewName: string }) =>
    `${CBX_URL.moduleAlias({ module })}/searchCustomAction/${actionId}?viewName=${viewName}`,

  getNotification: (id: string) => `api/notifications/id/${id}`,

  updateNotification: ({ module, action }: { module: string; action: string }) =>
    `${CBX_URL.moduleAlias({ module })}/${action}`,

  crossUpdateDoc: ({ module, refNo }: { module: string; refNo: string }) =>
    `${CBX_URL.moduleAlias({ module })}/${refNo}/cross/update`,

  requestForSignature: ({ module }: { module: string }) => `${CBX_URL.moduleAlias({ module })}/requestForSignature`,

  moreRequestForSignature: ({ module }: { module: string }) =>
    `${CBX_URL.moduleAlias({ module })}/moreRequestForSignature`,

  validateShareFileType: ({ module, refNo, version }: { module: string; refNo: string; version: number }) =>
    `${CBX_URL.moduleAlias({ module })}/validateShareFileType/${refNo}/${version}`,

  validateRequestForSignature: ({ module }: { module: string }) =>
    `${CBX_URL.moduleAlias({ module })}/validateRequestForSignature`,

  getSignatureDefaultEmail: ({ module, refNo, version }: { module: string; refNo: string; version: number }) =>
    `${CBX_URL.moduleAlias({ module })}/getSignatureDefaultEmail/${refNo}/${version}`,

  getSignatureInfo: ({ module }: { module: string }) => `${CBX_URL.moduleAlias({ module })}/getSignatureInfo`,

  copyToClipboardInDoc: ({ module, refNo }: { module: string; refNo: string }) =>
    `${CBX_URL.moduleAlias({ module })}/copyToClipboard/${refNo}`,

  pasteFromClipboardActionInDoc: ({ module }: { module: string }) =>
    `${CBX_URL.moduleAlias({ module })}/pasteFromClipboard`,

  updateRowsValueInDoc: ({
    module,
    refNo,
    gridDomain,
    updateProperty,
    updateValue,
  }: {
    module: string;
    refNo: string;
    gridDomain: string;
    updateProperty: string;
    updateValue: string;
  }) => `${CBX_URL.moduleAlias({ module })}/${refNo}/${gridDomain}?${updateProperty}=${updateValue}`,

  actionItemAsInDoc: ({
    module,
    refNo,
    gridDomain,
    action,
  }: {
    module: string;
    refNo: string;
    gridDomain: string;
    action: string;
  }) => `${CBX_URL.moduleAlias({ module })}/${refNo}/${gridDomain}?action=${action}`,

  exportInListing: 'api/report',
  printInListing: 'api/print/doc/multipleData',

  exportInDoc: ({
    refNo,
    version,
    module,
    actionId,
  }: {
    refNo: string;
    version: number;
    module: string;
    actionId: string;
  }) => `api/export/doc/${refNo}/${version}?module=${module}&customFormTemplate=${actionId}`,

  exportLookupExcel: ({ refNo }: { refNo: string }) => `api/lookupLists/downloadLookup/${refNo}`,

  exportExcel: ({ refNo, module, version }: { refNo: string; module: string; version: number }) =>
    `api/export/exportExcel/${module}/${refNo}/${version}`,

  printInDoc: ({
    refNo,
    version,
    module,
    actionId,
  }: {
    refNo: string;
    version: number;
    module: string;
    actionId: string;
  }) => `api/print/doc/${refNo}/${version}?module=${module}&customFormTemplate=${actionId}`,

  apiBatchAddAction: `api/batchupdate/apiBatchAddAction`,

  batchUpFieldInitInListing: (entityName: string) => `api/views/batchUpdateFieldInit/${entityName}`,
  batchUpPartyTmplInitInListing: (entityName: string) => `api/views/batchUpdatePartyTmplInit/${entityName}`,
  batchUpPartyTypeInitInListing: (entityName: string) => `api/views/batchUpdatePartyTypeInit/${entityName}`,
  batchUpUserInitInListing: `api/views/batchUpdateUserInit`,

  sendNotification4BatchTerminateAgreement: `api/batchupdate/sendNotification/terminateAgreement`,
  sendNotification4BatchAddAgreement: `api/batchupdate/sendNotification/addAgreement`,

  getBatchUpFieldsForUser: (viewName: string) => `api/batchupdate/getBatchUpdateFields/${viewName}`,
  saveBatchUpFieldsForUser: (viewName: string) => `api/batchupdate/saveBatchUpdateFields/${viewName}`,
  batchUpdateFieldInListing: (entityName: string, saveType: string) =>
    `api/batchupdate/field?entityName=${entityName}&saveType=${saveType}`,

  batchUpdateSendNotification: (entityName: string) => `/api/batchupdate/sendNotification?entityName=${entityName}`,

  batchUpdatePartyInListing: (
    entityName: string,
    fromUserName: string,
    toUserName: string,
    isCheckOwner: boolean,
    selectedPartyType: string,
  ) =>
    `api/batchupdate/responsibleparty?entityName=${entityName}&fromUserName=${fromUserName}&toUserName=${toUserName}&isCheckOwner=${isCheckOwner}&selectedPartyType=${selectedPartyType}`,

  batchUpdateAgreementInListing: `api/batchupdate/agreement`,

  batchUpdateAgreementStatusInListing: (vendorRefNo: string, agreementStatus: string) =>
    `api/vendors/${vendorRefNo}/vendorAgreementStatus/${agreementStatus}`,

  batchAddAgreementStatusInListing: (vendorRefNo: string) => `api/vendors/addAgreement/${vendorRefNo}`,

  batchReinitPartyInListing: (entityName: string, tmplRef: string, isKeepUnchanged?: boolean) =>
    `api/batchupdate/reinitParty?entityName=${entityName}&tmplRef=${tmplRef}&isKeepUnchanged=${isKeepUnchanged}`,

  getBatchUpdaterRPMarkAsOwnerConfig: ({ module }: { module: string }) => `api/parties/${module}/config`,

  /**
   * attachment module
   */

  updateAttachmentAngle: ({ id, angle }: { id: string; angle: number }) => `/api/files/attachment/${id}/${angle}`,

  findAttachmentAngle: ({ id }: { id: string }) => `/api/files/attachment/${id}`,

  /**
   * Invite Vendor
   */
  inviteVendors: (moduleId: string, isResend: boolean) => `api/invitations/${moduleId}/${isResend}`,

  validateInviteLink: ({ token }) => `api/invitations/validate/${token}`,
  sendPasscode: ({ token }) => `api/invitations/passcode/send/${token}`,
  loadPasscodeData: ({ token }) => `api/invitations/passcode/${token}`,
  updatePasscode: (passcodeValidateTimes: number, token: string) =>
    `api/invitations/passcode/${passcodeValidateTimes}/${token}`,

  checkIfEmailInUse: (moduleId: string) => `api/invitations/checkIfEmailInUse/${moduleId}`,

  vendorRegistration: 'api/vendors/onlineVendor',
  subUserVendorRegistration: 'api/vendors/subUserVendor',

  vendorProcessStatus: (refNo: string) => `api/vendors/getOnlineProcessStatus/${refNo}`,

  vendorSendNotification: 'api/vendors/sendNotification',
  factorySendNotification: 'api/factories/sendNotification',
  /**
   * Vendor Search Convert to Online
   */
  searchConvertToOnline: `api/vendors/searchConvertToOnline`,

  /**
   * Factory Search Convert to Online
   */
  searchConvertFactoryToOnline: `api/factories/searchConvertToOnline`,

  /**
   * Vendor Convert to Online
   */
  convertToOnline: () => `api/vendors/convertToOnline`,

  searchConvertToOnlineFromView: () => `api/vendors/searchConvertToOnlineFromView`,

  vendorAgreeSearchResend: () => `api/vendors/vendorAgreeSearchResend`,

  vendorAgreeSearchSetInactive: () => `api/vendors/vendorAgreeSearchSetInactive`,

  /**
   * Fact Convert to Online
   */
  convertFactoryToOnline: () => `api/factories/convertToOnline`,

  checkIfAccessEditPopup: `api/codelists/checkIfAccessEditPopup`,
  /**
   * Link Bar action
   */

  // favorite merged into follow in KME-7461
  followDocument: 'api/follows/document',

  healthCheck: 'actuator/health',

  /**
   * Approval
   */
  approvalProfiles: ({ documentId, isLatest }: { documentId: string; isLatest: boolean }) =>
    `api/approvalProfiles?docId=${documentId}&isLatest=${isLatest}`,

  approvalTemplates: ({ actionId, moduleId }: { actionId: string; moduleId: string }) =>
    `api/approvalTemplates?action=${actionId}&module=${moduleId}`,

  approvalApprovers: ({ profileId, stageId }: { profileId: string; stageId: string }) =>
    `api/approvalProfiles/${profileId}/approvalStages/${stageId}/stageApprovers`,

  withdrawnApproval: ({ profileId }: { profileId: string }) => `api/approvalProfiles/${profileId}`,

  approvalCustomFields: (aprvTypeId: string) => `api/approvalProfiles/customFields/${aprvTypeId}`,

  dataListCustomFields: (name: string) => `api/approvalProfiles/dataListCustomFields/${name}`,

  dataListCustomFieldsByAprvTemplName: (name: string) =>
    `api/approvalProfiles/dataListCustomFieldsByAprvTemplName/${name}`,

  // DocLock
  docLocks: `api/docLocks`,
  docLocks4Vq: `api/docLocks/lockVq`,
  docLocks4CostSheet: `api/docLocks/lockCostSheet`,

  // checkDocLockedByOthers
  checkDocLockedByOthers: 'api/docLocks/checkDocLockedByOthers',
  checkCurrentDocLockedByOthers: 'api/docLocks/checkCurrentDocLockedByOthers',

  // Activity
  activities: 'api/activities',
  integrations: 'api/docIntegrationHistories',

  versionComparisonVM: ({
    module,
    refNo,
    version1,
    version2,
  }: {
    module: string;
    refNo: string;
    version1: number;
    version2: number;
  }) => `api/version_comparison/newui/${module}/${refNo}/${version1}/${version2}`,

  versions: ({ module, refNo }: { module: string; refNo: string }) => `api/change_history/${module}/${refNo}`,

  changeHistory: ({ module, refNo, dateRange }: { module: string; refNo: string; dateRange: string }) =>
    `api/change_history/newui/${module}/${refNo}/${dateRange}`,

  cpmChangeHistory: ({
    cpmId,
    milestoneCode,
    dateRange,
  }: {
    cpmId: string;
    milestoneCode: string;
    dateRange: string;
  }) => `api/cpm/${cpmId}/${milestoneCode}/${dateRange}`,

  /**
   * Vendor APIs
   */
  vendorAgreementAgreed: ({
    module,
    refNo,
    vendorAgreementId,
  }: {
    module: string;
    refNo: string;
    vendorAgreementId: string;
  }) => `${CBX_URL.moduleAlias({ module })}/${refNo}/vendorAgreementList/${vendorAgreementId}`,

  ibDownloadQcPack: ({ refNo, version }: { refNo: string; version: number }) =>
    `api/inspect_bookings/downLoadQcPack/${refNo}/${version}`,

  factAgreementAgreed: ({
    module,
    refNo,
    factAgreementId,
  }: {
    module: string;
    refNo: string;
    factAgreementId: string;
  }) => `${CBX_URL.moduleAlias({ module })}/${refNo}/factAgreementList/${factAgreementId}`,

  moduleOptions: 'api/chats/referenceDocModules',

  allDomainAttributes: 'api/domainAttributes/getAllAllowlist',

  additionalDomainAttributes: 'api/domainAttributes/additional',

  agreementTemplatesStatusIsActiveAndIsDefault: '/api/agreementTemplates/statusIsActiveAndIsDefault',

  addOrMoveItemsToProject: (addOrMove: string) => `api/projects/addOrMoveItem/${addOrMove}`,

  /**
   * LineSheet APIs
   */

  lineSheetDropdown: 'api/lineSheets/dropdown',

  lineSheetNewItems: `api/lineSheets/inheritance/item`,

  lineSheetCopyAndSaveItems: 'api/lineSheets/lineSheetItem',

  lineSheetItemSourcingStatus: (refNoList: string[] = []) => `api/lineSheets/sourcingStatus/${refNoList.join(',')}`,

  lineSheetItemAcceptQuote: (refNo: string) => `api/lineSheets/acceptQuote/${refNo}`,

  dashboardConfig: (dashboardEntries: string) => `api/lookupLists/dashboardConfig/${dashboardEntries}`,

  shipment: '/api/vizion/getShipmentContainerTracking',

  findDefaultChecklistTemplate: (module: string, applyTo: string, tableReqChecklist: string) =>
    `/api/${MODULE_ENDPOINT[module]}/getDefaultChecklistTemplate/${applyTo}/${tableReqChecklist}`,

  getCapaViewCbxql: (module: string, refNo: string, version: string) =>
    `/api/${MODULE_ENDPOINT[module]}/getCapaViewCbxql/${refNo}/${version}`,

  regenerateCostSheets: (module: string, refNo: string) =>
    `/api/${MODULE_ENDPOINT[module]}/regenerateCostSheets/${refNo}`,

  getApprovePopParams: (option: string) => `/api/sample_evaluations/getApprovePopParams/${option}`,

  sampleDetailsSubmitValidation: () => `/api/sample_trackers/sampleDetailsSubmitValidation`,

  listingCheckValidation: ({ module, actionId, refNoList }: { module: string; actionId: string; refNoList: string }) =>
    `${CBX_URL.moduleAlias({ module })}/checkValidation/${actionId}/${refNoList}`,
  /**
   * VPO APIs
   */
  refreshItems: 'api/purchase_orders/refresh/item',
  saveAfterRefresh: 'api/purchase_orders/refresh/item/save',
  batchUpdateFactory: 'api/purchase_orders/batchUpdateVpoFactory',
  refreshVpoItemByRefNoAndVersion: ({ refNo, version }: { refNo: string; version: number }) =>
    `api/purchase_orders/refresh/item/${refNo}/${version}`,

  getRefreshItemUpdateDoc: ({ refNo, version }: { refNo: string; version: number }) =>
    `api/purchase_orders/refresh/item/${refNo}/${version}`,

  relatedSampleEvaluations: (refNo: string) => `api/sample_evaluations/relatedSampleEvaluations/${refNo}`,

  refreshMeasurementByItem: () => `api/inspect_reports/refreshMeasurement`,

  replaceBom: `api/items/replaceBOM`,

  getVpobyByRefNoAndVersion: (refNo: string, version: number) => `api/purchase_orders/${refNo}/${version}`,

  confirmVendorChangeProposed: () => `api/purchase_orders/acceptAndRejectVendorChanges`,

  displayVendorChange: () => `api/purchase_orders/displayVendorChange`,

  displayInlineChange: () => `api/purchase_orders/displayInlineChange`,

  ChainOfCustodySupplier: () => `api/purchase_orders/vendorRelatedHierarchy`,

  ChainOfCustodySelect: (vpoRefNo: string, itemNo: string) =>
    `api/purchase_orders/selectChainOfCustody/${vpoRefNo}/${itemNo}`,

  chainOfCustodyModalSummary: (vpoId: string) => `/api/purchase_orders/chainOfCustody/summary/${vpoId}`,
  chainOfCustodyModalAnalysis: (vpoId: string) => `/api/purchase_orders/chainOfCustody/analysis/${vpoId}`,
  chainOfCustodyUpdateEntityStatus: (
    vpoId: string,
    analysisId: string,
    pageNo: number,
    entityId: string,
    status: string,
  ) =>
    `/api/purchase_orders/chainOfCustody/review/${analysisId}/${pageNo}/${entityId}?aiAnalysisPageStatus=${status}&vpoId=${vpoId}`,
  chainOfCustodyAlertReferenceList: (vpoId: string) => `/api/purchase_orders/chainOfCustody/alertReference/${vpoId}`,

  chainOfCustodyRescan: 'api/purchase_orders/chainOfCustody/rescan',

  refreshVpoChainOfCustody: (itemRefNo: string) => `api/purchase_orders/refresh/chainOfCustody/${itemRefNo}`,

  updateVpoChainOfCustody: (vpoNo: string, itemNo: string) =>
    `api/purchase_orders/update/chainOfCustody/${vpoNo}/${itemNo}`,

  /**
   * Info Center APIs
   */
  markDocAsRead: (moduleId: string, docRef: string) => `/api/${MODULE_ENDPOINT[moduleId]}/${docRef}/read`,
  getReadUserDetailList: (moduleId: string, docRef: string, status: string) =>
    `/api/${MODULE_ENDPOINT[moduleId]}/${docRef}/readStatus?status=${status}`,

  markDocAsAcknowledged: (moduleId: string, docRef: string) =>
    `/api/${MODULE_ENDPOINT[moduleId]}/${docRef}/acknowledged`,
  getAcknowledgedUserDetailList: (moduleId: string, docRef: string, status: string) =>
    `/api/${MODULE_ENDPOINT[moduleId]}/${docRef}/acknowledgedStatus?status=${status}`,

  /**
   * Doc comment
   */
  findDocChangeComment: (moduleId: string, docRef: string, docVer: number) =>
    `/api/docChangeComment/loadComment/${moduleId}/${docRef}/${docVer}`,

  findLastDocChangeComment: (moduleId: string, docRef: string, docVer: number) =>
    `/api/docChangeComment/loadLastComment/${moduleId}/${docRef}/${docVer}`,

  getNearbyAddressList: (lng: string, lat: string, module: string) =>
    `/api/factories/getNearbyAddressList/${module}/${lng}/${lat}`,

  translate: (query: string, from: string, to: string) => `/api/factories/translate/${query}/${from}/${to}`,

  addChangeComment: '/api/docChangeComment/addComment',

  itemPartailCopy: '/api/items/partailCopy',

  getDataWithoutPermission: ({ refNos, actionId }: { refNos: string; actionId: string }) =>
    `/api/purchase_orders/getActionAccessRight?refNos=${refNos}&action=${actionId}`,

  resendNotification: ({ refNos }: { refNos: string }) => `/api/vendors/resendNotification?refNos=${refNos}`,

  /**
   * DataListType APIS
   */
  getDataListType4Dropdown: ({ applyToEntity, docStatus }: { applyToEntity: string; docStatus: string }) =>
    `api/dataListTypes/applyTo/${applyToEntity}/${docStatus}`,

  dataListTypeInitInListing: (entityName: string) => `api/views/searchDataListType/${entityName}`,

  getSelectedDataListType: ({ id }: { id: string }) => `api/dataListTypes/byId/${id}`,

  refreshCsoItems: 'api/sales_orders/refresh/items',
  /**
   * vq APIs
   */
  adoptSpecToItem: (docId: string) => `api/quotations/adoptSpecToItem/${docId}`,
  adoptAsNewItem: (docId: string) => `api/quotations/adoptAsNewItem/${docId}`,
  concurrentEditAdoptAsNewItem: (refNo: string) => `api/concurrentEdit/concurrentEditAdoptAsNewItem/${refNo}`,
  concurrentEditCopyFromExistingVq: `api/concurrentEdit/concurrentEditCopyFromExistingVq`,

  /**
   * HclType APIS
   */
  hclTypeInitInListing: `api/hclTypes/searchHclTypes`,

  getSelectedHclType: ({ id }: { id: string }) => `api/hclTypes/byId/${id}`,

  checkCodelistDetail: `api/dataListTypes/validation/attrValue`,

  loadCustomFieldRecords: `api/dataListTypes/customFields`,

  sendCommandPartialCopyToItem: `api/items/sendCommand/partialCopyToItem`,
  lookupTypeInitInListing: (entityName: string) => `api/views/searchDataListType/${entityName}`,

  /**
   * VPO Visibility APIs
   */
  getSaList: (vpoNo: string) => `api/shipment/tracking/rhp/saList/${vpoNo}`,

  getContainerList: (saRefNo: string) => `api/shipment/tracking/rhp/containerList/${saRefNo}`,

  isNeedMarkasLinkedDoc: (module: string, id: string) =>
    `${CBX_URL.moduleAlias({ module })}/isNeedMarkasLinkedDoc/${id}`,

  getMilestone: (saRefNo: string, containerNo: string) =>
    `api/shipment/tracking/rhp/milestone/${saRefNo}/${containerNo}`,

  getCustQRCode: 'api/sso/getQRCode',

  requestNewDnaRevision: ({ module, action }: { module: string; action: string }) =>
    `${CBX_URL.moduleAlias({ module })}/requestNewDnaRevision?action=${action}`,

  loadCpmFormAcl: () => `api/cpm/loadCpmPopupSecurity`,

  uploadToFlexPLM: () => '/api/items/uploadToFlexPLM',

  /**
   * Docu Sign APIs
   */
  getDocuSignUrl: `api/docusign/signUrl`,
  getDocuSignUrlForFact: `api/docusign/signUrlForFact`,

  resendDocuSignEnvelope: `api/docusign/resend`,
  resendDocuSignEnvelopeForFact: `api/docusign/resendForFact`,

  getDataAbstractByModule: (module: string) => `api/forms/getDataAbstract/${module}`,

  /**
   * inline Comparison APIs
   */
  updateInlineComparison: 'api/inlineComparison/updateInlineComparison',
  inlineComparison: `api/inlineComparison/find`,
  getRefCompareModuleDataByVersion: `api/inlineComparison/getRefModuleDataByVersion`,

  /**
   * inline comment APIs
   */
  saveDocumentComment: 'api/inlineComment/saveInlineComment',
  listCommentFields: (moduleId: string, refNo: string, version: number) =>
    `api/inlineComment/${moduleId}/${refNo}/${version}`,
  listFieldComments: `api/inlineComment/listFieldComments`,
  listAllFieldComments: `api/inlineComment/listAllFieldComments`,
  // Item
  itemGenerateEss: 'api/items/generateEss',
  itemGenerateTbsc: 'api/items/generateTbsc',
  itemGenerateLibrarySpecCard: 'api/items/generateLibrarySpecCard',
  itemGenerateDnaDoc: 'api/items/generateDnaDoc',

  FactAuditParentInfo: (factRefNo: string) => `api/factory_audits/factAuditRelatedParentInfo/${factRefNo}`,

  validateProjectMandatory: ({ module }: { module: string }) =>
    `${CBX_URL.moduleAlias({ module })}/validateProjectMandatory`,

  CapParentInfo: (factRefNo: string) => `api/corrective_action_plans/capRelatedParentInfo/${factRefNo}`,

  createAuditFollowup: 'api/factory_audits/createFollowup',
  inspectionReportCustomizedValidation: 'api/inspect_reports/customizedValidation',

  getExternalPartyIdByExternalDomainIdAndExternalPartyType: (domainId: string) =>
    `api/domainMasterMappings/externalDomainId=${domainId}`,
  hasDashboardAdmin: '/api/widgets/haveDashboardAdmin',

  /**
   * DNB api
   */
  getDnbCompanyMatch: `api/dnb/matchCompany`,
  getDnbRating: `api/dnb/applyForCreditEnhanced`,

  /**
   * Kharon api
   */
  getKharonByName: `api/kharon/search`,
  getKharonBySecurityIdentifier: `api/kharon/searchBySecurity`,
  getKharonAttributes: `api/kharon/getAttributes`,
  getKharonRisk: `api/kharon/getRiskInformation`,
  reloadInspectBookingQualityPlan: 'api/inspect_bookings/loadMatchQualityPlanTemplates',
  reloadInspectReportQualityPlan: 'api/inspect_reports/loadMatchQualityPlanTemplates',
  reloadFactAuditQualityPlan: 'api/factory_audits/loadMatchQualityPlanTemplates',
  reloadMultipleQualityPlanWhenUpdateEnabled: 'api/inspect_reports/reloadMultipleQualityPlanWhenUpdateEnabled',

  /**
   * Domain APIs
   */

  domainDropdown: 'api/domains/dropdown',

  existsQuotations: `api/quotations/existsQuotations`,
  batchCreateQuotations: `api/quotations/batchCreateQuotations`,
  // START: image drawing
  imageDrawing: ({ fileId }: { fileId: string }) => `api/drawingData/${fileId}`,
  // END: image drawing

  listModules: `api/compTempls/listModules`,

  listLevel: `api/compTempls/listLevel`,
} as const;
