import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { Dialog, DialogConfig } from '@angular/cdk/dialog';
import { Overlay, PositionStrategy } from '@angular/cdk/overlay';
import { AsyncPipe, CommonModule, NgIf, formatNumber } from '@angular/common';
import { HttpErrorResponse, HttpEvent, HttpEventType, HttpParams } from '@angular/common/http';
import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  HostBinding,
  inject,
  Inject,
  Input,
  OnInit,
  Optional,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, NavigationExtras, ParamMap, Router } from '@angular/router';

import { TranslocoModule, TranslocoService } from '@ngneat/transloco';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import {
  ColDef,
  ColumnState,
  ExcelExportParams,
  ExcelStyle,
  GridApi,
  GridReadyEvent,
  GroupSelectionMode,
  IRowNode,
  RowSelectedEvent,
  SideBarDef,
  ValueFormatterParams,
  ValueGetterParams,
} from 'ag-grid-community';
import { AngularSplitModule } from 'angular-split';
import { Decimal } from 'decimal.js';
import * as R from 'ramda';
import {
  BehaviorSubject,
  combineLatest,
  EMPTY,
  forkJoin,
  from,
  merge,
  Observable,
  of,
  ReplaySubject,
  Subject,
  throwError,
} from 'rxjs';
import {
  auditTime,
  catchError,
  concatMap,
  debounceTime,
  delay,
  distinctUntilChanged,
  filter,
  first,
  map,
  mergeMap,
  pairwise,
  shareReplay,
  skip,
  startWith,
  switchMap,
  take,
  tap,
  throttleTime,
  withLatestFrom,
} from 'rxjs/operators';

import { SlideToggleComponent } from '../../../../component/slide-toggle/slide-toggle.component';
import { defaultDialogConfig, generalAlertDialogConfig } from '../../../../config/dialog';
import { getViewFieldWidth, viewColumnSortingIconWidth } from '../../../../config/width-code-map';
import { AlertDialogTypes, DialogButtonTypes } from '../../../../entities/dialog';
import { UserService } from '../../../../services/user.service';
import { FileService } from '../../../services/file.service';
import { FloatingFilterComponent } from '../../../shared/common/cbx-table/floating-filter/floating-filter.component';
import { CreateOrEditViewDialogComponent } from '../../../shared/common/create-or-edit-view-dialog/create-or-edit-view-dialog.component';
import { EllipsisLoaderComponent } from '../../../shared/common/ellipsis-loader/ellipsis-loader.component';
import { ListingMenuBarComponent } from '../../../shared/common/listing-menu-bar/listing-menu-bar.component';
import { ModeToggleComponent } from '../../../shared/common/mode-toggle/mode-toggle.component';
import { TimeSelectionComponent } from '../../../shared/common/time-selection/time-selection.component';
import { ListingViewPackingListPopupComponent } from '../../document/cbx-module/packing-list/listing-view-packing-list-popup/listing-view-packing-list-popup.component';
import { BatchAddAgreementProgressBarComponent } from '../batch-add-agreement-progress-bar/batch-add-agreement-progress-bar.component';
import { BatchUpdateCocDetailProgressBarDialogComponent } from '../batch-update-process-dialog/batch-update-coc-detail-process-dialog.component';
import { ColumnEditDialogComponent } from '../column-edit-dialog/column-edit-dialog.component';
import { CpmViewFilterComponent } from '../cpm-view/cpm-view-filter/cpm-view-filter.component';
import { CpmViewComponent } from '../cpm-view/cpm-view.component';
import { EditColumnsComponent } from '../edit-columns/edit-columns.component';
import { ExportDataComponent } from '../export-data/export-data.component';
import { ImportDialogComponent } from '../import-dialog/import-dialog.component';
import { ListingViewBatchSubmissionCOCPopupComponent } from '../listing-view-batch-submission-coc-popup/listing-view-batch-submission-coc-detail-table.component';
import { LookupTypeDialogComponent } from '../lookup-type-dialog/lookup-type-dialog.component';
import { RecordsResolvedData } from '../resolver/records.resolver';
import {
  COLUMN_PREPROCESSOR,
  ColumnPreprocessor,
  MODULE_ACTION,
  ModuleAction,
} from '../service/module-injection-token';
import { RECORD_ACTION_SERVICE, RecordActionServiceInfra } from '../service/record-action.model';
import { RECORD_CELL_ACTION_SERVICE, RecordCellActionServiceInfra } from '../service/record-cell-action.model';
import { SettingConfigurationComponent } from '../setting-configuration/setting-configuration.component';
import { ViewOptionsDropdownComponent } from '../view-options-dropdown/view-options-dropdown.component';
import { IconComponent } from 'src/app/component/icon/icon.component';
import { CBX_URL } from 'src/app/config/constant';
import { SearchCondition } from 'src/app/entities';
import { FormDefine_OpenDialogMeta } from 'src/app/entities/api';
import { BatchUpdateProgressBarParam } from 'src/app/entities/batch-update';
import { Action, OpenDialogMeta, ViewActionDto } from 'src/app/entities/form-field';
import {
  AnyObject,
  BatchUpdateProgressBarData,
  ColumnEditDialogData,
  ColumnEditDialogResult,
  ColumnTypeMap,
  CpmEndToEndView,
  CreateOrEditViewDialogData,
  DefaultSelectDialogData,
  ExportExcelDialogData,
  ExportExcelDialogResult,
  FilterType,
  ImportDialogData,
  LookupTypeDialogData,
  ModeType,
  NaviView,
  PackingListPopupData,
  PackingListPopupResult,
  Record,
  TableSelectDialogResult,
  TemplateSelectDialogData,
  TemplateSelectDialogResult,
  View,
  ViewColumn,
  ViewSorting,
} from 'src/app/interface/model';
import { CpmDocTracker, PopAssigneeView } from 'src/app/interface/model/cpm';
import { DocStatusResponse } from 'src/app/interface/model/doc-status-response';
import { Template } from 'src/app/interface/model/template';
import { ExportExcelDialogComponent } from 'src/app/modules/export-excel-dialog/export-excel-dialog.component';
import { DocumentService } from 'src/app/modules/services/document.service';
import { FieldBackgroundColorfulService } from 'src/app/modules/services/field-background-colorful.service';
import { ListingService } from 'src/app/modules/services/listing.service';
import { MenuBarService } from 'src/app/modules/services/menu-bar.service';
import { CpmFilterEditDialogComponent } from 'src/app/modules/shared/common/cbx-cpm-table';
import { CbxCpmTableComponent } from 'src/app/modules/shared/common/cbx-cpm-table/cbx-cpm-table.component';
import { CpmEndToEndEditColumnDialogComponent } from 'src/app/modules/shared/common/cbx-cpm-table/cpm-end-to-end-view/cpm-end-to-end-edit-column-dialog/cpm-end-to-end-edit-column-dialog.component';
import { CpmEndToEndEditColumnMainComponent } from 'src/app/modules/shared/common/cbx-cpm-table/cpm-end-to-end-view/cpm-end-to-end-edit-column-main/cpm-end-to-end-edit-column-main.component';
import { CpmEndToEndEditColumnReferenceComponent } from 'src/app/modules/shared/common/cbx-cpm-table/cpm-end-to-end-view/cpm-end-to-end-edit-column-reference/cpm-end-to-end-edit-column-reference.component';
import { CpmEndToEndEditColumnComponent } from 'src/app/modules/shared/common/cbx-cpm-table/cpm-end-to-end-view/cpm-end-to-end-edit-column/cpm-end-to-end-edit-column.component';
import { CpmEndToEndViewComponent } from 'src/app/modules/shared/common/cbx-cpm-table/cpm-end-to-end-view/cpm-end-to-end-view.component';
import { CbxTableCellMapperService } from 'src/app/modules/shared/common/cbx-table/cbx-table-cell-mapper.service';
import { CbxTableComponent } from 'src/app/modules/shared/common/cbx-table/cbx-table.component';
import { CellActionBusEvent, CellActionEvent } from 'src/app/modules/shared/common/cbx-table/cell-action-event.model';
import { DefaultSelectDialogComponent } from 'src/app/modules/shared/common/default-select-dialog/default-select-dialog.component';
import { TemplateSelectDialogComponent } from 'src/app/modules/shared/common/template-select-dialog/template-select-dialog.component';
import { AlertDialogComponent } from 'src/app/modules/shared/common/warning-dialog/alert-dialog.component';
import { ElementLifeDirective } from 'src/app/modules/shared/directives/element-life.directive';
import { AutoFileUploadDirective } from 'src/app/modules/shared/directives/file-upload/auto-file-upload.directive';
import { InertDirective } from 'src/app/modules/shared/directives/inert.directive';
import { SyncVerticalScrollContainerDirective } from 'src/app/modules/shared/directives/sync-vertical-scroll-container.directive';
import { SyncVerticalScrollItemDirective } from 'src/app/modules/shared/directives/sync-vertical-scroll-item.directive';
import { ApiService } from 'src/app/services/api.service';
import { AuthService } from 'src/app/services/auth.service';
import { LocalStorageService } from 'src/app/services/cache/local-storage.service';
import { ChatSettingService } from 'src/app/services/chat/chat-setting.service';
import { CodelistService } from 'src/app/services/codelist.service';
import { ConfigurationService } from 'src/app/services/configuration.service';
import { CpmService } from 'src/app/services/cpm.service';
import { DateTimeFormateService } from 'src/app/services/date-time-formate.service';
import { NavigationService } from 'src/app/services/navigation.service';
import { NotificationCheckService } from 'src/app/services/notification-check.service';
import { NotificationMarkAsReadService } from 'src/app/services/notification-markasread.service';
import { NotificationService } from 'src/app/services/notification.service';
import { PopoverParams } from 'src/app/services/popover/popover';
import { PopoverService } from 'src/app/services/popover/popover.service';
import { RecordService } from 'src/app/services/record.service';
import { CONDITION_SYMBOL, SearchFilter } from 'src/app/services/search/search.service';
import { StateRecordService } from 'src/app/services/state-record.service';
import { WINDOW } from 'src/app/services/window.service';
import {
  capitalizeLetter,
  checkViewActionConditions,
  compatibleUrl,
  customURLEstimate,
  deepEqual,
  getHeaderMinWidth,
  getValueFormatter,
  isEmptyOrNil,
  isNotEmptyOrNil,
  isNotNil,
  missingValue,
  normalizeDomainId,
  resolveFormat,
  roundUp,
  stringAsc,
} from 'src/app/utils';
import { getFileExtension } from 'src/app/utils/file-util';
import { InputObservable } from 'src/app/utils/input-subject-observer';
import { coerceBooleanMap, notMap } from 'src/app/utils/operators';
import { DomainAttributeService } from 'src/app/workspace/service/domain-attribute.service';
import { DOMAIN_ATTRIBUTES } from 'src/app/workspace/service/domain-attributes';

@UntilDestroy()
@Component({
  selector: 'app-records',
  templateUrl: './records.component.html',
  styleUrls: ['./records.component.scss'],
  providers: [RecordService],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    NgIf,
    AsyncPipe,
    TranslocoModule,
    AngularSplitModule,
    ViewOptionsDropdownComponent,
    ListingMenuBarComponent,
    EllipsisLoaderComponent,
    SlideToggleComponent,
    TimeSelectionComponent,
    SettingConfigurationComponent,
    EditColumnsComponent,
    ModeToggleComponent,
    ExportDataComponent,
    CpmViewFilterComponent,
    CpmEndToEndViewComponent,
    CbxCpmTableComponent,
    CbxTableComponent,
    CpmViewComponent,
    SyncVerticalScrollContainerDirective,
    SyncVerticalScrollItemDirective,
    CpmEndToEndEditColumnComponent,
    CpmEndToEndEditColumnDialogComponent,
    CpmEndToEndEditColumnMainComponent,
    CpmEndToEndEditColumnReferenceComponent,
    CpmFilterEditDialogComponent,
    IconComponent,
    CommonModule,
    ElementLifeDirective,
    AutoFileUploadDirective,
  ],
  hostDirectives: [
    {
      directive: InertDirective,
      inputs: ['appInert'],
    },
  ],
})
export class RecordsComponent implements OnInit {
  cbxTableCellComponents = inject(CbxTableCellMapperService).components;

  constructor(
    @Inject(WINDOW) private readonly window: Window,
    @Optional() @Inject(COLUMN_PREPROCESSOR) private readonly columnPreprocessor: ColumnPreprocessor,
    @Optional() @Inject(MODULE_ACTION) private readonly moduleAction: ModuleAction,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly listingService: ListingService,
    private readonly dialog: Dialog,
    private readonly navigationService: NavigationService,
    private readonly recordService: RecordService,
    private readonly fileService: FileService,
    private readonly notificationService: NotificationService,
    private readonly stateRecordService: StateRecordService,
    private readonly elementRef: ElementRef,
    private readonly renderer: Renderer2,
    private readonly chatSettingService: ChatSettingService,
    private readonly documentService: DocumentService,
    private readonly codelistService: CodelistService,
    private readonly cpmService: CpmService,
    private readonly configurationService: ConfigurationService,
    private readonly authService: AuthService,
    private readonly domainAttributeService: DomainAttributeService,
    private readonly fieldBackgroundColorfulService: FieldBackgroundColorfulService,
    private readonly localStorageService: LocalStorageService,
    private readonly userService: UserService,
    private readonly translocoService: TranslocoService,
    private readonly dateTimeFormatService: DateTimeFormateService,
    private readonly notificationCheckService: NotificationCheckService,
    private readonly menuBarService: MenuBarService,
    private readonly apiService: ApiService,
    private readonly titleService: Title,
    private readonly overlay: Overlay,
    private readonly popper: PopoverService,
    @Inject(RECORD_ACTION_SERVICE) private readonly recordActionService: RecordActionServiceInfra,
    private readonly notificationMarkAsReadService: NotificationMarkAsReadService,
    @Inject(RECORD_CELL_ACTION_SERVICE) private readonly recordCellActionService: RecordCellActionServiceInfra,
  ) {
    this.rowClassRules = {
      'cbx-grid-row-grouping-background': (params: any) => params.node.group > 0,
    };
  }

  readonly canClientExport = coerceBooleanProperty(this.configurationService.CONFIG.FLAG_LISTING_CLIENT_EXPORT);

  @HostBinding('class') class = 'grid height-100';

  @ViewChild(CbxTableComponent) table: CbxTableComponent<Record>;
  @ViewChild(CbxCpmTableComponent) cpmTable: CbxCpmTableComponent<Record>;
  @ViewChild(CpmEndToEndViewComponent) cpmEndToEndViewComponent: CpmEndToEndViewComponent<Record>;

  cpmEndToEndView$ = new BehaviorSubject<CpmEndToEndView>(null);

  private readonly widgetHidden$ = this.route.queryParamMap.pipe(
    map((params) => params.get('widgetHidden')),
    coerceBooleanMap,
  );

  widgetsDisabled$ = this.widgetHidden$.pipe(
    switchMap((widgetHidden) => (widgetHidden ? of(false) : this.domainAttributeService.listingWidgetsEnabled$)),
    notMap,
  );

  selectAll = false;
  loadCount = 0;
  loadTill = 0;
  recordCount = 0;
  toExport = false;
  exportReady$ = new Subject();
  selectAllReady$ = new Subject();
  exportInfo;
  exporting$ = new BehaviorSubject(false);
  naviModuleMap: Map<string, string>;
  cpmEndToEndql;
  hasAccessToEditView = false;
  shareWithList: PopAssigneeView[];
  optionsMap: Map<string, string>;
  totalPartyMe: number;
  supplierTypeField = 'supplierType';
  keepQueryParamsInListingPage = this.configurationService.CONFIG.FLAG_KEEP_QUERY_PARAMS_IN_LISTING_PAGE;

  private readonly actionCellRendererParams = {
    followDoc: (documentRef: string) => this.recordService.followDocument(this.moduleId, documentRef),
    unfollowDoc: (documentRef: string) => this.recordService.unfollowDocument(this.moduleId, documentRef),
  };

  private readonly actionColKey = '_actionCol';
  private readonly detailViewIconColKey = '_detailViewIconCol';

  maxWidthRatio = 2;

  private readonly actionCol: ColDef = {
    field: this.actionColKey,
    headerComponent: 'commonCheckboxHeader',
    headerComponentParams: { justifyContent: 'start' },
    width: this.configurationService.CONFIG.FLAG_NAVIGATE_DOCUMENT_EDIT_MODE_AT_LISTING ? 90 : 65,
    pinned: 'left',
    resizable: false,
    lockPosition: true,
    valueGetter: (params: ValueGetterParams) => params.data,
    cellRenderer: 'actionComponent',
    cellRendererParams: this.actionCellRendererParams,
    suppressSizeToFit: true,
    suppressColumnsToolPanel: true, // hide column into Columns Tool Panel
  };

  private readonly detailViewIconCol: ColDef = {
    headerName: '',
    field: this.detailViewIconColKey,
    width: 25,
    pinned: 'left',
    resizable: false,
    lockPosition: true,
    cellRenderer: 'agGroupCellRenderer',
    suppressSizeToFit: true,
    suppressColumnsToolPanel: true, // hide column into Columns Tool Panel
  };

  private readonly thumbnailCol: ColDef = {
    minWidth: 91,
    maxWidth: 91,
    pinned: 'left',
    resizable: false,
    lockPosition: true,
    valueGetter: (params: ValueGetterParams) => params.data.thumbnailUrl || params.data.thumbnail,
    valueFormatter: (params: ValueFormatterParams) => compatibleUrl(params.value),
    cellRenderer: 'imageLinkComponent',
    cellClass: 'image-cell',
    suppressSizeToFit: true,
    suppressColumnsToolPanel: true, // hide column into Columns Tool Panel
  };

  private readonly colorCol: ColDef = {
    minWidth: 91,
    maxWidth: 91,
    pinned: 'left',
    resizable: false,
    lockPosition: true,
    valueGetter: (params: ValueGetterParams) => params.data.hex,
    valueFormatter: (params: ValueFormatterParams) => {
      if (isEmptyOrNil(params.value)) {
        return params.value;
      }
      const [firstChar] = params.value;
      return firstChar === '#' ? params.value : `#${params.value}`;
    },
    cellRenderer: 'colorComponent',
  };

  dataKeys = ['rowIndex'];

  time$ = this.route.queryParamMap.pipe(map((queryParamMap) => queryParamMap.get('dateRange') || 'ANYTIME'));

  filterByFavorite$ = this.route.queryParamMap.pipe(map((queryParamMap) => !!queryParamMap.get('filterByFavorite')));

  filterByUnread$ = this.route.queryParamMap.pipe(map((queryParamMap) => !!queryParamMap.get('filterByUnread')));

  isResponsibleByMe = false;

  isCpmMode$ = this.route.queryParamMap.pipe(
    map((queryParamMap) => !!queryParamMap.get('isCpmMode')),
    untilDestroyed(this),
    shareReplay(1),
  );

  filterByCpmAssignToMe: boolean;
  filterByCpmAssignToMe$ = this.route.queryParamMap.pipe(
    map((queryParamMap) => !!queryParamMap.get('filterByCpmAssignToMe')),
    tap((filterByCpmAssignToMe) => {
      this.filterByCpmAssignToMe = filterByCpmAssignToMe;
    }),
    untilDestroyed(this),
    shareReplay(1),
  );

  filterByCpmInComplete: boolean;
  filterByCpmInComplete$ = this.route.queryParamMap.pipe(
    map((queryParamMap) => !!queryParamMap.get('filterByCpmInComplete')),
    tap((filterByCpmInComplete) => {
      this.filterByCpmInComplete = filterByCpmInComplete;
    }),
    untilDestroyed(this),
    shareReplay(1),
  );

  loadCpmIsOpenSubTasks$ = new BehaviorSubject<boolean>(null);

  tabId: string;

  resolvedData$ = new ReplaySubject<RecordsResolvedData>(1);
  @InputObservable()
  @Input()
  resolvedData: RecordsResolvedData;

  param$ = this.resolvedData$.pipe(
    map((resolvedData) => ({
      tabId: resolvedData.tabId,
      viewName: resolvedData.viewId,
      listingDateRange: resolvedData.listingDateRange,
      listingQueryParams: resolvedData.listingQueryParams,
    })),
    tap(({ listingDateRange: dateRange, listingQueryParams }) => {
      if (this.keepQueryParamsInListingPage) {
        this.routeByQuery({ dateRange, ...listingQueryParams });
      }
    }),
    tap(({ tabId }) => {
      this.tabId = tabId;
    }),
    untilDestroyed(this),
    shareReplay(1),
  );

  viewSubject = new ReplaySubject<View>(1);
  viewType: string;
  viewId: string;
  description: string;
  baseViewName: string;
  view$ = this.viewSubject.pipe(
    distinctUntilChanged(),
    tap((view) => {
      this.viewType = view.type;
      this.viewId = view.id;
      this.description = view.description;
      this.baseViewName = view.baseViewName ?? view.name;
    }),
  );

  sortingActionSubject$ = new Subject<void>();
  isOpenSubTasks$ = new BehaviorSubject<boolean>(null);
  skipUpdateIsOpenSubTasks: boolean = false;

  moduleId: string;
  moduleId$ = this.view$.pipe(
    map((view) => view?.module),
    distinctUntilChanged(),
    tap((moduleId) => {
      this.moduleId = moduleId;
    }),
    untilDestroyed(this),
    shareReplay(1),
  );

  enabledConcurrent: boolean;
  enabledConcurrent$ = this.moduleId$.pipe(
    map((moduleId) => this.domainAttributeService.enabledConcurrentModules.includes(moduleId)),
    distinctUntilChanged(),
    tap((enabledConcurrent) => {
      this.enabledConcurrent = enabledConcurrent;
    }),
  );

  enableResponsibleByMe$ = combineLatest([this.moduleId$, this.domainAttributeService.responsibleByMeList$]).pipe(
    switchMap(([moduleId]) =>
      this.queryStringResponsibleByMe(moduleId).pipe(
        map(() => this.authService.state.userType === 'buyer' && this.totalPartyMe > 0),
      ),
    ),
  );

  enableUnread$ = combineLatest([this.moduleId$, this.domainAttributeService.unreadList$]).pipe(
    map(([moduleId, unreadList]) => unreadList?.includes(moduleId)),
  );

  isResponsibleByMe$ = this.enableResponsibleByMe$.pipe(
    switchMap((enableResponsibleByMe) => (enableResponsibleByMe ? this.userService.isResponsibleByMe$ : of(false))),
    tap((isResponsibleByMe) => {
      this.isResponsibleByMe = isResponsibleByMe;
    }),
  );

  statusChange$ = new Subject<void>();

  urlChange$ = combineLatest([this.param$, this.route.queryParamMap]).pipe(
    auditTime(0),
    untilDestroyed(this),
    shareReplay(1),
  );

  queryString$ = combineLatest([this.urlChange$, this.isResponsibleByMe$]).pipe(
    map(([[{ viewName }, queryParamMap], isResponsibleByMe]) => {
      const queryString = this.generateQueryString(queryParamMap, [
        `isResponsibleByMe=${isResponsibleByMe}`,
        `showFavoriteState=true`,
      ]);

      return { viewName, condition: queryString };
    }),
  );

  urlParams$ = this.queryString$.pipe(untilDestroyed(this), shareReplay(1));

  searchCondition$ = this.route.queryParamMap.pipe<SearchCondition>(
    map((queryParamMap) =>
      Object.keys(new SearchCondition())
        .map((key) => [key, queryParamMap.get(key)])
        .filter(([, value]) => value)
        .reduce((prev, [key, value]) => ({ ...prev, [key]: value }), {}),
    ),
  );

  sortings$ = this.view$.pipe(
    map((view) => view?.sortings),
    distinctUntilChanged(),
    auditTime(0),
    untilDestroyed(this),
    shareReplay(1),
  );

  loadMore$ = new Subject<boolean>();

  loadRecord$ = this.loadMore$.pipe(withLatestFrom(this.urlChange$));

  resetRecord$ = merge(
    this.urlChange$,
    this.statusChange$.pipe(delay(1000)),
    this.sortingActionSubject$,
    this.isResponsibleByMe$,
  ).pipe(
    tap(() => this.recordService.resetRecordState()),
    tap(() => this.loadMore()),
    untilDestroyed(this),
  );

  totalSize$ = this.recordService.pagination$.pipe(map((pagination) => pagination?.totalSize));

  viewName: string;
  viewName$ = this.param$.pipe(
    map(({ viewName }) => viewName),
    filter((viewName) => !!viewName),
    tap((viewName) => {
      this.viewName = viewName;
      this.switchMode$.next(null);
    }),
    distinctUntilChanged(),
    untilDestroyed(this),
    shareReplay(1),
  );

  isVpoCpm$ = this.viewName$.pipe(map((viewName) => viewName === 'vpoCpmEndToEnd'));
  isInboxView$ = this.viewName$.pipe(map((viewName) => viewName === 'newInboxView' || viewName === 'inboxView'));
  notVpoCpm$ = this.isVpoCpm$.pipe(map((isVpoCpm) => !isVpoCpm));

  action$ = combineLatest([
    this.isCpmMode$,
    this.view$.pipe(
      map((view) => {
        const actions = view.actions as ViewActionDto[];
        this.hasAccessToEditView = false;
        this.shareWithList = [];
        actions.forEach((action) => {
          if (action.id === 'settingView') {
            this.hasAccessToEditView = true;
            action.disabled = true;
            if (this.viewType === 'Customized') {
              this.listingService
                .getCustomViewShareWithList$(this.viewName)
                .pipe(distinctUntilChanged(deepEqual))
                .subscribe((shareWithList) => {
                  this.shareWithList = shareWithList;
                });
            } else if (!this.viewType || this.viewType === 'PersonalizeView') {
              this.listingService
                .getPersonalizeViewOptionsMap$(this.viewId)
                .pipe(distinctUntilChanged(deepEqual))
                .subscribe((optionsMap) => {
                  this.optionsMap = optionsMap;
                });
            }
          }
        });
        return this.moduleAction?.processAction(actions) ?? actions;
      }),
    ),
  ]).pipe(
    map(([isCpmMode, actions]) => {
      if (isCpmMode) {
        return actions;
      }
      return actions.filter((action) => action.id !== 'cpmGroup' && action.id !== 'openBatchUpdateWin');
    }),
  );

  option$ = this.view$.pipe(
    map((view) => view?.option),
    distinctUntilChanged(),
  );

  isShowDetailView: boolean;
  isShowDetailView$ = this.view$.pipe(
    map((view) => view?.isShowDetailView),
    distinctUntilChanged(),
    tap((isShowDetailView) => {
      this.isShowDetailView = isShowDetailView;
    }),
    untilDestroyed(this),
  );
  detailViewBaseFieldId: string;
  detailViewBaseFieldId$ = this.option$.pipe(
    map((option) => option?.detailViewBaseFieldId),
    distinctUntilChanged(),
    tap((detailViewBaseFieldId) => {
      this.detailViewBaseFieldId = detailViewBaseFieldId;
    }),
    untilDestroyed(this),
  );

  modes$ = this.option$.pipe(
    filter((options) => !!options),
    map(({ supportedModes }) => supportedModes),
  );

  hasCpmMode$ = this.option$.pipe(
    filter((options) => !!options),
    map(({ openCPMMode }) => openCPMMode),
    map(isNotEmptyOrNil),
  );

  defaultMode$ = this.option$.pipe(
    filter((options) => !!options),
    map(({ defaultMode }) => defaultMode),
  );

  switchMode$ = new Subject<ModeType>();

  mode$: Observable<ModeType> = merge(this.defaultMode$, this.switchMode$).pipe(
    distinctUntilChanged(),
    untilDestroyed(this),
    shareReplay(1),
  );

  gridApi: GridApi;
  notDocTransactionView = true;
  isNotNotificationView = true;

  formatByDataAbstract: string | null = null;

  gridScrollViewport$ = new ReplaySubject<HTMLElement>(1);

  cpmScrollViewport$ = new ReplaySubject<HTMLElement>(1);

  columns: ViewColumn[];
  columns$ = this.view$.pipe(
    map((view) => view?.columns),
    distinctUntilChanged(),
    switchMap((columns) => {
      if (this.configurationService.CONFIG.FLAG_APP_FIELD_BACKGROUND_COLORFUL) {
        return combineLatest([this.fieldBackgroundColorfulService.backgroundColorMap$, this.moduleId$]).pipe(
          map(([backgroundColorMap, moduleId]) =>
            columns.map((column) => {
              const columnBackgroundColorMap = backgroundColorMap[moduleId]?.[column.fieldId];
              if (columnBackgroundColorMap) {
                return {
                  ...column,
                  backgroundColorMap: columnBackgroundColorMap,
                };
              }
              return column;
            }),
          ),
        );
      }
      return of(columns);
    }),
    switchMap((columns) => this.columnPreprocessor?.transform(columns) ?? of(columns)),
    tap((columns) => {
      this.columns = columns;
    }),
    untilDestroyed(this),
    shareReplay(1),
  );

  filterBus$ = new Subject<{ fieldId: string; filters: SearchFilter[]; filterCodeList?: SearchFilter[] }>();

  hasFilter$ = this.searchCondition$.pipe(
    map(({ cbxql }) => cbxql),
    map(isNotEmptyOrNil),
  );

  hasSearchOrFilter$ = this.searchCondition$.pipe(
    map((searchCondition) => R.pickAll(['cbxql', 'q'])(searchCondition)),
    map((searchCondition) => Object.values(searchCondition).some(isNotEmptyOrNil)),
  );

  filterMap$ = this.searchCondition$.pipe(
    map(({ cbxql }) => cbxql),
    map((filtersString1) => {
      const filterMap: { [fieldId: string]: SearchFilter[] } = {};
      if (R.isNil(filtersString1)) {
        return filterMap;
      }

      this.cpmEndToEndql = decodeURIComponent(filtersString1);
      const rg = /[^%&',;=?$\x22]+/;
      const filtersString = rg.test(filtersString1) ? filtersString1 : decodeURIComponent(filtersString1);

      const fieldConditionRegExp = /(?<fieldId>[\w-]+)\s?(?<condition>[~!=(IS)><].*)/;
      const fieldConditionOrRefExp = /(?<orFieldId>[\w-]+)\s?(?<orCondition>[~!=(IS)><].*)/;

      const filters = filtersString.split(CONDITION_SYMBOL.and);
      filters.filter(isNotEmptyOrNil).forEach((filterString) => {
        const [andFilter, ...orFilters] = filterString.split(CONDITION_SYMBOL.globalOr);

        const { fieldId, condition } = andFilter.match(fieldConditionRegExp).groups;

        if (!filterMap[fieldId]) {
          filterMap[fieldId] = [];
        }
        filterMap[fieldId].push({ condition, joinOperator: CONDITION_SYMBOL.and });

        orFilters.forEach((orFilterString) => {
          const { orFieldId, orCondition } = orFilterString.match(fieldConditionOrRefExp).groups;
          if (!filterMap[orFieldId]) {
            filterMap[orFieldId] = [];
          }
          filterMap[orFieldId].push({ condition: orCondition, joinOperator: CONDITION_SYMBOL.globalOr });
        });
      });
      return filterMap;
    }),
  );

  cellRendererMapByType: ColumnTypeMap<string> = {
    RefNo: 'linkComponent',
    Hyperlink: 'linkComponent',
    HyperlinkBySupplier: 'linkComponent',
    HyperlinkMultiple: 'linkMultipleComponent',
    HyperlinkOrNew: 'linkOrNewComponent',
    Thumbnail: 'imageLinkComponent',
    Date: 'dateComponent',
    Datetime: 'datetimeComponent',
    CostSheetNo: 'listingViewCostSheetNoComponent',
    ColorChip: 'colorComponent',
    ColorImage: 'colorImageComponent',
    CodelistHyperlink: 'codelistLinkCellComponent',
    CodelistPopup: 'codelistPopupCellComponent',
    ReferencesCellRenderer: 'linkManyCellComponent',
    NotificationSubjectLink: 'notificationSubjectLinkCellComponent',
    NotificationReferencesLink: 'notificationReferencesLinkCellComponent',
    VpoShipmentStatus: 'vpoShipmentStatusCellComponent',
    Tags: 'tagsCellComponent',
    button: 'buttonComponent',
    MultipleDate: 'multipleDateCellComponent',
    RichText: 'richTextComponent',
  };

  fixedSizeMap: ColumnTypeMap<boolean> = {
    Thumbnail: true,
  };

  canFilterMap: ColumnTypeMap<boolean> = {
    RefNo: true,
    Text: true,
    Label: true,
    Date: true,
    Datetime: true,
    Hyperlink: true,
    HyperlinkOrNew: true,
    Number: true,
    Decimal: true,
    Codelist: true,
    checkbox: true,
    CostSheetNo: true,
    HyperlinkMultiple: true,
    Dropdown: true,
    DateTime: true,
    ReferencesCellRenderer: true,
    NotificationReferencesLink: true,
    Percentage: true,
    VpoShipmentStatus: true,
    Tags: true,
    NotificationSubjectLink: true,
    button: true,
    MultipleDate: true,
  };

  filterTypeMap: ColumnTypeMap<FilterType> = {
    Date: 'date',
    Datetime: 'date',
    Number: 'number',
    Decimal: 'number',
    Percentage: 'percentage',
    Tags: 'tags',
  };

  cellClassMap: ColumnTypeMap<string> = {
    RefNo: 'hyperlink',
    Hyperlink: 'hyperlink',
    HyperlinkOrNew: 'hyperlink',
    Thumbnail: 'image-cell',
    Text: 'excelTextFormat',
    Percentage: 'percentage',
  };

  enableValueTypeMap: ColumnTypeMap<boolean> = {
    Number: true,
    Decimal: true,
  };

  columnDefs$: Observable<ColDef[]> = combineLatest([
    this.param$,
    this.columns$,
    this.sortings$,
    this.moduleId$,
    this.filterMap$,
    this.enabledConcurrent$,
  ]).pipe(
    auditTime(0),
    map(([param, columns, sortings = [], moduleId, filterMap, enabledConcurrent]) => {
      if (!moduleId) {
        return [];
      }
      const { tabId } = param;
      const sortingCols = Object.fromEntries(sortings.map(({ fieldId, type }) => [fieldId, type]));

      const valueFormatterMapByType: ColumnTypeMap<((params: ValueFormatterParams) => string) | string> = {
        RefNo: (params: ValueFormatterParams) => {
          const colId = params.colDef.cellRendererParams.col.fieldId;
          const docRef = params.data?.refNo || params.data?.docRef || params.data?.transactionRef;
          const version = params.data?.version;
          const domainId = normalizeDomainId(params.data?.domainId);

          if (isEmptyOrNil(docRef)) {
            console.warn(`refNo or docRef is required in RefNo field (${colId})`);
            return '';
          }

          if (isEmptyOrNil(version) && !enabledConcurrent) {
            console.warn(`version is required in RefNo field (${colId})`);
          }

          let link: string;
          if (moduleId === 'docTransaction') {
            const newModuleId = params.data?.transactionRefType.toString();
            const lastModuleId = newModuleId?.replace(newModuleId[0], newModuleId[0].toLowerCase());
            const lastNavi = this.navigationService.naviModuleMap[lastModuleId];

            link = `/document/${lastNavi}/${lastModuleId}/${docRef}`;
          } else if (moduleId === 'formTemplate') {
            return `/formBuilder/formTemplate/${docRef}`;
          } else {
            link = `/document/${tabId}/${moduleId}/${docRef}`;
          }
          const url = enabledConcurrent ? link : `${link}/${version ?? ''}`;
          if (customURLEstimate(moduleId, domainId)) {
            return `${url}/${domainId}`;
          }
          return url;
        },
        Hyperlink: (params) => this.getFormatValueForHyperlink(params),
        HyperlinkBySupplier: (params) => this.getFormatValueForHyperlink(params),
        HyperlinkOrNew: (params) => this.getFormatValueForHyperlink(params),
        HyperlinkMultiple: (params) => this.getFormatValueForHyperlink(params),
        Thumbnail: (params: ValueFormatterParams) => compatibleUrl(params.value),
        ColorChip: (params: ValueFormatterParams) => {
          if (isEmptyOrNil(params.value)) {
            return null;
          }
          const [firstChar] = params.value;
          return firstChar === '#' ? params.value : `#${params.value}`;
        },
        Number: (params: ValueFormatterParams) => this.getFormatValueForNumber(params, '.0-0'),
        Decimal: (params: ValueFormatterParams) => {
          const format = params.colDef?.cellRendererParams?.col?.format ?? '.4-4';
          return this.getFormatValueForNumber(params, format);
        },
        NotificationReferencesLink: (params: ValueFormatterParams) => {
          const refDocId = params.data?.refDocId;
          // just design for home notification listing page
          return refDocId;
        },
        Percentage: (params: ValueFormatterParams) => {
          if (Number.isNaN(Number(params.value)) || isEmptyOrNil(params.value)) {
            return params.value;
          }

          const format: string = params.colDef?.cellRendererParams?.col?.dataFormat;
          const match = format?.match(/decimalPlace=(\d+)/);
          const decimalPlace = match ? Number(match[1]) : 0;

          return `${roundUp(new Decimal(params.value ?? 0).mul(100).toNumber(), decimalPlace)}%`;
        },
      };

      return columns
        .filter((col) => !col.isDetailViewColumn)
        .map((col) => {
          const width = getViewFieldWidth(col.type, col.size);
          const minWidth = Math.min(getHeaderMinWidth(col.label), width * this.maxWidthRatio);
          if (col.type === 'Text' && col.action === 'OpenModuleDocAction' && isNotEmptyOrNil(col.actionParams)) {
            col.type = 'Hyperlink';
          }
          const cellRenderer =
            this.cellRendererMapByType[col.type.replace('com.core.cbx.sharefile.ui.', '')] || 'textComponent';
          const valueFormatter =
            valueFormatterMapByType[col.type] ||
            getValueFormatter(col?.dataFormat, this.translocoService.translate.bind(this.translocoService));

          const hasCodelist =
            (col.visible &&
              ((col.dataFormat &&
                (col.dataFormat?.includes('bookName') ||
                  col.fieldId === 'editingStatus' ||
                  col.fieldId === 'docStatus' ||
                  ((col.fieldId === 'status' || col.fieldId === 'poStatus') &&
                    col.dataFormat?.includes('labelIdPrefix=lbl.status.')))) ||
                col.fieldId === 'productCategory' ||
                col.fieldId === 'tags')) ||
            col.dataFormat?.includes('labelIdPrefix') ||
            col.dataFormat?.includes('labelPrefix');

          const codelistOptions$ = hasCodelist
            ? this.codelistService.getCodelistOptions(this.moduleId, this.viewName, col.fieldId).pipe(
                map((result) => {
                  const r = result[col.fieldId] ?? [];
                  return R.sort(stringAsc, r);
                }),
                catchError(() => of([])),
                untilDestroyed(this),
                shareReplay(1),
              )
            : of([]);

          const enableValueType = this.enableValueTypeMap[col.type];
          const needTransformConditionByIds = ['status', 'poStatus', 'docStatus', 'editingStatus'];

          return {
            id: col.id,
            headerName: col.label,
            headerComponentParams: {
              filterConditions$: of(filterMap[col.fieldId]),
              filter: this.canFilterMap[col.type.replace('com.core.cbx.sharefile.ui.', '')],
              filterType: this.filterTypeMap[col.type],
              applyFilter: (filters: SearchFilter[], filterCodeList: SearchFilter[]) =>
                this.filterBus$.next({ fieldId: col.fieldId, filters, filterCodeList }),
              codelistOptions$,
              hasCodelist,
              sortable: col.sortable,
              needTransformCondition: R.includes(col.fieldId, needTransformConditionByIds),
              fieldDefine: col,
            },
            filter: !!filterMap[col.fieldId],
            floatingFilter: !!filterMap[col.fieldId],
            floatingFilterComponent: FloatingFilterComponent,
            floatingFilterComponentParams: {
              filterConditions: filterMap[col.fieldId],
              suppressFilterButton: true,
            },
            pinned: col.frozenType ? col.frozenType : null,
            field: col.fieldId,
            hide: !col.visible,
            width: width + (col.sortable ? viewColumnSortingIconWidth : 0),
            minWidth,
            maxWidth: width * this.maxWidthRatio,
            resizable: !this.fixedSizeMap[col.type] && (col.resizable ?? true),
            sort: sortingCols[col.fieldId] ?? null,
            cellRenderer,
            cellRendererParams: { col, moduleId },
            valueFormatter,
            comparator: () => 0,
            cellClass: col.fieldId === 'companyWebsite' ? 'hyperlink' : this.cellClassMap[col.type],
            enableRowGroup: true, // whether can drop in the row group bar
            enablePivot: true, // whether can drop in pivot mode
            enableValue: enableValueType, // allows column to be dragged to the 'Values` section of the Columns Tool Panel
            // allowedAggFuncs: ['sum', 'min', 'max', 'count', 'avg', 'first', 'last'],
          } as ColDef;
        });
    }),
    untilDestroyed(this),
    shareReplay(1),
  );

  detailViewColumnDefs$: Observable<ColDef[]> = combineLatest([
    this.param$,
    this.columns$,
    this.sortings$,
    this.moduleId$,
    this.filterMap$,
    this.enabledConcurrent$,
  ]).pipe(
    auditTime(0),
    map(([param, columns, sortings = [], moduleId, filterMap, enabledConcurrent]) => {
      if (!moduleId) {
        return [];
      }
      const { tabId } = param;
      const sortingCols = Object.fromEntries(sortings.map(({ fieldId, type }) => [fieldId, type]));

      const valueFormatterMapByType: ColumnTypeMap<((params: ValueFormatterParams) => string) | string> = {
        RefNo: (params: ValueFormatterParams) => {
          const colId = params.colDef.cellRendererParams.col.fieldId;
          const docRef = params.data?.refNo || params.data?.docRef || params.data?.transactionRef;
          const version = params.data?.version;

          if (isEmptyOrNil(docRef)) {
            console.warn(`refNo or docRef is required in RefNo field (${colId})`);
            return '';
          }

          if (isEmptyOrNil(version) && !enabledConcurrent) {
            console.warn(`version is required in RefNo field (${colId})`);
          }

          let link: string;
          if (moduleId === 'docTransaction') {
            const newModuleId = params.data?.transactionRefType.toString();
            const lastModuleId = newModuleId?.replace(newModuleId[0], newModuleId[0].toLowerCase());
            const lastNavi = this.navigationService.naviModuleMap[lastModuleId];

            link = `/document/${lastNavi}/${lastModuleId}/${docRef}`;
          } else {
            link = `/document/${tabId}/${moduleId}/${docRef}`;
          }

          return enabledConcurrent ? link : `${link}/${version ?? ''}`;
        },
        Hyperlink: (params) => this.getFormatValueForHyperlink(params),
        HyperlinkOrNew: (params) => this.getFormatValueForHyperlink(params),
        HyperlinkMultiple: (params) => this.getFormatValueForHyperlink(params),
        Thumbnail: (params: ValueFormatterParams) => compatibleUrl(params.value),
        ColorChip: (params: ValueFormatterParams) => {
          if (isEmptyOrNil(params.value)) {
            return null;
          }
          const [firstChar] = params.value;
          return firstChar === '#' ? params.value : `#${params.value}`;
        },
        Number: (params: ValueFormatterParams) => this.getFormatValueForNumber(params, '.0-0'),
        Decimal: (params: ValueFormatterParams) => {
          const format = params.colDef?.cellRendererParams?.col?.format ?? '.4-4';
          return this.getFormatValueForNumber(params, format);
        },
        NotificationReferencesLink: (params: ValueFormatterParams) => {
          const refDocId = params.data?.refDocId;
          // just design for home notification listing page
          return refDocId;
        },
        Percentage: (params: ValueFormatterParams) => {
          if (Number.isNaN(Number(params.value)) || isEmptyOrNil(params.value)) {
            return params.value;
          }

          return `${new Decimal(params.value ?? 0).mul(100).toNumber()}%`;
        },
      };

      return columns
        .filter((col) => col.isDetailViewColumn)
        .map((col) => {
          const width = getViewFieldWidth(col.type, col.size);
          const minWidth = Math.min(getHeaderMinWidth(col.label), width * this.maxWidthRatio);

          const cellRenderer =
            this.cellRendererMapByType[col.type.replace('com.core.cbx.sharefile.ui.', '')] || 'textComponent';
          const valueFormatter =
            valueFormatterMapByType[col.type] ||
            getValueFormatter(col?.dataFormat, this.translocoService.translate.bind(this.translocoService));

          const hasCodelist =
            (col.visible &&
              ((col.dataFormat &&
                (col.dataFormat?.includes('bookName') ||
                  col.fieldId === 'editingStatus' ||
                  col.fieldId === 'docStatus' ||
                  ((col.fieldId === 'status' || col.fieldId === 'poStatus') &&
                    col.dataFormat?.includes('labelIdPrefix=lbl.status.')))) ||
                col.fieldId === 'productCategory')) ||
            col.dataFormat?.includes('labelIdPrefix') ||
            col.dataFormat?.includes('labelPrefix');

          const codelistOptions$ = hasCodelist
            ? this.codelistService.getCodelistOptions(this.moduleId, this.viewName, col.fieldId).pipe(
                map((result) => {
                  const r = result[col.fieldId] ?? [];
                  return R.sort(stringAsc, r);
                }),
                catchError(() => of([])),
                untilDestroyed(this),
                shareReplay(1),
              )
            : of([]);

          const enableValueType = this.enableValueTypeMap[col.type];
          const needTransformConditionByIds = ['status', 'poStatus', 'docStatus', 'editingStatus'];

          return {
            id: col.id,
            headerName: col.label,
            headerComponentParams: {
              filterConditions$: of(filterMap[col.fieldId]),
              filter: this.canFilterMap[col.type.replace('com.core.cbx.sharefile.ui.', '')],
              filterType: this.filterTypeMap[col.type],
              codelistOptions$,
              hasCodelist,
              sortable: false,
              needTransformCondition: R.includes(col.fieldId, needTransformConditionByIds),
              fieldDefine: col,
            },
            filter: !!filterMap[col.fieldId],
            floatingFilter: !!filterMap[col.fieldId],
            floatingFilterComponent: FloatingFilterComponent,
            floatingFilterComponentParams: {
              filterConditions: filterMap[col.fieldId],
              suppressFilterButton: true,
            },
            pinned: col.frozenType ? col.frozenType : null,
            field: col.fieldId,
            hide: !col.visible,
            width: width + (col.sortable ? viewColumnSortingIconWidth : 0),
            minWidth,
            maxWidth: width * this.maxWidthRatio,
            resizable: !this.fixedSizeMap[col.type] && (col.resizable ?? true),
            sort: sortingCols[col.fieldId] ?? null,
            cellRenderer,
            cellRendererParams: { col, moduleId },
            valueFormatter,
            comparator: () => 0,
            cellClass: col.fieldId === 'companyWebsite' ? 'hyperlink' : this.cellClassMap[col.type],
            enableRowGroup: true, // whether can drop in the row group bar
            enablePivot: true, // whether can drop in pivot mode
            enableValue: enableValueType, // allows column to be dragged to the 'Values` section of the Columns Tool Panel
            // allowedAggFuncs: ['sum', 'min', 'max', 'count', 'avg', 'first', 'last'],
          } as ColDef;
        });
    }),
    untilDestroyed(this),
    shareReplay(1),
  );

  hasVisibleThumbnailColumn$ = this.columnDefs$.pipe(
    map((colDefs) => colDefs.some((colDef) => colDef.cellRendererParams.col.type === 'Thumbnail' && !colDef.hide)),
  );

  isDetailMode: boolean;
  isDetailMode$ = this.mode$.pipe(map((mode) => mode === 'detail'));

  isTallRow$ = combineLatest([this.isDetailMode$, this.hasVisibleThumbnailColumn$, this.isCpmMode$]).pipe(
    auditTime(0),
    map(
      ([isDetailMode, hasVisibleThumbnailColumn, isCpmMode]) => isDetailMode || hasVisibleThumbnailColumn || isCpmMode,
    ),
    distinctUntilChanged(),
  );

  rowHeight: number;
  rowHeight$ = this.isTallRow$.pipe(
    map((isTallRow) => (isTallRow ? 92 : 38)),
    tap((rowHeight) => {
      this.rowHeight = rowHeight;
    }),
  );

  oneLine$ = this.isTallRow$.pipe(notMap);

  thumbnailColumn$ = combineLatest([this.isDetailMode$, this.moduleId$]).pipe(
    auditTime(0),
    filter(([, moduleId]) => !!moduleId),
    map(([isDetailMode, moduleId]) => {
      const detailCol = moduleId === 'color' ? this.colorCol : this.thumbnailCol;
      return isDetailMode && moduleId ? [detailCol] : [];
    }),
  );

  fixedColumnDefs$ = combineLatest([this.thumbnailColumn$, this.isShowDetailView$]).pipe(
    auditTime(0),
    map(([thumbnailColumn, isShowDetailView]) => {
      if (isShowDetailView) {
        return [this.detailViewIconCol, this.actionCol, ...thumbnailColumn];
      }

      return [this.actionCol, ...thumbnailColumn];
    }),
    distinctUntilChanged(),
  );

  displayColumnDefs$ = combineLatest([this.fixedColumnDefs$, this.columnDefs$]).pipe(
    auditTime(0),
    map(([fixedColumnDefs, columnDefs]) => [...fixedColumnDefs, ...columnDefs]),
  );

  records$ = this.recordService.records$;
  loading$ = this.recordService.loading$;
  cpmRecords$ = this.recordService.cpmDocs$.pipe(
    pairwise(),
    switchMap(([previouse, cpmDocs]) =>
      isEmptyOrNil(cpmDocs)
        ? of(null)
        : this.getNewCpmDoc(R.differenceWith((pre, curr) => pre.cpmId === curr.cpmId, cpmDocs, previouse)),
    ),
    // map(([cpmDocs, newCpmDocs]) => this.filterViewCpmRecords(cpmDocs, newCpmDocs)),
    untilDestroyed(this),
    shareReplay(1),
  );

  cpmEndToEndViewreferenceRowData$ = this.records$.pipe(
    pairwise(),
    switchMap(([previouse, records]) =>
      this.tabId === 'cpm'
        ? isEmptyOrNil(records)
          ? of(null)
          : this.getCpmEndToEndViewReferenceRowData(
              this.moduleId,
              this.viewName,
              R.differenceWith(
                (pre, curr) =>
                  isEmptyOrNil(pre?.docId) || isEmptyOrNil(curr?.docId)
                    ? pre.refNo === curr.refNo
                    : pre?.docId === curr?.docId,
                records,
                previouse,
              ),
            )
        : of([]),
    ),
    untilDestroyed(this),
    shareReplay(1),
  );

  download$ = new Subject<boolean>();

  naviTabLabel$ = this.navigationService.tab$.pipe(
    filter((tab) => !!tab),
    map(({ label }) => label),
  );

  naviTabId$ = this.navigationService.tab$.pipe(
    filter((tab) => !!tab),
    map(({ id }) => id),
  );
  notCpmTab$ = this.naviTabId$.pipe(map((tabId) => tabId !== 'cpm'));
  selections: Record[];
  naviModuleLabel;
  naviModuleLabel$ = this.navigationService.module$.pipe(
    filter((module) => !!module),
    map(({ label }) => label),
    tap((naviModuleLabel) => {
      this.naviModuleLabel = naviModuleLabel;
    }),
  );
  naviViewOptions$ = this.navigationService.module$.pipe(
    filter((module) => !!module),
    map(({ menuItems }) => menuItems),
    map((menuItems) => menuItems.filter((menuItem) => !menuItem.isBookmarkView)),
  );

  isCpm$ = this.naviTabId$.pipe(map((id) => id === 'cpm'));
  notCpm$ = this.isCpm$.pipe(map((isCpm) => !isCpm));

  notCpmAndNotVpoCpm$ = combineLatest([this.notCpm$, this.notVpoCpm$]).pipe(
    map(([notCpm, notVpoCpm]) => notCpm && notVpoCpm),
  );

  bookmarkNaviViewOptions$ = combineLatest([
    this.navigationService.bookmarkNaviTabs$.pipe(filter(isNotNil)),
    this.navigationService.tabId$,
    this.navigationService.moduleId$,
  ]).pipe(
    map(
      ([bookmarkNaviTabs, tabId, moduleId]) =>
        bookmarkNaviTabs.find((tab) => tab.id === tabId)?.menuGroups.find((group) => group.id === moduleId)?.menuItems,
    ),
  );

  naviView$ = this.navigationService.view$;
  naviView2$ = combineLatest([this.navigationService.module$.pipe(filter((module) => !!module)), this.viewName$]).pipe(
    filter(([, viewName]) => viewName !== ''),
    map(([module, viewName]) => module.menuItems.find((view) => view.id === viewName)),
  );
  naviLoading$ = this.navigationService.actionLoading$;

  hasViewAccess$ = this.naviView$.pipe(
    startWith(''),
    switchMap((viewName) =>
      isEmptyOrNil(viewName) ? this.navigationService.isBookmarkView$ || this.isInboxView$ : of(true),
    ),
  );

  hasViewButtonAccess$ = this.naviView$.pipe(
    startWith(''),
    switchMap((viewName) => (isEmptyOrNil(viewName) ? this.isInboxView$ : of(true))),
  );

  defaultExcelExportParams: ExcelExportParams;

  excelStyles: ExcelStyle[] = [
    {
      id: 'header',
    },
    {
      id: 'cell',
      alignment: {
        vertical: 'Top',
      },
    },
    {
      id: 'hyperlink',
      font: {
        underline: 'Single',
        color: '#046dc9',
      },
      dataType: 'Formula',
    },
    {
      id: 'excelTextFormat',
      dataType: 'String',
    },
    {
      id: 'percentage',
      dataType: 'Number',
      alignment: {
        horizontal: 'Right',
      },
      numberFormat: {
        format: '0%',
      },
    },
  ];

  suppressAggFuncInHeader = true;

  groupSelects: GroupSelectionMode = 'filteredDescendants';

  rowClassRules: any;

  sideBar$ = combineLatest([this.viewName$, this.domainAttributeService.rowGroupingView$]).pipe(
    map(([viewName, rowGroupingView]) =>
      rowGroupingView?.includes(viewName)
        ? ({
            toolPanels: [
              {
                id: 'columns',
                labelDefault: 'Columns',
                labelKey: 'columns',
                iconKey: 'columns',
                toolPanel: 'agColumnsToolPanel',
                toolPanelParams: {
                  suppressPivotMode: true, // <-- suppress pivot!
                  suppressColumnSelectAll: true,
                },
              },
            ],
          } as SideBarDef)
        : false,
    ),
  );

  autoGroupColumnDef: ColDef = {
    lockPosition: 'left',
    pinned: 'left',
    filter: false,
    floatingFilter: false,
  };

  aggFuncs = {
    sum: this.sumFunction,
    min: this.minFunction,
    max: this.maxFunction,
    count: this.countFunction,
    avg: this.avgFunction,
    first: this.firstFunction,
    last: this.lastFunction,
  };

  selectAllReadyCompleted$ = this.selectAllReady$
    .pipe(
      switchMap(({ payload }) =>
        // console.log('this.selectAllReady$ pipe');
        of({ payload }),
      ),
      debounceTime(1000),
      untilDestroyed(this),
    )
    .subscribe({
      next: () => {
        // console.log('this.selectAllReady$ subscribe');
        this.gridApi.selectAll();
        this.selectAll = false;
      },
    });

  codelistItems: any[];
  codelistItems$ = this.documentService
    .getCodelistByNameWithCache$('CPM_TASK_STATUS')
    .pipe(
      tap((codelistItems) => {
        this.codelistItems = codelistItems;
      }),
      untilDestroyed(this),
      shareReplay(1),
    )
    .subscribe();

  exportReadyCompleted$ = this.exportReady$
    .pipe(
      switchMap(({ payload }) => {
        const thumbnailColumns = this.columns.filter((column) => column.type === 'Thumbnail');
        const rowNodes = [];
        if (payload.onlySelected) {
          const selectData = this.gridApi.getSelectedNodes().map((rowNode) => rowNode.data);
          rowNodes.push(...selectData);
        } else {
          rowNodes.push(...this.recordService.state.records);
        }
        const urls: { value: string; url: string }[] = [];
        rowNodes.forEach((rowNode) => {
          const hardCodeThumbnail = rowNode.thumbnailUrl || rowNode.thumbnail;
          const hardCodeUrl = compatibleUrl(hardCodeThumbnail);
          if (hardCodeUrl && (payload.allColumns || this.isDetailMode)) {
            urls.push({ value: hardCodeThumbnail, url: hardCodeUrl });
          }

          thumbnailColumns.forEach((column) => {
            const value = rowNode[column.fieldId];
            const url = compatibleUrl(value);
            if (url) {
              urls.push({ value, url });
            }
          });
        });

        this.exporting$.next(true);

        const imageAndPayload$ = urls.length
          ? forkJoin(R.splitEvery(200, urls).map((subUrls) => this.fileService.downloadToExcel$(subUrls))).pipe(
              map((imageMap) => ({ imageMap: R.mergeAll(imageMap), payload })),
            )
          : of({ imageMap: {}, payload });

        return forkJoin([imageAndPayload$]);
      }),
      debounceTime(1000),
      untilDestroyed(this),
    )
    .subscribe({
      next: ([{ imageMap, payload }]) => {
        const getRefNoHyperlink = (data) => {
          const docRef = data?.refNo || data?.docRef || data?.transactionRef;
          const version = data?.version;
          if (this.moduleId === 'docTransaction') {
            const newModuleId = data?.transactionRefType.toString();
            const lastModuleId = newModuleId?.replace(newModuleId[0], newModuleId[0].toLowerCase());
            const lastNavi = this.navigationService.naviModuleMap[lastModuleId];
            return isEmptyOrNil(docRef) ? '' : `/document/${lastNavi}/${lastModuleId}/${docRef}/${version ?? ''}`;
          }

          return isEmptyOrNil(docRef) ? '' : `/document/${this.tabId}/${this.moduleId}/${docRef}/${version ?? ''}`;
        };
        const getHyperlink = (data, column) => {
          const { actionParams } = column;

          if (!actionParams) {
            return '';
          }

          const { naviModule, moduleId: module, fieldId, refNo, version } = actionParams;

          let docRef: any;
          let docVer: any;
          if (data) {
            docRef = data[refNo || fieldId];
            docVer = data[version];
          }

          return isEmptyOrNil(docRef)
            ? ''
            : `/document/${naviModule || this.tabId}/${module}/${docRef}/${docVer ?? ''}`;
        };

        const refNoColumns = this.columns.filter((column) => column.type === 'RefNo');
        const hyperlinkColumns = this.columns.filter(
          (column) => column.type === 'Hyperlink' || column.type === 'HyperlinkOrNew',
        );
        const hyperlinksByRowNode = new WeakMap<IRowNode, { [fieldId: string]: string }>();
        const rowNodes: IRowNode[] = [];
        if (payload.onlySelected) {
          rowNodes.push(...this.gridApi.getSelectedNodes());
        } else {
          this.gridApi.forEachNode((rowNode) => rowNodes.push(rowNode));
        }
        rowNodes.forEach((rowNode) => {
          const current = hyperlinksByRowNode.get(rowNode) ?? {};
          if (isNotEmptyOrNil(refNoColumns)) {
            refNoColumns.forEach((column) => {
              current[column.fieldId] = getRefNoHyperlink(rowNode.data);
            });
          }
          hyperlinkColumns.forEach((column) => {
            current[column.fieldId] = getHyperlink(rowNode.data, column);
          });
          hyperlinksByRowNode.set(rowNode, current);
        });

        const imageExp =
          /\/(?<filename>[^/]*).(?<extension>jpg|JPG|webp|WEBP|jpeg|JPEG|png|PNG|gif|GIF|jfif|JFIF|bmp|BMP|tif|TIF)/;

        const filterColumnKey = {
          [this.actionColKey]: true,
          [this.detailViewIconColKey]: true,
        };

        const filterStrategy = payload.allColumns ? () => true : (column: ColumnState) => !column.hide;

        const manuallyControlKeys = this.gridApi
          .getColumnState()
          .filter((column) => !filterColumnKey[column.colId])
          .filter(filterStrategy)
          .map((column) => column.colId);

        this.gridApi.exportDataAsExcel({
          ...payload,
          columnKeys: manuallyControlKeys,
          sheetName: payload.fileName,
          rowHeight: this.rowHeight,
          headerRowHeight: 32,
          processCellCallback: (params) => {
            const colDef = params.column.getColDef();
            try {
              if (colDef.cellClass === 'hyperlink') {
                const path = hyperlinksByRowNode.get(params.node)[colDef.field];
                let displayValue = params.value ? String(params.value).valueOf() : '';
                if (!this.notDocTransactionView) {
                  return params.value;
                }
                // displayValue = displayValue.replace(new RegExp('&', 'g'), '&amp;');
                // displayValue = displayValue.replace(new RegExp('<', 'g'), '&lt;');
                // displayValue = displayValue.replace(new RegExp('>', 'g'), '&gt;');
                // displayValue = displayValue.replace(new RegExp('"', 'g'), '&quot;');
                // displayValue = displayValue.replace(new RegExp("'", 'g'), '&apos;');
                displayValue = displayValue.replace(/"/g, '""');
                return path
                  ? `=HYPERLINK("${this.window.location.protocol}//${this.window.location.host}${path}", "${displayValue}")`
                  : params.value
                  ? `=HYPERLINK("${this.window.location.protocol}//${this.window.location.host}//${displayValue}", "${displayValue}")`
                  : displayValue
                  ? `=HYPERLINK("${displayValue}")`
                  : ``;
              }
              if (colDef.cellRendererParams?.col?.type === 'Label') {
                if (colDef.cellRendererParams?.col?.valueType === 'string' && params.value === 'true') {
                  return colDef.cellRendererParams.col.displayLabelMap
                    ? colDef.cellRendererParams.col.displayLabelMap[params.value]
                    : null;
                }
                if (
                  colDef.cellRendererParams?.col?.valueType === 'string' &&
                  (params.value === 'false' || params.value === null)
                ) {
                  return colDef.cellRendererParams.col.displayLabelMap
                    ? colDef.cellRendererParams.col.displayLabelMap[params.value === null ? false : params.value]
                    : null;
                }
                if (colDef.cellRendererParams?.col?.valueType === 'boolean') {
                  return colDef.cellRendererParams.col.displayLabelMap
                    ? colDef.cellRendererParams.col.displayLabelMap[params.value === null ? false : params.value]
                    : null;
                }
                if (isEmptyOrNil(params.value)) {
                  return '';
                }
                return colDef.cellRendererParams?.col?.displayLabelMap
                  ? colDef.cellRendererParams.col.displayLabelMap[params.value]
                  : params.value;
              }
              if (colDef.cellRendererParams?.col?.type === 'Datetime') {
                return params.value ? this.dateTimeFormatService.transform(params.value, 'datetime') : '';
              }
              if (colDef.cellRendererParams?.col?.type === 'Date') {
                return params.value ? this.dateTimeFormatService.transform(params.value, 'date') : '';
              }
              if (this.tabId === 'cpm' && typeof colDef.cellRendererParams?.col?.type === 'undefined') {
                if (colDef.cellRenderer === 'cpmDateCellComponent') {
                  return params.value ? this.dateTimeFormatService.transform(params.value, 'date') : '';
                }

                if (colDef.cellRenderer === 'cpmStatusCellComponent' && params.value) {
                  return this.codelistItems?.find((codelistItem) => codelistItem.code === params.value)?.name ?? '';
                }
              }
              return params.value ?? '';
            } catch (e) {
              console.warn(`error : ${colDef.field}`, e);
              return params.value ?? '';
            }
          },
          addImageToCell: (rowIndex, column, value) => {
            if (column.getColDef().cellClass === 'image-cell') {
              if (imageMap[value]) {
                const { filename, extension } = value.match(imageExp)?.groups ?? {};
                if (extension) {
                  return {
                    image: {
                      id: `${filename}_${rowIndex}`,
                      base64: imageMap[value],
                      imageType: extension.toLowerCase() as
                        | 'jpg'
                        | 'jpeg'
                        | 'png'
                        | 'gif'
                        | 'jfif'
                        | 'tif'
                        | 'bmp'
                        | 'webp',
                      fitCell: true,
                    },
                  };
                }
              }
            }
            if (
              column.getColDef().cellRenderer === 'colorComponent' &&
              value !== null &&
              value !== undefined &&
              value !== ''
            ) {
              const snapshotCanvas = this.renderer.createElement('canvas');
              snapshotCanvas.width = '20';
              snapshotCanvas.height = '20';
              const context = snapshotCanvas.getContext('2d');
              context.fillStyle = `#${value}`;
              context.fillRect(0, 0, snapshotCanvas.width, snapshotCanvas.height);
              const shotURL = snapshotCanvas.toDataURL('png');
              return {
                image: {
                  id: `${value}`,
                  base64: shotURL,
                  fitCell: true,
                },
              };
            }
            this.exporting$.next(false);
            return undefined;
          },
        });
      },
    });

  totalSizeUpdated$ = this.totalSize$.pipe(untilDestroyed(this)).subscribe((val) => {
    this.recordCount = val;
  });

  loadingStream$ = this.loading$.pipe(throttleTime(10));

  loadingStreamCompleted$ = this.loadingStream$.pipe(untilDestroyed(this)).subscribe((val) => {
    // console.log('loadingStreamCompleted : ' + val);
    if (!val && this.tabId !== 'cpm') {
      this.loadCount += 1;
      // console.log('loadingStreamCompleted : ' + this.loadCount + '/' + this.loadTill);
      this.checkForExport();
    }
  });

  defaultColDef: ColDef = {
    wrapHeaderText: true,
  };

  fileInput$ = new BehaviorSubject<HTMLElement>(null);
  fileList$ = new Subject<FileList>();
  files$ = this.fileList$.pipe(map((fileList) => fileList && Array.from(fileList)));

  ngOnInit() {
    this.handleTitle();

    this.handleFetchRecord();

    this.handleFilterRoute();

    this.handleLoadCpmEndToEndViewData();

    this.isDetailMode$.subscribe((isDetailMode) => {
      this.isDetailMode = isDetailMode;
    });

    if (this.keepQueryParamsInListingPage) {
      this.handleQueryStringToLocalStorage();
    }
    if (this.viewName === 'docTransactionView') {
      this.notDocTransactionView = false;
      this.navigationService.getNaviModuleMap().subscribe((data) => {
        this.navigationService.naviModuleMap = data;
      });
    }
    if (this.viewName === 'newInboxView') {
      this.isNotNotificationView = false;
    }
    this.reLoad();

    this.getFormatbyDataAbstract().subscribe((format) => {
      this.formatByDataAbstract = format;
    });

    this.isOpenSubTasks$
      .pipe(
        filter(isNotEmptyOrNil),
        skip(1),
        debounceTime(200),
        distinctUntilChanged(deepEqual),
        tap((isOpenSubTasks: boolean) => this.loadCpmIsOpenSubTasks$.next(isOpenSubTasks)),
        filter(() => {
          if (this.skipUpdateIsOpenSubTasks) {
            this.skipUpdateIsOpenSubTasks = false;
            return false;
          }
          return true;
        }),
        switchMap((isOpenSubTasks: boolean) => this.cpmService.updateCpmIsSubTaskOn(isOpenSubTasks).pipe(take(1))),
      )
      .subscribe();

    this.cpmService
      .loadCpmIsSubTaskOn()
      .pipe(take(1), untilDestroyed(this))
      .subscribe((isSubTaskOn) => {
        this.isOpenSubTasks$.next(isSubTaskOn);
        this.loadCpmIsOpenSubTasks$.next(isSubTaskOn);
      });
  }

  private reLoad() {
    this.notificationMarkAsReadService.needReload$.pipe(untilDestroyed(this)).subscribe((needReload) => {
      if (needReload) {
        this.handleFetchRecord();
      }
    });
  }

  naviView({ type, ...params }: { type: string; [prop: string]: any }) {
    if (params.id === 'createView') {
      return this.editView(false);
    }

    if (type === 'NaviView') {
      if (params?.naviEntryId === 'dataAbstractConfig') {
        this.router.navigateByUrl(`/document/setup/dataAbstractConfig/${this.authService.state.userInfo.domain_id}`);
      }
      const bookmarkq = params?.bookmarkq;
      const bookmarkQueryParam = this.navigationService.getParamsMap(bookmarkq);
      if (isNotNil(bookmarkQueryParam)) {
        this.router.navigate(['../', params.id], {
          relativeTo: this.route,
          queryParams: {
            cbxql: bookmarkQueryParam.get('cbxql'),
            q: bookmarkQueryParam.get('q'),
            dateRange: bookmarkQueryParam.get('dateRange'),
          },
        });
      } else {
        this.router.navigate(['../', params.id], {
          relativeTo: this.route,
        });
      }
    }
    return null;
  }

  setAsDefaultView(view: NaviView) {
    this.navigationService.setAsDefaultView(view.id);
  }

  rowSelected(event: RowSelectedEvent) {
    this.selections = event.api.getSelectedRows();
  }

  onMenuClick(menu: ViewActionDto) {
    const {
      warningNoSelectionDialogMeta,
      warningNoLatestVersionDialogMeta,
      warningCanSelectOnlyOneRecordMeta,
      warningItemNoActiveAConfirmedALatestAOrderDialogMeta,
      confirmDialogMeta,
      warningValidationDialogMeta,
      warningSelectionDiffDialogMeta,
      warningAnyMatchDialogMeta,
      actionId,
      warningStatusDialogMeta,
      warningVpoActiveViewDialogMeta,
      warningNotConfirmedDialogMeta,
      warningDocStatusIsInactiveDialogMeta,
      warningNotLatestDialogMeta,
      warningVpoSameVendorViewDialogMeta,
    } = menu;
    const selections = this.selections || [];
    const hasNoSelection = !selections.length;

    if (warningNoSelectionDialogMeta && hasNoSelection) {
      this.openMessageDialogAndConfirm(warningNoSelectionDialogMeta);
      return;
    }

    if (warningCanSelectOnlyOneRecordMeta && selections.length > 1) {
      this.openMessageDialogAndConfirm(warningCanSelectOnlyOneRecordMeta);
      return;
    }

    // WUN-3351, WUN-2621
    if (isNotEmptyOrNil(warningVpoActiveViewDialogMeta?.conditions) && !warningNotLatestDialogMeta) {
      const { conditions: vpoConditions } = warningVpoActiveViewDialogMeta;
      const poStatusCondition = [];
      const generalConditions = [];
      vpoConditions.forEach((condition) => {
        if (condition.id === 'poStatus') {
          poStatusCondition.push(condition);
        } else {
          generalConditions.push(condition);
        }
      });

      const hasWarning = selections.some((selection) => {
        const generalResult = isNotEmptyOrNil(generalConditions)
          ? checkViewActionConditions(generalConditions, selection, 'SOME', true)
          : false;
        const poStatusResult = isNotEmptyOrNil(poStatusCondition)
          ? checkViewActionConditions(poStatusCondition, selection, 'EVERY', true)
          : false;
        return generalResult || poStatusResult;
      });

      if (hasWarning) {
        this.openMessageDialogAndConfirm(warningVpoActiveViewDialogMeta);
        return;
      }
    }

    if (warningVpoSameVendorViewDialogMeta && selections.length > 1) {
      const codeString = `${selections[0].vendorCode}_${selections[0].custCode}`;
      let hasWarning = false;

      selections.forEach((selection) => {
        if (codeString !== `${selection.vendorCode}_${selection.custCode}`) {
          hasWarning = true;
        }
      });

      if (hasWarning) {
        this.openMessageDialogAndConfirm(warningVpoSameVendorViewDialogMeta);
        return;
      }
    }

    // WUN-2621 issue #1, #2
    if (isNotEmptyOrNil(warningStatusDialogMeta?.conditions)) {
      const { conditions: statusConditions } = warningStatusDialogMeta;
      const hasWarning = selections.some((selection) => checkViewActionConditions(statusConditions, selection, 'SOME'));

      if (hasWarning) {
        this.openMessageDialogAndConfirm(warningStatusDialogMeta);
        return;
      }
    }

    // WUN-2619 issue #1
    if (isNotEmptyOrNil(warningNotConfirmedDialogMeta?.conditions)) {
      const { conditions: confirmCondition } = warningNotConfirmedDialogMeta;
      const moduleName = capitalizeLetter(this.moduleId ?? '');
      const isNotConfirmed = selections
        .filter((selection) => checkViewActionConditions(confirmCondition, selection, 'SOME', true))
        .map((record) => `${moduleName}: ${record.vpoNo || record.mpoNo || record.refNo || record.docRef}`)
        .join('<br/>');

      if (isNotEmptyOrNil(isNotConfirmed)) {
        this.openMessageDialogAndConfirm(warningNotConfirmedDialogMeta, [isNotConfirmed]);
        return;
      }
    }

    // WUN-2619 issue #2
    if (isNotEmptyOrNil(warningDocStatusIsInactiveDialogMeta?.conditions)) {
      const { conditions: docStatusConditions } = warningDocStatusIsInactiveDialogMeta;
      const hasWarning = selections.some((selection) =>
        checkViewActionConditions(docStatusConditions, selection, 'SOME', true),
      );

      if (hasWarning) {
        this.openMessageDialogAndConfirm(warningDocStatusIsInactiveDialogMeta);
        return;
      }
    }

    // WUN-2619 issue #3
    if (isNotEmptyOrNil(warningNoLatestVersionDialogMeta?.conditions)) {
      const { conditions: latestConditions } = warningNoLatestVersionDialogMeta;
      const hasWarning = selections.some((selection) =>
        checkViewActionConditions(latestConditions, selection, 'SOME', true),
      );

      if (hasWarning) {
        const moduleNames = {
          vpo: 'Vendor Purchase Order',
          mpo: 'Vendor Master Orders',
        };
        const moduleName = moduleNames[menu.actionParams.moduleId] || menu.label;
        this.openMessageDialogAndConfirm(warningNoLatestVersionDialogMeta, [moduleName]);
        return;
      }
    }

    if (isNotEmptyOrNil(warningSelectionDiffDialogMeta?.conditions)) {
      const { conditions: diffConditions } = warningSelectionDiffDialogMeta;
      const uniqueData = selections.filter((selection) => checkViewActionConditions(diffConditions, selection, 'SOME'));
      const distinct = new Set(uniqueData.map((data) => data.vendorRef));

      if (distinct.size > 1) {
        this.openMessageDialogAndConfirm(warningSelectionDiffDialogMeta);
        return;
      }
    }

    if (isNotEmptyOrNil(warningItemNoActiveAConfirmedALatestAOrderDialogMeta?.conditions)) {
      const { conditions: itemConditions } = warningItemNoActiveAConfirmedALatestAOrderDialogMeta;
      const noActiveAConfirmedALatestAOrderRecords = selections
        .filter((selection) => checkViewActionConditions(itemConditions, selection, 'SOME', true))
        .map((record) => `Item: ${record.refNo ?? record.docRef}`)
        .join('<br/>');

      if (noActiveAConfirmedALatestAOrderRecords) {
        this.openMessageDialogAndConfirm(warningItemNoActiveAConfirmedALatestAOrderDialogMeta, [
          '<br/>'.concat(noActiveAConfirmedALatestAOrderRecords),
        ]);
        return;
      }
    }

    if (isNotEmptyOrNil(warningAnyMatchDialogMeta?.conditions)) {
      const { conditions: anyMatchConditions } = warningAnyMatchDialogMeta;
      const hasWarning = selections.some((selection) =>
        checkViewActionConditions(anyMatchConditions, selection, 'SOME'),
      );

      if (hasWarning) {
        this.openMessageDialogAndConfirm(warningAnyMatchDialogMeta);
        return;
      }
    }

    if (warningValidationDialogMeta && !warningNotLatestDialogMeta) {
      this.checkValidationForRecords(menu, selections, actionId, warningValidationDialogMeta, confirmDialogMeta);
    } else if (menu.actionId === 'VpoBatchNewShipmentBookingDoc') {
      // WUN-6225
      this.checkVpoListAllIsLatest(menu, warningNotLatestDialogMeta, selections);
    } else if (menu.actionId === 'QqBatchSearchNewRfqDoc') {
      let itemStatusValidatorMessage =
        'Below record(s) is not available to create / add to RFQ since the document is not active and confirmed.';
      let itemisInactiveOrDraft = false;
      selections.forEach((selection) => {
        this.documentService.getViewData('qq', selection.refNo, selection.version).subscribe((data) => {
          data.qqItemList.forEach((qqItem) => {
            if (qqItem.item.latestDocStatus === 'inactive' || qqItem.item.latestEditingStatus === 'draft') {
              itemisInactiveOrDraft = true;
              itemStatusValidatorMessage += `<br>Unknown macro: ${qqItem.item.refNo}`;
            }
          });
          if (itemisInactiveOrDraft) {
            this.openMessageDialog('Okay', itemStatusValidatorMessage, 'information');
          } else {
            this.handleAction(menu, selections);
          }
        });
      });
    } else {
      this.openMessageDialogAndConfirm(confirmDialogMeta)
        .pipe(filter((confirmed) => confirmed))
        .subscribe(() => {
          this.handleAction(menu, selections);
        });
    }
  }

  loadMore() {
    this.loadMore$.next(true);
  }

  clearSearchResult() {
    this.cpmEndToEndql = null;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {
        cbxql: null,
        q: null,
      },
      queryParamsHandling: 'merge',
    });
  }

  private handleColumns(columnDefs: ColDef[]): ColDef[] {
    const indexedColumnDefs = Object.fromEntries(columnDefs.map((columnDef) => [columnDef.field, columnDef]));
    const table = this.cpmTable ?? this.table ?? this.cpmEndToEndViewComponent;

    return table.agGrid.api
      .getColumnState()
      .map((column) => {
        const col = indexedColumnDefs[column.colId.replace(/_\d?$/, '')];
        if (!col || col.lockPosition) {
          return null;
        }
        return { ...col, pinned: column.pinned, width: column.width };
      })
      .filter((columnDef) => !!columnDef);
  }

  openEditColumnDialog() {
    const newView$ = combineLatest([this.viewName$, this.columnDefs$]).pipe(
      take(1),
      mergeMap(([viewName, columnDefs]) => {
        const dialogRef = this.dialog.open<ColumnEditDialogResult, ColumnEditDialogData, ColumnEditDialogComponent>(
          ColumnEditDialogComponent,
          {
            ...defaultDialogConfig,
            maxWidth: '800px',
            width: '80vw',
            data: {
              viewName,
              columnDefs: this.handleColumns(columnDefs),
            },
          },
        );

        return dialogRef.closed;
      }),
      filter((result) => result?.type === 'done'),
      map((result) => result.payload.view),
    );

    this.handleUpdateColumns(newView$);
  }

  columnSort(sortings: ViewSorting[]) {
    const columnStates = sortings.map(
      (sort, i) => ({ colId: sort.fieldId, sort: sort.type, sortIndex: i } as ColumnState),
    );
    this.table?.agGrid.api.applyColumnState({ state: columnStates, defaultState: { sort: null } });
    this.cpmEndToEndViewComponent?.agGrid.api.applyColumnState({
      state: columnStates,
      defaultState: { sort: null },
    });
    this.viewName$
      .pipe(
        take(1),
        mergeMap((viewName) => this.listingService.setSortings(viewName, sortings)),
        switchMap(() => this.view$.pipe(take(1))),
        untilDestroyed(this),
      )
      .subscribe((view) => {
        this.viewSubject.next({ ...view, sortings });
        this.sortingActionSubject$.next();
      });
  }

  changeTimeRange(dateRange: string) {
    this.routeByQuery({
      dateRange: dateRange || null,
    });
  }

  changeResponsibleByMe(checked: boolean) {
    this.userService.updateUserResponsibleByMe(checked);
    this.isResponsibleByMe = checked;
  }

  changeFavorite(checked: boolean) {
    this.routeByQuery({
      filterByFavorite: checked || null,
    });
  }
  changeUnread(checked: boolean) {
    this.routeByQuery({
      filterByUnread: checked || null,
    });
  }
  changeMode(mode: ModeType) {
    this.switchMode$.next(mode);
    this.viewName$
      .pipe(
        first(),
        mergeMap((viewName) => this.listingService.setMode(viewName, mode, this.columns)),
      )
      .subscribe();
  }

  routeByQuery(condition: SearchCondition) {
    const { queryParamMap } = this.route.snapshot;
    const queryParams = Object.fromEntries(queryParamMap.keys.map((key) => [key, queryParamMap.get(key)]));

    this.router.navigate(['./'], {
      relativeTo: this.route,
      queryParams: {
        ...queryParams,
        ...condition,
      },
    });
  }

  private generateQueryString(queryParamMap: ParamMap, queryStringArray: string[]) {
    const queryStrings = queryParamMap.keys.map((key) => {
      const value =
        queryParamMap.get(key).indexOf('&') !== -1
          ? encodeURIComponent(queryParamMap.get(key))
          : queryParamMap.get(key);
      return `${key}=${value}`;
    });
    const resultQueryString = [...queryStringArray, ...queryStrings].join('&');

    return resultQueryString;
  }

  routeToDocument(refNo: string, version: string, extras?: NavigationExtras): Observable<any> {
    return refNo && version
      ? combineLatest([this.param$, this.moduleId$, this.enabledConcurrent$]).pipe(
          first(),
          concatMap(([{ tabId }, moduleId, enabledConcurrent]) =>
            this.documentService
              .getDataAccessiable(moduleId, refNo, version)
              .pipe(
                concatMap(() =>
                  from(
                    this.router.navigate(
                      ['/document', tabId, moduleId, refNo, enabledConcurrent ? null : version],
                      extras,
                    ),
                  ),
                ),
              ),
          ),
        )
      : throwError(() => ({ message: 'missing refNo or version' }));
  }

  handleCellAction({ type, params }: CellActionEvent) {
    switch (type) {
      case 'link': {
        const { col } = params.colDef.cellRendererParams;

        this.stateRecordService.getStateRecords(this.recordService.state?.records || []);

        if (col.type === 'RefNo') {
          this.stateRecordService.updateCurrentRowIndex(params.node.rowIndex);
        }

        if (
          col.type === 'HyperlinkBySupplier' ||
          col.type === 'Hyperlink' ||
          col.type === 'HyperlinkOrNew' ||
          col.type === 'HyperlinkMultiple'
        ) {
          const currentModule = this.moduleId;
          const openModuleId = col?.actionParams?.moduleId;
          const currentRecordRowIndex = currentModule === openModuleId ? params.node.rowIndex : -1;

          this.stateRecordService.updateCurrentRowIndex(currentRecordRowIndex);
        }

        if (col.type === 'HyperlinkOrNew' && isEmptyOrNil(params.value)) {
          const moduleId = col?.actionParams?.moduleId;
          const naviModule = col?.actionParams?.naviModule;
          const dmrNames = col?.actionParams?.dmrNames?.split(',') || [];
          const actionId = col?.action;

          const menu = {
            action: 'CreateWithData',
            actionId,
            actionParams: {
              naviModule,
              moduleId,
              dmrNames,
            },
            id: 'CreateWithData',
            label: 'CreateWithData',
            type: 'menuItem',
          } as unknown as ViewActionDto;

          this.handleAction(menu, [params.data]);

          break;
        }
        const split = params.valueFormatted.split('/');
        if (split.length === 6 || split.length === 5) {
          const docVer = split.length === 6 ? split[5] : null;
          const docRef = split[4];
          const docModule = split[3];
          this.documentService.getDataAccessiable(docModule, docRef, docVer).subscribe(() => {
            this.router.navigateByUrl(params.valueFormatted);
          });
        } else {
          this.router.navigateByUrl(params.valueFormatted);
        }
        break;
      }
      case 'imageLink': {
        const { refNo, docRef, version } = params.data;
        this.routeToDocument(refNo || docRef, version).subscribe();
        break;
      }
      case 'navigateToEditDocument': {
        const { refNo, docRef, version } = params.data;
        this.documentService
          .lockDoc(refNo || docRef, this.moduleId)
          .pipe(
            catchError(({ error }) => {
              this.notificationService.open({ type: 'warn', message: error.message });
              return of('skipNavigate');
            }),
            concatMap((value) =>
              value === 'skipNavigate'
                ? of(value)
                : this.routeToDocument(refNo || docRef, version, { queryParams: { enterEditMode: true } }),
            ),
            catchError(() => this.documentService.unlockDoc(refNo || docRef, this.moduleId)),
            untilDestroyed(this),
          )
          .subscribe();
        break;
      }
      default:
    }
  }

  handleCellActionBus({ action, params }: CellActionBusEvent) {
    this.recordCellActionService.dispatchAction(action, params);
  }

  toTopButtonShow(visible: boolean) {
    this.chatSettingService.setBubbleOffsetY(visible ? -52 : 0);
  }

  columnChange(columnDefs: ColDef[]) {
    const viewColumns = columnDefs.map(
      (column) =>
        ({
          ...column.cellRendererParams.col,
          size: String(column.width),
          visible: !column.hide,
          frozenType: column.pinned,
        } as ViewColumn),
    );

    const newView$ = this.viewName$.pipe(
      take(1),
      switchMap((viewName) => this.listingService.setColumns(viewName, viewColumns)),
    );

    this.handleUpdateColumns(newView$);
  }

  private handleTitle() {
    combineLatest([this.naviView2$, this.naviModuleLabel$])
      .pipe(distinctUntilChanged(), untilDestroyed(this))
      .subscribe(([naviView, naviModuleLabel]) => {
        this.titleService.setTitle(`${naviModuleLabel} - ${naviView?.label} - CBX Cloud`);
      });
  }

  private handleFetchView() {
    this.viewName$
      .pipe(
        distinctUntilChanged(),
        switchMap((viewName) => this.listingService.getListingView$(viewName)),
        untilDestroyed(this),
      )
      .subscribe((view) => {
        this.viewSubject.next(view);
        this.gridApi.resetColumnState();
        if (this.gridApi.isToolPanelShowing()) {
          this.gridApi.closeToolPanel();
        }
      });
  }

  private queryStringResponsibleByMe(moduleId: any): Observable<void> {
    return this.recordService.getRecordParty(moduleId, this.viewName).pipe(
      map((result) => {
        this.totalPartyMe = result.totalSize;
      }),
    );
  }

  private handleFetchRecord() {
    combineLatest([
      this.loadRecord$,
      this.queryString$,
      this.moduleId$,
      this.domainAttributeService.crossModuleAlertSetting$,
    ])
      .pipe(
        auditTime(0),
        filter(([, moduleId]) => !!moduleId),
        untilDestroyed(this),
      )
      .subscribe(([, { viewName, condition }, moduleId, obtainAlert]) => {
        this.recordService.fetchRecord(moduleId, viewName, condition, null, obtainAlert);
        this.notificationCheckService.loadNotificationStatus();
      });

    this.resetRecord$.subscribe();
    this.notificationCheckService.loadNotificationStatus();
  }

  private handleFilterRoute() {
    this.filterBus$
      .pipe(withLatestFrom(this.filterMap$), auditTime(0), untilDestroyed(this))
      .subscribe(([{ fieldId, filters }, filterMap]) => {
        let cbxql = '';
        const result = { ...filterMap, [fieldId]: filters };
        Object.entries(result).forEach(([resultId, resultFilters]) => {
          resultFilters.forEach(({ condition, joinOperator }) => {
            if (condition.includes('IS BLANK') || condition.includes('IS NOT BLANK')) {
              cbxql += `${cbxql ? joinOperator ?? CONDITION_SYMBOL.and : ''}${resultId} ${condition}`;
            } else {
              cbxql += `${cbxql ? joinOperator ?? CONDITION_SYMBOL.and : ''}${resultId}${condition}`;
            }
          });
        });

        this.routeByQuery({ cbxql: cbxql === '' ? null : cbxql });
        this.cpmEndToEndql = cbxql === '' ? null : decodeURIComponent(cbxql);
      });
  }

  private handleUpdateColumns(newView$: Observable<View>) {
    newView$
      .pipe(
        switchMap((newView) => combineLatest([of(newView), this.view$]).pipe(take(1))),
        map(([newView, view]) => ({ ...view, id: newView.id, columns: newView.columns, option: newView.option })),
        untilDestroyed(this),
      )
      .subscribe((view: View) => this.viewSubject.next(view));
  }

  private openMessageDialogAndConfirm(openDialogMeta: OpenDialogMeta, params?: string[]) {
    if (!openDialogMeta) {
      return of(true);
    }

    const { type, buttonType, message } = openDialogMeta;

    if (!params) {
      return this.openMessageDialogWithoutParam(buttonType, message, type).closed.pipe(
        filter((result) => buttonType === 'Okay' || result?.payload),
        map(() => true),
        untilDestroyed(this),
      );
    }

    return this.openMessageDialog(buttonType, message, type, params).closed.pipe(
      filter((result) => buttonType === 'Okay' || result?.payload),
      map(() => true),
      untilDestroyed(this),
    );
  }

  private openMessageDialogWithoutParam(messageButtons: DialogButtonTypes, message: string, title: AlertDialogTypes) {
    return this.dialog.open<any>(AlertDialogComponent, {
      ...generalAlertDialogConfig,
      data: {
        messageButtons,
        message,
        title,
      },
    });
  }

  private openMessageDialog(
    messageButtons: DialogButtonTypes,
    message: string,
    title: AlertDialogTypes,
    params?: string[],
  ) {
    return this.dialog.open<any>(AlertDialogComponent, {
      ...generalAlertDialogConfig,
      data: {
        messageButtons,
        message: resolveFormat(message, null, params),
        title,
      },
    });
  }

  private checkValidationForRecords(
    menu: ViewActionDto,
    selections: Record[],
    actionId: string,
    warningValidationDialogMeta: FormDefine_OpenDialogMeta,
    confirmDialogMeta: FormDefine_OpenDialogMeta,
  ) {
    const refNoList = selections.map((record) => record.refNo || record.docRef).join(',');
    this.recordService.checkValidation(this.moduleId, actionId, refNoList).subscribe((isFailed) => {
      if (isFailed) {
        this.openMessageDialogAndConfirm(warningValidationDialogMeta);
      } else {
        this.openMessageDialogAndConfirm(confirmDialogMeta)
          .pipe(filter((confirmed) => confirmed))
          .subscribe(() => {
            this.handleAction(menu, selections);
          });
      }
    });
  }

  private checkVpoListAllIsLatest(
    menu: ViewActionDto,
    warningNotLatestDialogMeta: FormDefine_OpenDialogMeta,
    selections: Record[],
  ) {
    const selectedRecords = [];
    this.selections.forEach((selection) => {
      selectedRecords.push(`${selection?.businessRefNo}:${selection?.version}`);
    });
    this.apiService.get<boolean>(CBX_URL.vpoListAllIsLatest(selectedRecords)).subscribe((allLatest) => {
      if (!allLatest) {
        this.openMessageDialogAndConfirm(warningNotLatestDialogMeta);
      } else {
        this.handleAction(menu, selections);
      }
    });
  }

  private handleAction(menu: ViewActionDto, selections: Record[] = []) {
    const { action } = menu;

    const hasExecutedAction = this.recordActionService.dispatchAction(menu, {
      tabId: this.tabId,
      moduleId: this.moduleId,
      viewName: this.viewName,
      selections,
      filterByCpmAssignToMe: this.filterByCpmAssignToMe,
      filterByCpmInComplete: this.filterByCpmInComplete,
      download$: this.download$,
      statusChange$: this.statusChange$,
      urlParams$: this.urlParams$,
      recordCount: this.recordCount,
      columns: this.columns,
    });
    if (hasExecutedAction) {
      return;
    }

    switch (action) {
      case 'searchNewDocByPdf': {
        this.searchNewDocByPdfAction(menu);
        break;
      }

      case 'SelectAll': {
        this.selectAllAction(menu);
        break;
      }

      case 'CreateWithDefaultData': {
        this.createWithDefaultDataAction(menu, selections);
        break;
      }
      case 'CreateWithDefaultDataAndCheckIsLatest': {
        this.createWithDefaultDataAction(menu, selections);
        break;
      }

      case 'OpenListingView': {
        this.openListingViewAction(menu, selections);
        break;
      }

      case 'vpoBatchNewPackingList': {
        this.vpoBatchNewPackingList(menu, selections);
        break;
      }

      case 'BatchSubmission': {
        this.batchSubmission(menu, selections);
        break;
      }

      case 'UpdateInboxMessageStatus': {
        this.updateInboxMessageStatus(menu, selections);
        break;
      }

      case 'adminModuleUpload': {
        this.adminModuleUploadAction(menu);
        break;
      }

      case 'viewAdminModuleUpload': {
        this.adminModuleUploadAction(menu);
        break;
      }

      case 'validationConfigImport': {
        this.adminModuleUploadAction(menu);
        break;
      }

      case 'entityConfigImport': {
        this.adminModuleUploadAction(menu);
        break;
      }

      case 'adminModuleDownload': {
        this.adminModuleDownload(menu, selections);
        break;
      }

      case 'viewAdminModuleDownload': {
        this.adminModuleDownload(menu, selections);
        break;
      }

      case 'validationConfigDownload': {
        this.adminModuleDownload(menu, selections);
        break;
      }

      case 'BatchUpdateVq': {
        const refNos = [];
        if (this.moduleId === 'vq') {
          if (this.selections.length > 100) {
            const { warningMoreThan100RecordsDialogMeta } = menu;

            this.openMessageDialogAndConfirm(warningMoreThan100RecordsDialogMeta);

            break;
          }
          this.selections.forEach((selection) => {
            refNos.push(selection.refNo ? selection.refNo : selection.docRef);
          });
          const { location } = this.window;
          if (!location) {
            // eslint-disable-next-line no-alert
            alert('the URL is invalid');
            return;
          }
          const baseUrl = location.href.substring(0, location.href.lastIndexOf('listing'));
          const fullUrl = `${baseUrl}document/sourcing/vqce?refNos=${refNos.join(',')}`;
          this.window.open(fullUrl, '_blank');
        }
        break;
      }

      case 'openLookupPopupWin': {
        this.openLookupPopupWinAction(menu);
        break;
      }

      case 'OpenImportPopupWin': {
        this.openImportPopupWinAction(menu);
        break;
      }

      case 'importRawData': {
        this.importRawDataAction();
        break;
      }

      case 'entityConfigDownload': {
        this.adminModuleDownload(menu, selections);
        break;
      }
      case 'BatchAddAgreement': {
        this.batchAddAgreementAction(menu, selections);
        break;
      }
      case 'uploadEntityIdFormat': {
        this.uploadEntityIdFormatAction(menu);
        break;
      }

      case 'uploadUserFormat': {
        this.uploadUserFormatAction(menu);
        break;
      }

      case 'configDownload': {
        this.configDownload();
        break;
      }

      case 'configUpload': {
        this.configUpload();
        break;
      }

      case 'clearSystemRedisCache': {
        this.clearSystemRedisCache();
        break;
      }

      default: {
        break;
      }
    }
  }

  editView(isEditView: boolean) {
    return this.columnDefs$
      .pipe(
        first(),
        map((columnDefs) => this.handleColumns(columnDefs)),
        map((columnDefs) => {
          this.dialog
            .open<any, CreateOrEditViewDialogData>(CreateOrEditViewDialogComponent, {
              ...defaultDialogConfig,
              height: '600px',
              width: '960px',
              data: {
                moduleName: this.naviModuleLabel$,
                columnDefs,
                viewName: this.viewName,
                moduleId: this.moduleId,
                searchCriteria: this.searchCondition$,
                isEditView,
                viewType: this.viewType,
                shareWithList: this.shareWithList,
                viewId: this.viewId,
                description: isEditView ? this.description : null,
                options: this.optionsMap,
                baseViewName: this.baseViewName,
              },
            })
            .closed.pipe(filter((result) => result?.type === 'done'))
            .subscribe((payload) => {
              this.listingService.getView$(payload?.viewName).subscribe((view) => this.viewSubject.next(view));
              this.router.navigate(['../', payload?.viewName], {
                relativeTo: this.route,
                queryParams: {
                  cbxql: payload?.cbxql,
                  q: payload?.q,
                  dateRange: payload?.dateRange,
                },
              });
            });
        }),
        untilDestroyed(this),
      )
      .subscribe();
  }

  private batchAddAgreementAction(menu: Action, selections: Record[] = []) {
    const { module } = menu.actionParams;
    if (module === 'vendor') {
      this.batchAddAgreement4Vendor(menu, selections);
    } else {
      this.batchAddAgreement(menu, selections);
    }
  }

  private batchAddAgreement4Vendor(menu: Action, selections: Record[] = []) {
    const format = '{refNo}';
    this.selectTemplateFromPopupDialog(menu, 'agreementTemplates')
      .pipe(
        switchMap(({ payload }) => {
          const selectedRecordDetails = [];
          // eslint-disable-next-line no-restricted-syntax
          for (const selection of selections) {
            // eslint-disable-next-line no-restricted-syntax
            for (const source of payload) {
              const targetRefNos = selection.refNo;
              const sourceIds = source?.agreementTemplateItemId;
              const code = source?.code;
              const type = source?.type?.name;
              const clonedSelection = {
                ...selection,
                code,
                type,
              };
              const lastFormat = this.formatByDataAbstract || format;
              const formatValue =
                resolveFormat(lastFormat, clonedSelection) || resolveFormat('{docRef}', clonedSelection);
              selectedRecordDetails.push({
                targetRefNos,
                sourceIds,
                vendorAgreementCode: code,
                vendorAgreementName: type,
                formatValue,
              });
            }
          }
          return new Promise((resolve) => {
            this.openBatchAddProgressBar(selectedRecordDetails);
            resolve(null);
          }).then(() => of(null));
        }),
      )
      .subscribe();
  }

  private batchAddAgreement(menu: Action, selections: Record[] = []) {
    const { successMessage, failedMessage, action, sourceEntityName } = menu.actionParams;
    const targetEntityName = menu.actionParams.entityName;
    const targetRefNos = selections.map((selection) => selection?.refNo).join(',');
    this.selectTemplateFromPopupDialog(menu, 'agreementTemplates')
      .pipe(
        switchMap(({ payload }) => {
          const sourceIds = payload.map((source) => source?.agreementTemplateItemId).join(',');
          return this.menuBarService.apiBatchAddAction({
            action,
            targetEntityName,
            targetRefNos,
            sourceEntityName,
            sourceIds,
          });
        }),
      )
      .subscribe({
        next: () => {
          this.statusChange$.next();
          this.notificationService.open({
            message: resolveFormat(successMessage, null),
            position: 'right',
          });
          this.download$.next(false);
        },
        error: () => {
          this.errorHandler(resolveFormat(failedMessage, null));
          this.download$.next(false);
        },
      });
  }

  private getFormatbyDataAbstract(): Observable<string | null> {
    return this.getDataAbstract$('vendor', 'entity.vendorAgreements').pipe(
      take(1),
      map((item) => {
        if (item?.fieldIdsValue) {
          const values = item.fieldIdsValue.split(',').filter((value) => value.trim() !== '');
          return values.map((value) => `{${value.trim()}}`).join(' - ');
        }
        return null;
      }),
    );
  }

  private getDataAbstract$(module: string, level: string): Observable<any> {
    const url = CBX_URL.getDataAbstractByModuleAndLevel(module, level);
    return this.apiService.get<any>(url);
  }

  private openBatchAddProgressBar(batchUpdateProgressBarData: BatchUpdateProgressBarData[]) {
    const warmMessage =
      'Batch update in progress. Please retry another batch later as the current processing is underway.';
    if (this.localStorageService.get('isBatchAddProgressBarActive')) {
      this.dialog.open(AlertDialogComponent, {
        ...generalAlertDialogConfig,
        data: {
          messageButtons: 'Okay',
          message: warmMessage,
          title: 'Warning',
        },
      });
    } else {
      const config: PopoverParams<BatchUpdateProgressBarParam> = {
        disableEscClose: true,
        content: BatchAddAgreementProgressBarComponent,
        width: '700px',
        height: '400px',
        maxHeight: '100%',
        maxWidth: '100%',
        hasBackdrop: false,
        data: {
          batchUpdateProgressBarData,
          statusChange$: this.statusChange$,
        },
        positionType: 'global',
        positionParams: {
          position: {
            right: '10px',
            bottom: '10px',
          },
        },
      };
      this.popper.open(config);
    }
  }

  private openLookupPopupWinAction({ actionParams: { entityName, message, title }, actionId }: ViewActionDto) {
    this.dialog
      .open<LookupTypeDialogComponent, LookupTypeDialogData>(LookupTypeDialogComponent, {
        ...defaultDialogConfig,
        height: 'auto',
        width: '392px',
        data: {
          entityName,
          dataListTypeName: '',
          message,
          title,
        },
      })
      .closed.pipe(
        filter((result) => result?.type === 'done'),
        untilDestroyed(this),
      )
      .subscribe((payload) => {
        this.download$.next(true);

        if (payload?.payload) {
          this.router.navigate([`/document/${this.tabId}/${this.moduleId}/create`], {
            queryParams: { actionId, selectTypeId: payload?.payload?.id },
          });
        }
      });
  }

  private openImportPopupWinAction({ actionParams: { entityName, moduleId, message, title } }: ViewActionDto) {
    this.dialog
      .open<ImportDialogComponent, ImportDialogData>(ImportDialogComponent, {
        ...defaultDialogConfig,
        height: '285px',
        width: '720px',
        data: {
          entityName,
          moduleId,
          message,
          title,
        },
      })
      .closed.pipe(
        filter((result) => result?.type === 'done'),
        untilDestroyed(this),
      )
      .subscribe((payload) => {
        this.download$.next(true);

        if (payload?.payload) {
          this.menuBarService.updateDoc(payload.payload, 'import', 'save', null).subscribe((savedData) => {
            if (savedData?.refNo) {
              this.router.navigate([`/document/${this.tabId}/${this.moduleId}/${savedData.refNo}`]);
            }
          });
        }
      });
  }

  private importRawDataAction() {
    const fileInput: any = this.fileInput$.getValue();
    if (fileInput) {
      fileInput.value = '';
      fileInput.click();
    }
  }

  private selectTemplateFromPopupDialog(menu: Action, templateId: string) {
    const { viewId } = menu.actionParams;
    const { title } = menu.actionParams;
    const { module } = menu.actionParams;
    return this.documentService.getTemplates(module, templateId, null).pipe(
      switchMap(
        (templateList: Template[]) =>
          this.dialog.open<TemplateSelectDialogResult, TemplateSelectDialogData, TemplateSelectDialogComponent>(
            TemplateSelectDialogComponent,
            {
              ...defaultDialogConfig,
              maxWidth: '960px',
              width: '80vw',
              data: {
                rowSelection: 'multiple',
                templateList,
                title,
                dataKeys: ['hashKey'],
                viewId,
              } as TemplateSelectDialogData,
            },
          ).closed,
      ),
      take(1),
      filter((result) => result?.type === 'done'),
      filter((result) => !!result?.payload?.length),
    );
  }

  adminModuleUploadAction(menu: ViewActionDto) {
    const input = this.renderer.createElement('input');
    this.renderer.appendChild(this.elementRef.nativeElement, input);
    this.renderer.setStyle(input, 'display', 'none');
    this.renderer.setAttribute(input, 'type', 'file');
    this.renderer.listen(input, 'change', (event: any) => {
      this.uploadXmlFile(menu, event.target.files);
    });
    input.click();
    this.renderer.removeChild(this.elementRef.nativeElement, input);
  }
  uploadXmlFile(menu: ViewActionDto, filesList: ArrayLike<File>) {
    const files = Array.from(filesList);
    files.forEach((file) => {
      if (
        getFileExtension(file.name) === 'json' ||
        getFileExtension(file.name) === 'xml' ||
        getFileExtension(file.name) === 'zip'
      ) {
        this.fileService
          .uploadXml$(file, this.moduleId)
          .pipe(debounceTime(1000), take(1), untilDestroyed(this))
          .subscribe(() => {
            const { warningUploadProcessingDialogMeta } = menu;
            if (isEmptyOrNil(warningUploadProcessingDialogMeta)) {
              this.openMessageDialog(
                'Okay',
                'System is processing the upload and will provide the result shortly in your inbox.',
                'information',
              )
                .closed.pipe(take(1), untilDestroyed(this))
                .subscribe(() => {
                  if (this.moduleId === 'workflow') {
                    this.statusChange$.next();
                  }
                });
            } else {
              const { buttonType, message, type } = warningUploadProcessingDialogMeta;
              this.openMessageDialog(buttonType, message, type);
            }
          });
      } else {
        const { warningOnlyXmlAndZipDialogMeta } = menu;
        if (isEmptyOrNil(warningOnlyXmlAndZipDialogMeta)) {
          this.openMessageDialog('Okay', 'Please upload only JSON, XML or ZIP file format.', 'information');
        } else {
          const { buttonType, message, type } = warningOnlyXmlAndZipDialogMeta;
          this.openMessageDialog(buttonType, message, type);
        }
      }
    });
  }

  private configUpload() {}

  private configDownload() {
    this.openMessageDialog(
      'OkayCancel',
      'System is processing the configure XML and will provide the result shortly in your inbox.',
      'information',
    )
      .closed.pipe(
        take(1),
        tap((result) => {
          if (result?.type === 'done') {
            this.download$.next(true);
            const params = new HttpParams();
            this.apiService
              .postWithBinaryResponse(CBX_URL.downloadAllAdminModuleConfig(), null, {
                observe: 'response',
                responseType: 'blob',
                params,
              })
              .subscribe({
                next: () => {
                  this.download$.next(false);
                },
                error: () => {
                  this.notificationService.open({
                    message: 'Download Configuration Failed',
                    type: 'warn',
                    position: 'right',
                  });
                  this.download$.next(false);
                },
              });
          }
        }),
      )
      .subscribe();
  }

  private clearSystemRedisCache() {
    this.openMessageDialog('OkayCancel', 'Are you sure to clear all the system Memory caches?', 'information')
      .closed.pipe(
        take(1),
        tap((result) => {
          if (result?.type === 'done') {
            this.apiService.delete(CBX_URL.clearSystemRedisCache()).subscribe();
          }
        }),
      )
      .subscribe();
  }

  private adminModuleDownload(menu: ViewActionDto, selections: Record[]) {
    const viewModule = !menu?.actionParams?.moduleId ? this.moduleId : menu?.actionParams?.moduleId;

    const refNos: string[] = [];
    const names: string[] = [];
    const nameMapping: { [key: string]: string } = {
      notificationProfile: 'profileName',
      workflow: 'applyTo',
      printFormExportTemplate: 'applyTo',
      user: 'userId',
      systemFile: 'systemFileId',
      hclType: 'type',
      defaultProfile: 'refEntityName',
      testMethod: 'testNo',
      validationProfile: 'profileName',
      materialAttributeTemplate: 'templateName',
      formTemplate: 'refNo',
    };
    if (!selections?.length) {
      this.openMessageDialog('Okay', 'Please select at least one record.', 'information');
    } else if (viewModule === 'user') {
      this.openMessageDialog(
        'OkayCancel',
        "You are going to download user profiles which will include their passwords' hash in the export. Please keep the export file safe to avoid any password disclosure. Are you sure to continue?",
        'warning',
      )
        .closed.pipe(
          tap((result) => {
            if (result?.type === 'done') {
              this.download$.next(true);
              selections.forEach((selection) => {
                refNos.push(selection.refNo);
                let fieldName = nameMapping[viewModule] || 'name';
                if (selection[fieldName] === undefined) {
                  fieldName = 'refNo';
                }
                names.push(selection[fieldName]);
              });
              const params = new HttpParams();
              this.apiService
                .postWithBinaryResponse(CBX_URL.downloadAdminModuleConfiguration(viewModule, refNos), names, {
                  observe: 'response',
                  responseType: 'blob',
                  params,
                })
                .subscribe({
                  next: (httpResponse) => {
                    this.notificationService.open({ message: 'Download Configuration Succeeded', position: 'right' });
                    this.fileService.downloadFile(httpResponse, this.elementRef);
                    this.download$.next(false);
                  },
                  error: () => {
                    this.notificationService.open({
                      message: 'Download Configuration Failed',
                      type: 'warn',
                      position: 'right',
                    });
                    this.download$.next(false);
                  },
                });
            }
          }),
        )
        .subscribe();
    } else {
      if (viewModule === 'viewAdministration') {
        let isContainSystemView = false;
        selections.forEach((sel) => {
          const { viewType } = sel;
          if (viewType !== 'Customized') {
            isContainSystemView = true;
          }
        });
        if (isContainSystemView) {
          this.openMessageDialog('Okay', 'The selected views contain system view.', 'information');
          return;
        }
      }
      this.download$.next(true);
      selections.forEach((selection) => {
        if (viewModule === 'gridColsVisibleOrder') {
          refNos.push(selection.id);
        } else {
          refNos.push(!selection.refNo ? selection.docRef : selection.refNo);
        }
        let fieldName = nameMapping[viewModule] || 'name';
        if (selection[fieldName] === undefined) {
          fieldName = 'refNo';
        }
        names.push(selection[fieldName]);
      });
      const params = new HttpParams();
      this.apiService
        .postWithBinaryResponse(CBX_URL.downloadAdminModuleConfiguration(viewModule, refNos), names, {
          observe: 'response',
          responseType: 'blob',
          params,
        })
        .subscribe({
          next: (httpResponse) => {
            this.notificationService.open({ message: 'Download Configuration Succeeded', position: 'right' });
            this.fileService.downloadFile(httpResponse, this.elementRef);
            this.download$.next(false);
          },
          error: () => {
            this.notificationService.open({
              message: 'Download Configuration Failed',
              type: 'warn',
              position: 'right',
            });
            this.download$.next(false);
          },
        });
    }
  }
  private selectAllAction({ actionId }: Action) {
    if (!actionId) {
      console.error(`'actionId' is required in SelectAll action.`);
      return;
    }

    this.selectAll = true;
    const myMap = new Map();
    this.loadCount = 1;
    this.loadAll(myMap);
  }

  gridReady({ api }: GridReadyEvent) {
    this.gridApi = api;
    this.handleFetchView();
  }

  openExportExcelDialog() {
    this.loading$
      .pipe(
        take(1),
        switchMap((loading) => {
          if (loading) {
            this.notificationService.open({ message: 'Loading Records.', position: 'right' });
            return of(null);
          }
          return this.dialog.open<ExportExcelDialogResult, ExportExcelDialogData, ExportExcelDialogComponent>(
            ExportExcelDialogComponent,
            {
              hasBackdrop: true,
              data: {
                setting: {
                  fileName: `${this.moduleId}-${this.viewName}`,
                },
                option: { disabledOnlySelected: !this.selections?.length },
              },
            },
          ).closed;
        }),
        filter((result) => result?.type === 'done'),
      )
      .subscribe((info) => {
        // if has select any record, then no need to refresh all data to page
        if (info?.payload?.onlySelected) {
          // this.recordService.setStateRecordsEmpty();
          this.exportInfo = info;
          this.exportReady$.next(info);
        } else {
          this.loadCount = 0;
          this.loadAll(info);
        }
      });
  }

  private searchNewDocByPdfAction({ actionParams: { failedMessage } }: ViewActionDto) {
    const input = this.renderer.createElement('input');
    this.renderer.appendChild(this.elementRef.nativeElement, input);
    this.renderer.setStyle(input, 'display', 'none');
    this.renderer.setAttribute(input, 'type', 'file');
    this.renderer.listen(input, 'change', (event: any) => {
      this.uploadFileStart(event.target.files, failedMessage);
    });
    input.click();

    this.renderer.removeChild(this.elementRef.nativeElement, input);
  }

  private getFormatValueForHyperlink(params: ValueFormatterParams) {
    const colId = params.colDef.cellRendererParams.col.fieldId;
    const { type } = params.colDef.cellRendererParams.col;
    const { actionParams } = params.colDef.cellRendererParams.col;

    if (!actionParams) {
      return '';
    }

    const { naviModule, moduleIdKey, moduleId: module, fieldId, refNo, version, refNoFields, modules } = actionParams;
    const refNoFieldsArray: string[] = isEmptyOrNil(refNoFields) ? ''.split('') : refNoFields.split(',');
    const modulesArray: string[] = isEmptyOrNil(modules) ? ''.split('') : modules.split(',');

    if (type === 'HyperlinkMultiple') {
      return `/document/${naviModule || this.tabId}/${module}/`;
    }

    if (isEmptyOrNil(params.data)) {
      return '';
    }

    let refNoFromArr = '';
    let moduleFromArr = '';
    if (refNoFieldsArray.length === modulesArray.length) {
      for (let i = 0; i < refNoFieldsArray.length; i += 1) {
        if (refNoFieldsArray[i].length > 0 && params.data[refNoFieldsArray[i]]) {
          refNoFromArr = params.data[refNoFieldsArray[i]];
          moduleFromArr = modulesArray[i];
        }
      }
    }

    const docRef = refNoFromArr.length > 0 ? refNoFromArr : params.data[refNo || fieldId];
    const docModule = moduleFromArr.length > 0 ? moduleFromArr : moduleIdKey ? params.data[moduleIdKey] : module;
    const docVer = params.data[version];

    if (isEmptyOrNil(docRef)) {
      if (type !== 'HyperlinkOrNew') {
        console.warn(`refNo or fieldId is required in Hyperlink field (${colId})`);
      }

      return '';
    }

    if (type === 'HyperlinkBySupplier') {
      const moduleBySupplier = params.data[this.supplierTypeField] === 'Vendor' ? 'vendor' : 'fact';
      if (isEmptyOrNil(docVer)) {
        return `/document/${naviModule || this.tabId}/${moduleBySupplier}/${docRef}`;
      }
      return `/document/${naviModule || this.tabId}/${moduleBySupplier}/${docRef}/${docVer}`;
    }

    if (isEmptyOrNil(docVer) || this.enabledConcurrent) {
      return `/document/${naviModule || this.tabId}/${docModule}/${docRef}`;
    }

    return `/document/${naviModule || this.tabId}/${docModule}/${docRef}/${docVer}`;
  }

  private getFormatValueForNumber(params: ValueFormatterParams, format: string) {
    const locale = this.domainAttributeService.state[DOMAIN_ATTRIBUTES.UI_LOCALE] || 'en-US';
    const isEmpty = isEmptyOrNil(params.value);
    return isEmpty ? '' : formatNumber(params.value, locale, format);
  }

  uploadFileStart(filesList: ArrayLike<File>, message: string) {
    const files = Array.from(filesList);
    files?.map((file) => ({
      id: 'id',
      url: 'url',
      fileName: file.name,
      needUpload: true,
      fileToUpload: file,
      fileExtension: getFileExtension(file.name),
    }));

    files.forEach((file) => {
      if (file.type.indexOf('pdf') !== -1) {
        this.upload(file);
      } else {
        this.notificationService.open({
          message,
          type: 'warn',
          duration: 6000,
        });
      }
    });
  }

  upload(file: File) {
    const fileUpload = this.fileService.Textract$(file);
    fileUpload.subscribe({
      next: (event: HttpEvent<any>) => {
        switch (event.type) {
          case HttpEventType.Sent:
            break;
          case HttpEventType.Response:
            if (event.status >= 200 && event.status < 300) {
              let message = '';
              if (event.status !== 200) {
                message = this.translocoService.translate('listing.inspectionReportUpdatedFailed', {
                  $1: `${event.body.refNo}`,
                });
              } else {
                message = this.translocoService.translate('listing.inspectionReportUpdatedSuccess', {
                  $1: `${event.body.refNo}`,
                });
              }
              this.notificationService.open({ message, type: event.status === 200 ? 'primary' : 'warn' });
              break;
            } else {
              const message = this.translocoService.translate('x1uploaderrorstatuscodex2', {
                $1: `${file.name}`,
                $2: `${event.status}`,
              });
              this.notificationService.open({ message, type: 'warn' });
            }
            break;
          default:
        }
      },
      error: (error) => {
        let message = this.translocoService.translate('x1uploaderrormessagex2', {
          $1: `${file.name}`,
          $2: `${error.message}`,
        });
        const aa = error as HttpErrorResponse;
        if (aa.error && aa.error.message) {
          message = `${aa.error.message}`;
        }
        this.notificationService.open({ message, type: 'warn' });
      },
    });
  }

  changeCpmTracker(checked: boolean) {
    const isCpmMode = checked;
    const condition: SearchCondition = isCpmMode
      ? { isCpmMode: String(checked), filterByCpmAssignToMe: null, filterByCpmInComplete: null }
      : { isCpmMode: null, filterByCpmAssignToMe: null, filterByCpmInComplete: null };
    if (isCpmMode) {
      this.gridApi.resetColumnState();
      this.gridApi.resetColumnGroupState();
      if (this.gridApi.isToolPanelShowing()) {
        this.gridApi.closeToolPanel();
      }
    }
    this.routeByQuery(condition);
  }

  changeAssignToMe(assignToMe: boolean) {
    this.routeByQuery({ filterByCpmAssignToMe: assignToMe || null });
  }

  changeInComplete(inComplete: boolean) {
    this.routeByQuery({ filterByCpmInComplete: inComplete || null });
  }

  getNewCpmDoc(cpmDocs: CpmDocTracker[]) {
    const cpmIds: string[] = cpmDocs.map((cpmDoc) => cpmDoc?.cpmId).filter(isNotEmptyOrNil);

    return isEmptyOrNil(cpmIds)
      ? of([])
      : this.cpmService
          .loadCpmDocTrackerInfo(cpmIds, this.filterByCpmInComplete, this.filterByCpmAssignToMe)
          .pipe(map((newCpmDocs) => this.filterViewCpmRecords(cpmDocs, newCpmDocs)));
  }

  getCpmEndToEndViewReferenceRowData(moduleId: string, viewName: string, records: Record[]) {
    const docRefNos = [];
    const docIds = [];
    const cbxql = this.cpmEndToEndql;
    records.forEach((record) => {
      let { refNo, docId } = record;
      const { businessRefNo, _lineItemId } = record;
      if (isEmptyOrNil(refNo)) {
        refNo = businessRefNo;
      }
      if (isEmptyOrNil(docId)) {
        docId = _lineItemId;
      }
      if (isNotEmptyOrNil(refNo) && docRefNos.findIndex((docRefNo) => docRefNo === refNo) < 0) {
        docRefNos.push(refNo);
      }
      if (isNotEmptyOrNil(docId)) {
        docIds.push(docId);
      }
    });
    return isEmptyOrNil(docRefNos)
      ? of([])
      : this.cpmService.loadCpmEndToEndViewReferenceData(moduleId, viewName, { docIds, docRefNos, cbxql }).pipe(
          map((newRecords) => {
            if (records.length === newRecords.length) {
              return newRecords;
            }
            const correctRecords = [];
            records.forEach((record) => {
              const newRecord2 = newRecords.find((newRecord) => newRecord.refNo === record.refNo);
              correctRecords.push(isEmptyOrNil(newRecord2) ? { refNo: null } : newRecord2);
            });
            return correctRecords;
          }),
        );
  }

  filterViewCpmRecords(cpmDocs: CpmDocTracker[], newCpmDocs: CpmDocTracker[]) {
    const newCpmRecords = [];
    cpmDocs.forEach((cpmDoc) => {
      const cpmId2 = cpmDoc.cpmId;
      const newCpm = newCpmDocs.find((newCpmDoc) => cpmId2 === newCpmDoc.cpmId);
      if (newCpm) {
        newCpmRecords.push({
          ...cpmDoc,
          upstreamCpmMilestones: newCpm.upstreamCpmMilestones,
          cpmMilestones: newCpm.cpmMilestones ?? [],
        });
      } else {
        newCpmRecords.push({
          ...cpmDoc,
          upstreamCpmMilestones: [],
          cpmMilestones: [],
        });
      }
    });
    return newCpmRecords;
  }

  loadAll(info: any) {
    this.exportInfo = info;
    const cbxConfigureMaxSize = Number(
      this.domainAttributeService.state[DOMAIN_ATTRIBUTES.EXPORT_SEARCH_VIEW_MAX_RECORDS],
    );
    if (cbxConfigureMaxSize > this.recordCount) {
      this.loadTill = Math.ceil(this.recordCount / 1000);
    } else {
      this.loadTill = Math.ceil(cbxConfigureMaxSize / 1000);
    }

    this.toExport = true;
    this.checkForExport();
  }

  checkForExport() {
    if (this.toExport) {
      if (this.loadCount <= this.loadTill) {
        if (this.loadCount === 1) {
          this.recordService.setDownloadRecordState();
        }

        this.loadMore();
      } else {
        // console.log('start export');
        this.toExport = false;
        if (this.selectAll) {
          this.selectAllReady$.next(this.exportInfo);
        } else {
          this.exportReady$.next(this.exportInfo);
        }
      }
    }
  }

  cpmEndToEndDataReadyExport() {
    this.recordService.pagination$
      .pipe(
        tap(() => {
          this.loadCount += 1;
        }),
      )
      .subscribe();

    if (this.toExport) {
      this.checkForExport();
    }
  }

  private handleQueryStringToLocalStorage() {
    combineLatest([this.viewName$, this.route.queryParams])
      .pipe(
        auditTime(0),
        filter(isNotEmptyOrNil),
        map(([viewName, queryParams]) => {
          const listingDateRange = queryParams?.dateRange ?? null;
          const listingQueryParams = {};
          Object.entries(queryParams)
            .filter(([key, value]) => isNotEmptyOrNil(value) && key !== 'dateRange')
            .forEach(([key, value]) => {
              listingQueryParams[key] = value;
            });
          return { listingDateRange, listingQueryParams, viewName };
        }),
      )
      .subscribe(({ listingDateRange, listingQueryParams, viewName }) => {
        const storageQueryString: AnyObject<any> = this.localStorageService.get('listingQueryParams') ?? {};
        this.localStorageService.setObject({
          listingDateRange,
          listingQueryParams: { ...storageQueryString, [viewName]: listingQueryParams },
        });
      });
  }

  private createWithDefaultDataAction(menu: ViewActionDto, selections: Record[] = []) {
    const { actionId, actionParams } = menu;
    const { naviModule, dmrNames, moduleId, title, selectionMode } = actionParams;
    const selectedRecords = [] as Record[];
    if (selections.length > 1) {
      this.dialog
        .open<TableSelectDialogResult, DefaultSelectDialogData, DefaultSelectDialogComponent>(
          DefaultSelectDialogComponent,
          {
            ...defaultDialogConfig,
            maxWidth: '960px',
            width: '80vw',
            data: {
              rowSelection: selectionMode,
              viewId: this.viewName,
              title,
              dataKeys: ['rowIndex'],
              selectedRows: selections,
            },
          },
        )
        .closed.pipe(
          filter((result) => result?.type === 'done'),
          map((result) => result.payload),
        )
        .subscribe((defaultRows) => {
          if (defaultRows) {
            selections.forEach((selection) => {
              if (defaultRows.find((defaultRow) => defaultRow?.rowIndex === selection?.rowIndex)) {
                selectedRecords.push({ ...selection, isDefaultSelected: true });
              } else {
                selectedRecords.push(selection);
              }
            });
            const lastRecord = selectedRecords.pop();
            selectedRecords.push({ ...lastRecord, isLastRecord: true });
            const newSelectedRecords = selectedRecords.map((record) => ({
              ...record,
              selectedRecordSize: selectedRecords.length,
            }));
            return this.router.navigate([`/document/${naviModule}/${moduleId}/create/withData`], {
              queryParams: {
                from: 'listing',
                dmrNames,
                actionId,
              },
              state: {
                records: newSelectedRecords,
              },
            });
          }
          return '';
        });
    } else {
      selections.forEach((selection) => {
        selectedRecords.push({ ...selection, isDefaultSelected: true });
      });
      const lastRecord = selectedRecords.pop();
      selectedRecords.push({ ...lastRecord, isLastRecord: true });
      const newSelectedRecords = selectedRecords.map((record) => ({
        ...record,
        selectedRecordSize: selectedRecords.length,
      }));
      return this.router.navigate([`/document/${naviModule}/${moduleId}/create/withData`], {
        queryParams: {
          from: 'listing',
          dmrNames,
          actionId,
        },
        state: {
          records: newSelectedRecords,
        },
      });
    }
    return null;
  }

  private openListingViewAction(menu: ViewActionDto, selections: Record[] = []) {
    const { actionParams, warningCanSelectOnlyOneRecordMeta } = menu;
    if (!this.selections?.length || this.selections?.length > 1) {
      const { buttonType, message, type } = warningCanSelectOnlyOneRecordMeta;
      this.openMessageDialog(buttonType, message, type);
      return;
    }
    const { naviId, moduleId, viewName, field, columnId, isLike, isOpenNewWin } = actionParams;
    const record = selections[0];
    const columnValue = encodeURIComponent(record[field]);
    let url = `/listing/${naviId}/${moduleId}/${viewName}?cbxql=${columnId}=${columnValue}&urlParams={isPopupBrowser=true}`;
    if (isLike) {
      url = `/listing/${naviId}/${moduleId}/${viewName}?cbxql=${columnId}~${columnValue}&urlParams={isPopupBrowser=true}`;
    }

    if (isOpenNewWin) {
      this.window.open(url, '_blank', 'height=600, width=1200, top=150, left=200');
    } else {
      this.window.open(url, '_blank');
    }
  }
  private updateInboxMessageStatus(
    { actionId, actionParams: { statusId, failedMessage } }: ViewActionDto,
    selections: Record[] = [],
  ) {
    if (missingValue([actionId, statusId])) {
      console.error(`'actionId' and 'statusId' are required in MarkAs action.`);

      return;
    }

    // const statusLabelText = this.translocoService.translate(genStatusLabelKey(this.moduleId, statusId));

    // const translatedSuccessMessage = this.translocoService.translate('listing.batchMarkAsSuccess', null, this.moduleId);

    // const successMessageText =
    //   translatedSuccessMessage === `${this.moduleId}.listing.batchMarkAsSuccess`
    //     ? `The document is successfully mark as ${statusLabelText}: {$1}`
    //     : `${translatedSuccessMessage} ${statusLabelText}: {$1}`;

    // const translatedFailedMessage = this.translocoService.translate('listing.batchMarkAsFailed', null, this.moduleId);

    // const failedMessageText =
    //   translatedFailedMessage === `${this.moduleId}.listing.batchMarkAsFailed`
    //     ? `Failed to mark the document as ${statusLabelText}: {$1}`
    //     : `${translatedFailedMessage} ${statusLabelText}: {$1}`;

    this.menuBarService
      .markAsInListing(
        this.moduleId,
        statusId,
        selections.map(({ notificationToUserId }) => ({ notificationToUserId })),
      )
      .pipe(
        debounceTime(1000),
        distinctUntilChanged(deepEqual),
        untilDestroyed(this),
        tap(() => {
          this.handleFetchRecord();
        }),
      )
      .subscribe({
        next: (statusResponse) => this.handleStatusResponse(statusResponse, '', ''),
        error: () => this.errorHandler(resolveFormat(failedMessage, null)),
      });
  }
  private handleStatusResponse(statusResponse: DocStatusResponse[], successMessage: string, failedMessage: string) {
    if (this.menuBarService.hasFailedResponse(statusResponse)) {
      const failedRecords = this.menuBarService.getFailedRecords(statusResponse);
      this.openMessageDialog('Okay', failedMessage, 'information', [failedRecords]);
    }

    if (!this.menuBarService.hasSuccessResponse(statusResponse)) {
      return;
    }

    const successRecord = this.menuBarService.getSuccessRecords(statusResponse);

    this.statusChange$.next();
    this.notificationService.open({
      message: resolveFormat(successMessage, null, [successRecord]),
      position: 'right',
    });
  }

  private vpoBatchNewPackingList(menu: Action, selections: Record[] = []) {
    const selectedRecords = selections
      .map((selection) => selection?.refNo ?? selection?.docRef ?? selection?.refDocRefNo)
      .join(',');

    this.dialog
      .open<PackingListPopupResult, PackingListPopupData, ListingViewPackingListPopupComponent>(
        ListingViewPackingListPopupComponent,
        {
          ...defaultDialogConfig,
          maxWidth: '80vw',
          maxHeight: '90vw',
          height: '600px',
          width: '100%',
          data: {
            selectedRows: selectedRecords,
          },
        },
      )
      .closed.pipe(
        filter((result) => result?.type === 'done'),
        map((payload) => payload),
      )
      .subscribe({
        next: (payload) => {
          this.createWithDataAction(menu, [payload]);
        },
        error: () => {
          this.errorHandler(resolveFormat('Failure to Create packingList', null));
        },
      });
  }

  private batchSubmission(menu: Action, selections: Record[] = []) {
    const selectedRecords = selections
      .map((selection) => {
        const lineItemId = selection?._lineItemId;
        const refNo = selection?.refNo ?? selection?.docRef ?? selection?.refDocRefNo;
        if (lineItemId) {
          const firstUnderscoreIndex = lineItemId.indexOf('_');
          const secondUnderscoreIndex = lineItemId.indexOf('_', firstUnderscoreIndex + 1);
          if (secondUnderscoreIndex !== -1) {
            const part2 = lineItemId.substring(secondUnderscoreIndex + 1);
            return `${refNo}_${part2}`;
          }
        }
        return '';
      })
      .filter(Boolean)
      .join(',');

    const selection = selections[0];
    const refNo = selection?.refNo ?? selection?.docRef ?? selection?.refDocRefNo;
    this.documentService.getViewDefine('vpo', refNo, selection?.version).subscribe((payload) => {
      const fieldDefineInFirstVpo = payload.tabGroups
        .find((item) => item.id === 'tabChainOfCustody')
        .sections.find((item) => item.id === 'vpoChainOfCustodyList').subSections[0].fields;
      const readonlyList = [];
      const formShowField = [];
      fieldDefineInFirstVpo.forEach((fd) => {
        if (fd.type === 'ExpansionGroup' && fd.fields) {
          fd.fields.forEach((subFd) => {
            if (subFd.readonly || subFd.isSystemField) {
              readonlyList.push(subFd.id);
            }
            if ((subFd.label != null && subFd.visible) || subFd.id === 'sectionName') {
              formShowField.push(subFd.id);
            }
          });
        } else {
          if (fd.readonly || fd.isSystemField) {
            readonlyList.push(fd.id);
          }
          if ((fd.label != null && fd.visible) || fd.id === 'sectionName') {
            formShowField.push(fd.id);
          }
        }
      });

      this.dialog
        .open<any, any, ListingViewBatchSubmissionCOCPopupComponent>(ListingViewBatchSubmissionCOCPopupComponent, {
          ...defaultDialogConfig,
          maxWidth: '90vw',
          maxHeight: '100vw',
          height: '90%',
          width: '100%',
          data: {
            selectedRows: selectedRecords,
            readonlyList,
            formShowField,
            refNo,
            version: selection?.version,
          },
        })
        .closed.pipe(
          take(1),
          switchMap((data) => {
            if (data.type === 'cancel') return EMPTY;
            this.download$.next(true);
            const { selectedRows } = data;
            return this.batchUpdateProcess(selectedRows);
          }),
        )
        .subscribe({});
    });
  }

  private batchUpdateProcess(selectRows: string[]) {
    const warmMessage =
      'Batch Submission in progress. Please retry another batch later as the current processing is underway.';

    if (this.localStorageService.get('isBatchSubmissionProcessActive')) {
      this.dialog.open(AlertDialogComponent, {
        ...generalAlertDialogConfig,
        data: {
          messageButtons: 'Okay',
          message: warmMessage,
          title: 'Warning',
        },
      });
    } else {
      const positionStrategy: PositionStrategy = this.overlay.position().global().right('10px').bottom('10px');
      const dialogConfig: DialogConfig = {
        ...defaultDialogConfig,
        width: '700px',
        height: '400px',
        maxHeight: '100%',
        maxWidth: '100%',
        positionStrategy,
        data: {
          pendingData: selectRows,
        },
      };
      this.dialog
        .open(BatchUpdateCocDetailProgressBarDialogComponent, dialogConfig)
        .closed.pipe(take(1))
        .subscribe(() => {
          setTimeout(() => {
            this.statusChange$.next();
          }, 1000);
        });
    }

    this.download$.next(false);
    return of(true);
  }

  private createWithDataAction(
    {
      actionId,
      actionParams: { naviModule, moduleId, dmrNames },
    }: Action<{
      naviModule?: string;
      moduleId?: string;
      dmrNames: string[];
    }>,
    selections: Record[] = [],
  ) {
    this.router.navigate([`/document/${naviModule}/${moduleId}/create/withData`], {
      queryParams: {
        from: 'listing',
        dmrNames,
        actionId,
      },
      state: {
        records: selections,
      },
    });
  }

  private errorHandler(message: string) {
    return this.notificationService.open({
      message,
      type: 'warn',
      duration: 6000,
    });
  }

  sumFunction(params) {
    const sumValue = params.values.reduce((valueOne, valueTwo) => Number(valueOne) + Number(valueTwo));
    return sumValue;
  }

  minFunction(params) {
    const minValue = params.values.reduce((valueOne, valueTwo) => Math.min(Number(valueOne), Number(valueTwo)));
    return minValue;
  }

  maxFunction(params) {
    const maxValue = params.values.reduce((valueOne, valueTwo) => Math.max(Number(valueOne), Number(valueTwo)));
    return maxValue;
  }

  countFunction(params) {
    return params.values.length;
  }

  avgFunction(params) {
    const { length } = params.values;
    const sumValue = params.values.reduce((valueOne, valueTwo) => Number(valueOne) + Number(valueTwo));
    return isEmptyOrNil(sumValue) ? sumValue : sumValue / length;
  }

  firstFunction(params) {
    return params.values[0];
  }

  lastFunction(params) {
    return params.values.at(-1);
  }

  uploadEntityIdFormatAction(menu: ViewActionDto) {
    const input = this.renderer.createElement('input');
    this.renderer.appendChild(this.elementRef.nativeElement, input);
    this.renderer.setStyle(input, 'display', 'none');
    this.renderer.setAttribute(input, 'type', 'file');
    this.renderer.listen(input, 'change', (event: any) => {
      this.uploadExcelFile(menu, event.target.files);
    });
    input.click();
    this.renderer.removeChild(this.elementRef.nativeElement, input);
  }

  uploadUserFormatAction(menu: ViewActionDto) {
    const input = this.renderer.createElement('input');
    this.renderer.appendChild(this.elementRef.nativeElement, input);
    this.renderer.setStyle(input, 'display', 'none');
    this.renderer.setAttribute(input, 'type', 'file');
    this.renderer.listen(input, 'change', (event: any) => {
      this.uploadUserFormatExcelFile(menu, event.target.files);
    });
    input.click();
    this.renderer.removeChild(this.elementRef.nativeElement, input);
  }

  uploadUserFormatExcelFile(menu: ViewActionDto, filesList: ArrayLike<File>) {
    const { actionParams } = menu;
    const files = Array.from(filesList);
    files.forEach((file) => {
      if (
        getFileExtension(file.name) === 'xlsx' ||
        getFileExtension(file.name) === 'xlsm' ||
        getFileExtension(file.name) === 'zip'
      ) {
        this.fileService
          .uploadUserFormatExcel$(file, actionParams.moduleId)
          .pipe(debounceTime(3000), take(1), untilDestroyed(this))
          .subscribe(() => {
            const { warningUploadProcessingDialogMeta } = menu;
            if (isEmptyOrNil(warningUploadProcessingDialogMeta)) {
              this.openMessageDialog(
                'Okay',
                'System is processing the upload and will provide the result shortly in your inbox.',
                'information',
              );
            } else {
              const { buttonType, message, type } = warningUploadProcessingDialogMeta;
              this.openMessageDialog(buttonType, message, type);
            }
          });
      } else {
        const { warningOnlyExcelDialogMeta } = menu;
        if (isEmptyOrNil(warningOnlyExcelDialogMeta)) {
          this.openMessageDialog('Okay', 'Please upload only xlsx or xlsm file format.', 'information');
        } else {
          const { buttonType, message, type } = warningOnlyExcelDialogMeta;
          this.openMessageDialog(buttonType, message, type);
        }
      }
    });
  }

  uploadExcelFile(menu: ViewActionDto, filesList: ArrayLike<File>) {
    const { actionParams } = menu;
    const files = Array.from(filesList);
    files.forEach((file) => {
      if (
        getFileExtension(file.name) === 'xlsx' ||
        getFileExtension(file.name) === 'xlsm' ||
        getFileExtension(file.name) === 'zip'
      ) {
        this.fileService
          .uploadEntityIdFormatExcel$(file, actionParams.moduleId)
          .pipe(debounceTime(1000), untilDestroyed(this))
          .subscribe(() => {
            const { warningUploadProcessingDialogMeta } = menu;
            if (isEmptyOrNil(warningUploadProcessingDialogMeta)) {
              this.openMessageDialog(
                'Okay',
                'System is processing the upload and will provide the result shortly in your inbox.',
                'information',
              );
            } else {
              const { buttonType, message, type } = warningUploadProcessingDialogMeta;
              this.openMessageDialog(buttonType, message, type);
            }
          });
      } else {
        const { warningOnlyExcelDialogMeta } = menu;
        if (isEmptyOrNil(warningOnlyExcelDialogMeta)) {
          this.openMessageDialog('Okay', 'Please upload only xlsx or xlsm file format.', 'information');
        } else {
          const { buttonType, message, type } = warningOnlyExcelDialogMeta;
          this.openMessageDialog(buttonType, message, type);
        }
      }
    });
  }

  cpmEndToEndMainColumnDefsChange(viewResult: View) {
    this.viewSubject.next(viewResult);
  }

  handleLoadCpmEndToEndViewData() {
    this.viewName$
      .pipe(
        switchMap((viewName) =>
          this.tabId === 'cpm' && viewName ? this.listingService.getCpmEndToEndView$(viewName) : of(null),
        ),
      )
      .subscribe((view) => this.cpmEndToEndView$.next(view));
  }

  updateCpmIsSubTaskOn(isSubTaskOn: boolean) {
    return this.cpmService.updateCpmIsSubTaskOn(isSubTaskOn).pipe(take(1)).subscribe();
  }
}
