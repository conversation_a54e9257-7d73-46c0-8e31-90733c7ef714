<div class="border flex">
  <div class="icon-wrapper">
    <app-icon svgIcon="lightbulb" class="icon-size-14"> </app-icon>
  </div>

  <div class="content">
    <div>The values for password should match:</div>

    <ul>
      <li *ngFor="let chapter of hintChapters$ | async">
        <div class="flex">
          <div *ngIf="chapter.passed; else notPassedIcon" class="passed-icon">
            <app-icon class="icon-size-16"> done </app-icon>
          </div>
          <span>{{ chapter.message }}</span>
        </div>
        <ul class="sub-ul">
          <li class="flex" *ngFor="let subchapter of chapter.subChapter">
            <div *ngIf="subchapter.passed; else notPassedIcon" class="passed-icon">
              <app-icon class="icon-size-16"> done </app-icon>
            </div>
            <span>{{ subchapter.message }}</span>
          </li>
        </ul>
      </li>
    </ul>
  </div>
</div>

<ng-template #notPassedIcon>
  <div class="dot-icon"></div>
</ng-template>
